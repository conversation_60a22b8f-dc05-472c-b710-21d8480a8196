{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/lib/agents.ts"], "sourcesContent": ["import { Agent, AgentType } from '@/types';\n\nexport const agents: Record<AgentType, Agent> = {\n  technical_analyst: {\n    id: 'technical_analyst',\n    name: 'Dr. <PERSON>',\n    description: 'Especialista em Análise Técnica com PhD em Matemática Financeira',\n    personality: '<PERSON><PERSON><PERSON><PERSON><PERSON>, preciso e metodológico. Fala de forma técnica mas acessível, sempre baseando suas análises em dados concretos e indicadores matemáticos.',\n    specialties: [\n      'Análise de indicadores técnicos (RSI, MACD, Bollinger)',\n      'Padrões de candlestick e formações gráficas',\n      'Níveis de suporte e resistência',\n      'Análise de volume e momentum',\n      'Fibonacci e ondas de Elliott'\n    ],\n    avatar: '👨‍💼',\n    systemPrompt: `Você é Dr. <PERSON>, um analista técnico experiente com PhD em Matemática Financeira. \n\nPERSONALIDADE:\n- Analítico e metodológico\n- Sempre baseia análises em dados concretos\n- Explica conceitos técnicos de forma acessível\n- Usa linguagem profissional mas amigável\n- Gosta de números e estatísticas\n\nESPECIALIDADES:\n- Indicadores técnicos (RSI, MACD, SMA, EMA, Bollinger Bands)\n- Padrões de candlestick e formações gráficas\n- Análise de volume e momentum\n- Níveis de suporte e resistência\n- Fibonacci e análise de ondas\n\nESTILO DE COMUNICAÇÃO:\n- Sempre cumprimente de forma educada\n- Use dados reais e atuais do mercado\n- Explique o \"porquê\" por trás de cada análise\n- Forneça níveis específicos de entrada, stop-loss e take-profit\n- Mencione a confluência de indicadores\n- Use emojis técnicos: 📊 📈 📉 ⚡ 🎯\n\nIMPORTANTE: Sempre busque dados reais e atuais das criptomoedas antes de fazer qualquer análise. Nunca invente números ou dados.`\n  },\n\n  experienced_trader: {\n    id: 'experienced_trader',\n    name: 'Sarah \"The Wolf\" Rodriguez',\n    description: 'Trader veterana com 15 anos de experiência em mercados financeiros',\n    personality: 'Direta, confiante e pragmática. Fala como alguém que já viu de tudo no mercado, com histórias e experiências práticas.',\n    specialties: [\n      'Gestão de risco e money management',\n      'Psicologia do trading',\n      'Estratégias de entrada e saída',\n      'Análise de mercado em tempo real',\n      'Trading de alta frequência'\n    ],\n    avatar: '🐺',\n    systemPrompt: `Você é Sarah \"The Wolf\" Rodriguez, uma trader veterana com 15 anos batalhando nos mercados.\n\nPERSONALIDADE:\n- Direta e sem rodeios\n- Confiante baseada em experiência\n- Pragmática e focada em resultados\n- Conta histórias e experiências do mercado\n- Tem um toque de humor sarcástico\n\nESPECIALIDADES:\n- Gestão de risco e money management\n- Psicologia do trading e controle emocional\n- Estratégias de entrada e saída\n- Leitura de mercado em tempo real\n- Experiência com diferentes ciclos de mercado\n\nESTILO DE COMUNICAÇÃO:\n- Use gírias do trading (\"pump\", \"dump\", \"HODL\", \"diamond hands\")\n- Conte experiências pessoais (fictícias mas realistas)\n- Seja direta sobre riscos e oportunidades\n- Foque em aspectos práticos do trading\n- Use emojis de trader: 🐺 💎 🚀 🔥 💰 ⚡ 📈 📉\n\nFRASES TÍPICAS:\n- \"Já vi essa história antes...\"\n- \"No mercado, quem não tem disciplina, não tem dinheiro\"\n- \"Risk management é tudo, parceiro\"\n- \"O mercado não perdoa amadores\"\n\nIMPORTANTE: Sempre busque dados reais e atuais. Baseie conselhos em experiência prática de trading.`\n  },\n\n  market_consultant: {\n    id: 'market_consultant',\n    name: 'Prof. Elena Nakamura',\n    description: 'Consultora de mercado e professora de Economia Digital',\n    personality: 'Educativa, paciente e abrangente. Gosta de explicar o contexto macro e as tendências de longo prazo.',\n    specialties: [\n      'Análise fundamentalista',\n      'Tendências macroeconômicas',\n      'Regulamentações e políticas',\n      'Adoção institucional',\n      'Tecnologia blockchain'\n    ],\n    avatar: '👩‍🏫',\n    systemPrompt: `Você é Prof. Elena Nakamura, consultora de mercado e professora de Economia Digital.\n\nPERSONALIDADE:\n- Educativa e didática\n- Paciente para explicar conceitos complexos\n- Visão macro e de longo prazo\n- Gosta de contextualizar historicamente\n- Sempre atualizada com notícias e regulamentações\n\nESPECIALIDADES:\n- Análise fundamentalista de criptomoedas\n- Impacto de notícias e regulamentações\n- Tendências macroeconômicas globais\n- Adoção institucional e empresarial\n- Tecnologia blockchain e casos de uso\n\nESTILO DE COMUNICAÇÃO:\n- Explique conceitos de forma didática\n- Contextualize com eventos históricos\n- Mencione notícias recentes relevantes\n- Foque no \"big picture\" do mercado\n- Use emojis educativos: 👩‍🏫 📚 🌍 💡 🔍 📰 🏛️\n\nABORDAGEM:\n- Sempre contextualize a pergunta no cenário macro\n- Explique causas e consequências\n- Mencione fatores fundamentais\n- Discuta tendências de longo prazo\n- Relacione com adoção e regulamentação\n\nIMPORTANTE: Mantenha-se atualizada com notícias recentes do mercado cripto e economia global.`\n  }\n};\n\nexport function getAgent(agentType: AgentType): Agent {\n  return agents[agentType];\n}\n\nexport function getAllAgents(): Agent[] {\n  return Object.values(agents);\n}\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,SAAmC;IAC9C,mBAAmB;QACjB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,aAAa;YACX;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;QACR,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;gIAwB6G,CAAC;IAC/H;IAEA,oBAAoB;QAClB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,aAAa;YACX;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;QACR,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mGA6BgF,CAAC;IAClG;IAEA,mBAAmB;QACjB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,aAAa;YACX;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;QACR,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6FA8B0E,CAAC;IAC5F;AACF;AAEO,SAAS,SAAS,SAAoB;IAC3C,OAAO,MAAM,CAAC,UAAU;AAC1B;AAEO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/lib/crypto-api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { CryptoData, MarketData, TechnicalIndicators, ApiResponse } from '@/types';\n\nconst COINGECKO_BASE_URL = 'https://api.coingecko.com/api/v3';\nconst FEAR_GREED_API = 'https://api.alternative.me/fng/';\n\n// Cache simples para evitar muitas requisições\nconst cache = new Map<string, { data: any; timestamp: number }>();\nconst CACHE_DURATION = 60000; // 1 minuto\n\nfunction getCachedData<T>(key: string): T | null {\n  const cached = cache.get(key);\n  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n    return cached.data as T;\n  }\n  return null;\n}\n\nfunction setCachedData(key: string, data: any): void {\n  cache.set(key, { data, timestamp: Date.now() });\n}\n\nexport class CryptoApiService {\n  \n  /**\n   * Busca dados de uma criptomoeda específica\n   */\n  static async getCryptoData(coinId: string): Promise<ApiResponse<CryptoData>> {\n    try {\n      const cacheKey = `crypto-${coinId}`;\n      const cached = getCachedData<CryptoData>(cacheKey);\n      \n      if (cached) {\n        return {\n          success: true,\n          data: cached,\n          timestamp: new Date()\n        };\n      }\n\n      const response = await axios.get(\n        `${COINGECKO_BASE_URL}/coins/${coinId}`,\n        {\n          params: {\n            localization: false,\n            tickers: false,\n            market_data: true,\n            community_data: false,\n            developer_data: false,\n            sparkline: false\n          }\n        }\n      );\n\n      const data: CryptoData = {\n        id: response.data.id,\n        symbol: response.data.symbol,\n        name: response.data.name,\n        current_price: response.data.market_data.current_price.usd,\n        market_cap: response.data.market_data.market_cap.usd,\n        market_cap_rank: response.data.market_cap_rank,\n        fully_diluted_valuation: response.data.market_data.fully_diluted_valuation?.usd || 0,\n        total_volume: response.data.market_data.total_volume.usd,\n        high_24h: response.data.market_data.high_24h.usd,\n        low_24h: response.data.market_data.low_24h.usd,\n        price_change_24h: response.data.market_data.price_change_24h,\n        price_change_percentage_24h: response.data.market_data.price_change_percentage_24h,\n        market_cap_change_24h: response.data.market_data.market_cap_change_24h,\n        market_cap_change_percentage_24h: response.data.market_data.market_cap_change_percentage_24h,\n        circulating_supply: response.data.market_data.circulating_supply,\n        total_supply: response.data.market_data.total_supply,\n        max_supply: response.data.market_data.max_supply,\n        ath: response.data.market_data.ath.usd,\n        ath_change_percentage: response.data.market_data.ath_change_percentage.usd,\n        ath_date: response.data.market_data.ath_date.usd,\n        atl: response.data.market_data.atl.usd,\n        atl_change_percentage: response.data.market_data.atl_change_percentage.usd,\n        atl_date: response.data.market_data.atl_date.usd,\n        last_updated: response.data.last_updated\n      };\n\n      setCachedData(cacheKey, data);\n\n      return {\n        success: true,\n        data,\n        timestamp: new Date()\n      };\n\n    } catch (error) {\n      console.error('Erro ao buscar dados da crypto:', error);\n      return {\n        success: false,\n        error: 'Erro ao buscar dados da criptomoeda',\n        timestamp: new Date()\n      };\n    }\n  }\n\n  /**\n   * Busca dados de múltiplas criptomoedas\n   */\n  static async getMultipleCryptoData(coinIds: string[]): Promise<ApiResponse<CryptoData[]>> {\n    try {\n      const cacheKey = `multiple-${coinIds.join(',')}`;\n      const cached = getCachedData<CryptoData[]>(cacheKey);\n      \n      if (cached) {\n        return {\n          success: true,\n          data: cached,\n          timestamp: new Date()\n        };\n      }\n\n      const response = await axios.get(\n        `${COINGECKO_BASE_URL}/coins/markets`,\n        {\n          params: {\n            vs_currency: 'usd',\n            ids: coinIds.join(','),\n            order: 'market_cap_desc',\n            per_page: coinIds.length,\n            page: 1,\n            sparkline: false,\n            price_change_percentage: '24h'\n          }\n        }\n      );\n\n      const data: CryptoData[] = response.data.map((coin: any) => ({\n        id: coin.id,\n        symbol: coin.symbol,\n        name: coin.name,\n        current_price: coin.current_price,\n        market_cap: coin.market_cap,\n        market_cap_rank: coin.market_cap_rank,\n        fully_diluted_valuation: coin.fully_diluted_valuation || 0,\n        total_volume: coin.total_volume,\n        high_24h: coin.high_24h,\n        low_24h: coin.low_24h,\n        price_change_24h: coin.price_change_24h,\n        price_change_percentage_24h: coin.price_change_percentage_24h,\n        market_cap_change_24h: coin.market_cap_change_24h,\n        market_cap_change_percentage_24h: coin.market_cap_change_percentage_24h,\n        circulating_supply: coin.circulating_supply,\n        total_supply: coin.total_supply,\n        max_supply: coin.max_supply,\n        ath: coin.ath,\n        ath_change_percentage: coin.ath_change_percentage,\n        ath_date: coin.ath_date,\n        atl: coin.atl,\n        atl_change_percentage: coin.atl_change_percentage,\n        atl_date: coin.atl_date,\n        last_updated: coin.last_updated\n      }));\n\n      setCachedData(cacheKey, data);\n\n      return {\n        success: true,\n        data,\n        timestamp: new Date()\n      };\n\n    } catch (error) {\n      console.error('Erro ao buscar dados de múltiplas cryptos:', error);\n      return {\n        success: false,\n        error: 'Erro ao buscar dados das criptomoedas',\n        timestamp: new Date()\n      };\n    }\n  }\n\n  /**\n   * Busca dados globais do mercado\n   */\n  static async getMarketData(): Promise<ApiResponse<MarketData>> {\n    try {\n      const cacheKey = 'market-data';\n      const cached = getCachedData<MarketData>(cacheKey);\n      \n      if (cached) {\n        return {\n          success: true,\n          data: cached,\n          timestamp: new Date()\n        };\n      }\n\n      const [globalResponse, fearGreedResponse, trendingResponse] = await Promise.all([\n        axios.get(`${COINGECKO_BASE_URL}/global`),\n        axios.get(FEAR_GREED_API),\n        axios.get(`${COINGECKO_BASE_URL}/search/trending`)\n      ]);\n\n      const data: MarketData = {\n        global: globalResponse.data.data,\n        fear_greed_index: fearGreedResponse.data.data[0].value,\n        trending_coins: trendingResponse.data.coins.map((coin: any) => coin.item.id)\n      };\n\n      setCachedData(cacheKey, data);\n\n      return {\n        success: true,\n        data,\n        timestamp: new Date()\n      };\n\n    } catch (error) {\n      console.error('Erro ao buscar dados do mercado:', error);\n      return {\n        success: false,\n        error: 'Erro ao buscar dados do mercado',\n        timestamp: new Date()\n      };\n    }\n  }\n\n  /**\n   * Busca lista de criptomoedas populares\n   */\n  static async getPopularCoins(limit: number = 10): Promise<ApiResponse<CryptoData[]>> {\n    try {\n      const response = await axios.get(\n        `${COINGECKO_BASE_URL}/coins/markets`,\n        {\n          params: {\n            vs_currency: 'usd',\n            order: 'market_cap_desc',\n            per_page: limit,\n            page: 1,\n            sparkline: false,\n            price_change_percentage: '24h'\n          }\n        }\n      );\n\n      const data: CryptoData[] = response.data.map((coin: any) => ({\n        id: coin.id,\n        symbol: coin.symbol,\n        name: coin.name,\n        current_price: coin.current_price,\n        market_cap: coin.market_cap,\n        market_cap_rank: coin.market_cap_rank,\n        fully_diluted_valuation: coin.fully_diluted_valuation || 0,\n        total_volume: coin.total_volume,\n        high_24h: coin.high_24h,\n        low_24h: coin.low_24h,\n        price_change_24h: coin.price_change_24h,\n        price_change_percentage_24h: coin.price_change_percentage_24h,\n        market_cap_change_24h: coin.market_cap_change_24h,\n        market_cap_change_percentage_24h: coin.market_cap_change_percentage_24h,\n        circulating_supply: coin.circulating_supply,\n        total_supply: coin.total_supply,\n        max_supply: coin.max_supply,\n        ath: coin.ath,\n        ath_change_percentage: coin.ath_change_percentage,\n        ath_date: coin.ath_date,\n        atl: coin.atl,\n        atl_change_percentage: coin.atl_change_percentage,\n        atl_date: coin.atl_date,\n        last_updated: coin.last_updated\n      }));\n\n      return {\n        success: true,\n        data,\n        timestamp: new Date()\n      };\n\n    } catch (error) {\n      console.error('Erro ao buscar moedas populares:', error);\n      return {\n        success: false,\n        error: 'Erro ao buscar moedas populares',\n        timestamp: new Date()\n      };\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGA,MAAM,qBAAqB;AAC3B,MAAM,iBAAiB;AAEvB,+CAA+C;AAC/C,MAAM,QAAQ,IAAI;AAClB,MAAM,iBAAiB,OAAO,WAAW;AAEzC,SAAS,cAAiB,GAAW;IACnC,MAAM,SAAS,MAAM,GAAG,CAAC;IACzB,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,gBAAgB;QAC5D,OAAO,OAAO,IAAI;IACpB;IACA,OAAO;AACT;AAEA,SAAS,cAAc,GAAW,EAAE,IAAS;IAC3C,MAAM,GAAG,CAAC,KAAK;QAAE;QAAM,WAAW,KAAK,GAAG;IAAG;AAC/C;AAEO,MAAM;IAEX;;GAEC,GACD,aAAa,cAAc,MAAc,EAAoC;QAC3E,IAAI;YACF,MAAM,WAAW,CAAC,OAAO,EAAE,QAAQ;YACnC,MAAM,SAAS,cAA0B;YAEzC,IAAI,QAAQ;gBACV,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,WAAW,IAAI;gBACjB;YACF;YAEA,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,GAAG,mBAAmB,OAAO,EAAE,QAAQ,EACvC;gBACE,QAAQ;oBACN,cAAc;oBACd,SAAS;oBACT,aAAa;oBACb,gBAAgB;oBAChB,gBAAgB;oBAChB,WAAW;gBACb;YACF;YAGF,MAAM,OAAmB;gBACvB,IAAI,SAAS,IAAI,CAAC,EAAE;gBACpB,QAAQ,SAAS,IAAI,CAAC,MAAM;gBAC5B,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,eAAe,SAAS,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,GAAG;gBAC1D,YAAY,SAAS,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG;gBACpD,iBAAiB,SAAS,IAAI,CAAC,eAAe;gBAC9C,yBAAyB,SAAS,IAAI,CAAC,WAAW,CAAC,uBAAuB,EAAE,OAAO;gBACnF,cAAc,SAAS,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG;gBACxD,UAAU,SAAS,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG;gBAChD,SAAS,SAAS,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG;gBAC9C,kBAAkB,SAAS,IAAI,CAAC,WAAW,CAAC,gBAAgB;gBAC5D,6BAA6B,SAAS,IAAI,CAAC,WAAW,CAAC,2BAA2B;gBAClF,uBAAuB,SAAS,IAAI,CAAC,WAAW,CAAC,qBAAqB;gBACtE,kCAAkC,SAAS,IAAI,CAAC,WAAW,CAAC,gCAAgC;gBAC5F,oBAAoB,SAAS,IAAI,CAAC,WAAW,CAAC,kBAAkB;gBAChE,cAAc,SAAS,IAAI,CAAC,WAAW,CAAC,YAAY;gBACpD,YAAY,SAAS,IAAI,CAAC,WAAW,CAAC,UAAU;gBAChD,KAAK,SAAS,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG;gBACtC,uBAAuB,SAAS,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,GAAG;gBAC1E,UAAU,SAAS,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG;gBAChD,KAAK,SAAS,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG;gBACtC,uBAAuB,SAAS,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,GAAG;gBAC1E,UAAU,SAAS,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG;gBAChD,cAAc,SAAS,IAAI,CAAC,YAAY;YAC1C;YAEA,cAAc,UAAU;YAExB,OAAO;gBACL,SAAS;gBACT;gBACA,WAAW,IAAI;YACjB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,WAAW,IAAI;YACjB;QACF;IACF;IAEA;;GAEC,GACD,aAAa,sBAAsB,OAAiB,EAAsC;QACxF,IAAI;YACF,MAAM,WAAW,CAAC,SAAS,EAAE,QAAQ,IAAI,CAAC,MAAM;YAChD,MAAM,SAAS,cAA4B;YAE3C,IAAI,QAAQ;gBACV,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,WAAW,IAAI;gBACjB;YACF;YAEA,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,GAAG,mBAAmB,cAAc,CAAC,EACrC;gBACE,QAAQ;oBACN,aAAa;oBACb,KAAK,QAAQ,IAAI,CAAC;oBAClB,OAAO;oBACP,UAAU,QAAQ,MAAM;oBACxB,MAAM;oBACN,WAAW;oBACX,yBAAyB;gBAC3B;YACF;YAGF,MAAM,OAAqB,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC3D,IAAI,KAAK,EAAE;oBACX,QAAQ,KAAK,MAAM;oBACnB,MAAM,KAAK,IAAI;oBACf,eAAe,KAAK,aAAa;oBACjC,YAAY,KAAK,UAAU;oBAC3B,iBAAiB,KAAK,eAAe;oBACrC,yBAAyB,KAAK,uBAAuB,IAAI;oBACzD,cAAc,KAAK,YAAY;oBAC/B,UAAU,KAAK,QAAQ;oBACvB,SAAS,KAAK,OAAO;oBACrB,kBAAkB,KAAK,gBAAgB;oBACvC,6BAA6B,KAAK,2BAA2B;oBAC7D,uBAAuB,KAAK,qBAAqB;oBACjD,kCAAkC,KAAK,gCAAgC;oBACvE,oBAAoB,KAAK,kBAAkB;oBAC3C,cAAc,KAAK,YAAY;oBAC/B,YAAY,KAAK,UAAU;oBAC3B,KAAK,KAAK,GAAG;oBACb,uBAAuB,KAAK,qBAAqB;oBACjD,UAAU,KAAK,QAAQ;oBACvB,KAAK,KAAK,GAAG;oBACb,uBAAuB,KAAK,qBAAqB;oBACjD,UAAU,KAAK,QAAQ;oBACvB,cAAc,KAAK,YAAY;gBACjC,CAAC;YAED,cAAc,UAAU;YAExB,OAAO;gBACL,SAAS;gBACT;gBACA,WAAW,IAAI;YACjB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,WAAW,IAAI;YACjB;QACF;IACF;IAEA;;GAEC,GACD,aAAa,gBAAkD;QAC7D,IAAI;YACF,MAAM,WAAW;YACjB,MAAM,SAAS,cAA0B;YAEzC,IAAI,QAAQ;gBACV,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,WAAW,IAAI;gBACjB;YACF;YAEA,MAAM,CAAC,gBAAgB,mBAAmB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC9E,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,mBAAmB,OAAO,CAAC;gBACxC,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC;gBACV,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,mBAAmB,gBAAgB,CAAC;aAClD;YAED,MAAM,OAAmB;gBACvB,QAAQ,eAAe,IAAI,CAAC,IAAI;gBAChC,kBAAkB,kBAAkB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK;gBACtD,gBAAgB,iBAAiB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAc,KAAK,IAAI,CAAC,EAAE;YAC7E;YAEA,cAAc,UAAU;YAExB,OAAO;gBACL,SAAS;gBACT;gBACA,WAAW,IAAI;YACjB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,WAAW,IAAI;YACjB;QACF;IACF;IAEA;;GAEC,GACD,aAAa,gBAAgB,QAAgB,EAAE,EAAsC;QACnF,IAAI;YACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,GAAG,mBAAmB,cAAc,CAAC,EACrC;gBACE,QAAQ;oBACN,aAAa;oBACb,OAAO;oBACP,UAAU;oBACV,MAAM;oBACN,WAAW;oBACX,yBAAyB;gBAC3B;YACF;YAGF,MAAM,OAAqB,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC3D,IAAI,KAAK,EAAE;oBACX,QAAQ,KAAK,MAAM;oBACnB,MAAM,KAAK,IAAI;oBACf,eAAe,KAAK,aAAa;oBACjC,YAAY,KAAK,UAAU;oBAC3B,iBAAiB,KAAK,eAAe;oBACrC,yBAAyB,KAAK,uBAAuB,IAAI;oBACzD,cAAc,KAAK,YAAY;oBAC/B,UAAU,KAAK,QAAQ;oBACvB,SAAS,KAAK,OAAO;oBACrB,kBAAkB,KAAK,gBAAgB;oBACvC,6BAA6B,KAAK,2BAA2B;oBAC7D,uBAAuB,KAAK,qBAAqB;oBACjD,kCAAkC,KAAK,gCAAgC;oBACvE,oBAAoB,KAAK,kBAAkB;oBAC3C,cAAc,KAAK,YAAY;oBAC/B,YAAY,KAAK,UAAU;oBAC3B,KAAK,KAAK,GAAG;oBACb,uBAAuB,KAAK,qBAAqB;oBACjD,UAAU,KAAK,QAAQ;oBACvB,KAAK,KAAK,GAAG;oBACb,uBAAuB,KAAK,qBAAqB;oBACjD,UAAU,KAAK,QAAQ;oBACvB,cAAc,KAAK,YAAY;gBACjC,CAAC;YAED,OAAO;gBACL,SAAS;gBACT;gBACA,WAAW,IAAI;YACjB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,WAAW,IAAI;YACjB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 563, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/lib/technical-analysis.ts"], "sourcesContent": ["import { CryptoData, TechnicalIndicators, TradingSignal } from '@/types';\n\nexport class TechnicalAnalysisService {\n  \n  /**\n   * Calcula RSI (Relative Strength Index)\n   */\n  static calculateRSI(prices: number[], period: number = 14): number {\n    if (prices.length < period + 1) return 50; // Valor neutro se não há dados suficientes\n    \n    let gains = 0;\n    let losses = 0;\n    \n    // Calcula ganhos e perdas iniciais\n    for (let i = 1; i <= period; i++) {\n      const change = prices[i] - prices[i - 1];\n      if (change > 0) {\n        gains += change;\n      } else {\n        losses += Math.abs(change);\n      }\n    }\n    \n    let avgGain = gains / period;\n    let avgLoss = losses / period;\n    \n    // Calcula RSI para o resto dos dados\n    for (let i = period + 1; i < prices.length; i++) {\n      const change = prices[i] - prices[i - 1];\n      const gain = change > 0 ? change : 0;\n      const loss = change < 0 ? Math.abs(change) : 0;\n      \n      avgGain = (avgGain * (period - 1) + gain) / period;\n      avgLoss = (avgLoss * (period - 1) + loss) / period;\n    }\n    \n    if (avgLoss === 0) return 100;\n    const rs = avgGain / avgLoss;\n    return 100 - (100 / (1 + rs));\n  }\n\n  /**\n   * Calcula SMA (Simple Moving Average)\n   */\n  static calculateSMA(prices: number[], period: number): number {\n    if (prices.length < period) return prices[prices.length - 1] || 0;\n    \n    const slice = prices.slice(-period);\n    return slice.reduce((sum, price) => sum + price, 0) / period;\n  }\n\n  /**\n   * Calcula EMA (Exponential Moving Average)\n   */\n  static calculateEMA(prices: number[], period: number): number {\n    if (prices.length === 0) return 0;\n    if (prices.length === 1) return prices[0];\n    \n    const multiplier = 2 / (period + 1);\n    let ema = prices[0];\n    \n    for (let i = 1; i < prices.length; i++) {\n      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));\n    }\n    \n    return ema;\n  }\n\n  /**\n   * Calcula Bollinger Bands\n   */\n  static calculateBollingerBands(prices: number[], period: number = 20, stdDev: number = 2) {\n    const sma = this.calculateSMA(prices, period);\n    \n    if (prices.length < period) {\n      return {\n        upper: sma * 1.02,\n        middle: sma,\n        lower: sma * 0.98\n      };\n    }\n    \n    const slice = prices.slice(-period);\n    const variance = slice.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period;\n    const standardDeviation = Math.sqrt(variance);\n    \n    return {\n      upper: sma + (standardDeviation * stdDev),\n      middle: sma,\n      lower: sma - (standardDeviation * stdDev)\n    };\n  }\n\n  /**\n   * Calcula MACD\n   */\n  static calculateMACD(prices: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {\n    const emaFast = this.calculateEMA(prices, fastPeriod);\n    const emaSlow = this.calculateEMA(prices, slowPeriod);\n    const macdLine = emaFast - emaSlow;\n    \n    // Para simplificar, usamos uma aproximação do sinal\n    const signalLine = macdLine * 0.9; // Aproximação\n    const histogram = macdLine - signalLine;\n    \n    return {\n      macd: macdLine,\n      signal: signalLine,\n      histogram: histogram\n    };\n  }\n\n  /**\n   * Identifica níveis de suporte e resistência\n   */\n  static findSupportResistance(prices: number[], currentPrice: number) {\n    const sortedPrices = [...prices].sort((a, b) => a - b);\n    const priceRanges = [];\n    \n    // Agrupa preços em ranges\n    for (let i = 0; i < sortedPrices.length; i += Math.floor(sortedPrices.length / 10)) {\n      const range = sortedPrices.slice(i, i + Math.floor(sortedPrices.length / 10));\n      if (range.length > 0) {\n        priceRanges.push(range.reduce((sum, p) => sum + p, 0) / range.length);\n      }\n    }\n    \n    const support = priceRanges.filter(price => price < currentPrice).slice(-3);\n    const resistance = priceRanges.filter(price => price > currentPrice).slice(0, 3);\n    \n    return {\n      support: support.length > 0 ? support : [currentPrice * 0.95],\n      resistance: resistance.length > 0 ? resistance : [currentPrice * 1.05]\n    };\n  }\n\n  /**\n   * Gera análise técnica completa\n   */\n  static generateTechnicalAnalysis(cryptoData: CryptoData, historicalPrices?: number[]): TechnicalIndicators {\n    // Se não temos dados históricos, simulamos baseado nos dados atuais\n    const prices = historicalPrices || this.generateMockPrices(cryptoData);\n    \n    const rsi = this.calculateRSI(prices);\n    const macd = this.calculateMACD(prices);\n    const sma = {\n      sma20: this.calculateSMA(prices, 20),\n      sma50: this.calculateSMA(prices, 50),\n      sma200: this.calculateSMA(prices, 200)\n    };\n    const ema = {\n      ema12: this.calculateEMA(prices, 12),\n      ema26: this.calculateEMA(prices, 26)\n    };\n    const bollinger = this.calculateBollingerBands(prices);\n    const supportResistance = this.findSupportResistance(prices, cryptoData.current_price);\n    \n    return {\n      rsi,\n      macd,\n      sma,\n      ema,\n      bollinger,\n      volume_profile: cryptoData.total_volume / cryptoData.market_cap,\n      support_resistance: supportResistance\n    };\n  }\n\n  /**\n   * Gera sinal de trading baseado na análise técnica\n   */\n  static generateTradingSignal(cryptoData: CryptoData, technicalIndicators: TechnicalIndicators): TradingSignal {\n    let bullishSignals = 0;\n    let bearishSignals = 0;\n    let totalSignals = 0;\n    \n    const currentPrice = cryptoData.current_price;\n    const { rsi, macd, sma, ema, bollinger } = technicalIndicators;\n    \n    // Análise RSI\n    totalSignals++;\n    if (rsi < 30) bullishSignals++; // Oversold\n    else if (rsi > 70) bearishSignals++; // Overbought\n    \n    // Análise MACD\n    totalSignals++;\n    if (macd.macd > macd.signal && macd.histogram > 0) bullishSignals++;\n    else if (macd.macd < macd.signal && macd.histogram < 0) bearishSignals++;\n    \n    // Análise SMA\n    totalSignals++;\n    if (currentPrice > sma.sma20 && sma.sma20 > sma.sma50) bullishSignals++;\n    else if (currentPrice < sma.sma20 && sma.sma20 < sma.sma50) bearishSignals++;\n    \n    // Análise EMA\n    totalSignals++;\n    if (ema.ema12 > ema.ema26) bullishSignals++;\n    else bearishSignals++;\n    \n    // Análise Bollinger\n    totalSignals++;\n    if (currentPrice < bollinger.lower) bullishSignals++; // Oversold\n    else if (currentPrice > bollinger.upper) bearishSignals++; // Overbought\n    \n    // Análise de tendência de preço\n    totalSignals++;\n    if (cryptoData.price_change_percentage_24h > 5) bullishSignals++;\n    else if (cryptoData.price_change_percentage_24h < -5) bearishSignals++;\n    \n    const bullishPercentage = (bullishSignals / totalSignals) * 100;\n    const bearishPercentage = (bearishSignals / totalSignals) * 100;\n    \n    let type: 'BUY' | 'SELL' | 'HOLD';\n    let strength: 'WEAK' | 'MODERATE' | 'STRONG';\n    let confidence: number;\n    \n    if (bullishPercentage >= 60) {\n      type = 'BUY';\n      strength = bullishPercentage >= 80 ? 'STRONG' : 'MODERATE';\n      confidence = bullishPercentage;\n    } else if (bearishPercentage >= 60) {\n      type = 'SELL';\n      strength = bearishPercentage >= 80 ? 'STRONG' : 'MODERATE';\n      confidence = bearishPercentage;\n    } else {\n      type = 'HOLD';\n      strength = 'WEAK';\n      confidence = Math.max(bullishPercentage, bearishPercentage);\n    }\n    \n    // Calcula targets\n    const volatility = Math.abs(cryptoData.price_change_percentage_24h) / 100;\n    const priceTarget = type === 'BUY' \n      ? currentPrice * (1 + volatility * 2)\n      : currentPrice * (1 - volatility * 2);\n    \n    const stopLoss = type === 'BUY'\n      ? currentPrice * 0.95\n      : currentPrice * 1.05;\n    \n    return {\n      type,\n      strength,\n      confidence: Math.round(confidence),\n      price_target: Math.round(priceTarget * 100) / 100,\n      stop_loss: Math.round(stopLoss * 100) / 100,\n      timeframe: '24h',\n      reasoning: this.generateReasoning(type, strength, technicalIndicators, cryptoData)\n    };\n  }\n\n  /**\n   * Gera explicação do sinal\n   */\n  private static generateReasoning(\n    type: 'BUY' | 'SELL' | 'HOLD',\n    strength: 'WEAK' | 'MODERATE' | 'STRONG',\n    indicators: TechnicalIndicators,\n    cryptoData: CryptoData\n  ): string {\n    const reasons = [];\n    \n    if (indicators.rsi < 30) reasons.push('RSI indica sobrevenda');\n    if (indicators.rsi > 70) reasons.push('RSI indica sobrecompra');\n    if (indicators.macd.macd > indicators.macd.signal) reasons.push('MACD bullish');\n    if (indicators.macd.macd < indicators.macd.signal) reasons.push('MACD bearish');\n    if (cryptoData.current_price > indicators.sma.sma20) reasons.push('Preço acima da SMA20');\n    if (cryptoData.current_price < indicators.sma.sma20) reasons.push('Preço abaixo da SMA20');\n    \n    return reasons.join(', ') || 'Análise baseada em múltiplos indicadores';\n  }\n\n  /**\n   * Gera preços históricos simulados baseados nos dados atuais\n   */\n  private static generateMockPrices(cryptoData: CryptoData): number[] {\n    const prices = [];\n    const currentPrice = cryptoData.current_price;\n    const volatility = Math.abs(cryptoData.price_change_percentage_24h) / 100;\n    \n    // Gera 200 pontos de preço simulados\n    for (let i = 0; i < 200; i++) {\n      const randomChange = (Math.random() - 0.5) * volatility * 2;\n      const price = currentPrice * (1 + randomChange);\n      prices.push(Math.max(price, currentPrice * 0.5)); // Evita preços muito baixos\n    }\n    \n    // Adiciona tendência baseada na mudança de 24h\n    const trend = cryptoData.price_change_percentage_24h / 100;\n    for (let i = 0; i < prices.length; i++) {\n      const trendFactor = (i / prices.length) * trend;\n      prices[i] *= (1 + trendFactor);\n    }\n    \n    return prices;\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IAEX;;GAEC,GACD,OAAO,aAAa,MAAgB,EAAE,SAAiB,EAAE,EAAU;QACjE,IAAI,OAAO,MAAM,GAAG,SAAS,GAAG,OAAO,IAAI,2CAA2C;QAEtF,IAAI,QAAQ;QACZ,IAAI,SAAS;QAEb,mCAAmC;QACnC,IAAK,IAAI,IAAI,GAAG,KAAK,QAAQ,IAAK;YAChC,MAAM,SAAS,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE;YACxC,IAAI,SAAS,GAAG;gBACd,SAAS;YACX,OAAO;gBACL,UAAU,KAAK,GAAG,CAAC;YACrB;QACF;QAEA,IAAI,UAAU,QAAQ;QACtB,IAAI,UAAU,SAAS;QAEvB,qCAAqC;QACrC,IAAK,IAAI,IAAI,SAAS,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YAC/C,MAAM,SAAS,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE;YACxC,MAAM,OAAO,SAAS,IAAI,SAAS;YACnC,MAAM,OAAO,SAAS,IAAI,KAAK,GAAG,CAAC,UAAU;YAE7C,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI;YAC5C,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI;QAC9C;QAEA,IAAI,YAAY,GAAG,OAAO;QAC1B,MAAM,KAAK,UAAU;QACrB,OAAO,MAAO,MAAM,CAAC,IAAI,EAAE;IAC7B;IAEA;;GAEC,GACD,OAAO,aAAa,MAAgB,EAAE,MAAc,EAAU;QAC5D,IAAI,OAAO,MAAM,GAAG,QAAQ,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,IAAI;QAEhE,MAAM,QAAQ,OAAO,KAAK,CAAC,CAAC;QAC5B,OAAO,MAAM,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,OAAO,KAAK;IACxD;IAEA;;GAEC,GACD,OAAO,aAAa,MAAgB,EAAE,MAAc,EAAU;QAC5D,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;QAChC,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO,MAAM,CAAC,EAAE;QAEzC,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC;QAClC,IAAI,MAAM,MAAM,CAAC,EAAE;QAEnB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,MAAM,AAAC,MAAM,CAAC,EAAE,GAAG,aAAe,MAAM,CAAC,IAAI,UAAU;QACzD;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,wBAAwB,MAAgB,EAAE,SAAiB,EAAE,EAAE,SAAiB,CAAC,EAAE;QACxF,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ;QAEtC,IAAI,OAAO,MAAM,GAAG,QAAQ;YAC1B,OAAO;gBACL,OAAO,MAAM;gBACb,QAAQ;gBACR,OAAO,MAAM;YACf;QACF;QAEA,MAAM,QAAQ,OAAO,KAAK,CAAC,CAAC;QAC5B,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,KAAK,GAAG,CAAC,QAAQ,KAAK,IAAI,KAAK;QACnF,MAAM,oBAAoB,KAAK,IAAI,CAAC;QAEpC,OAAO;YACL,OAAO,MAAO,oBAAoB;YAClC,QAAQ;YACR,OAAO,MAAO,oBAAoB;QACpC;IACF;IAEA;;GAEC,GACD,OAAO,cAAc,MAAgB,EAAE,aAAqB,EAAE,EAAE,aAAqB,EAAE,EAAE,eAAuB,CAAC,EAAE;QACjH,MAAM,UAAU,IAAI,CAAC,YAAY,CAAC,QAAQ;QAC1C,MAAM,UAAU,IAAI,CAAC,YAAY,CAAC,QAAQ;QAC1C,MAAM,WAAW,UAAU;QAE3B,oDAAoD;QACpD,MAAM,aAAa,WAAW,KAAK,cAAc;QACjD,MAAM,YAAY,WAAW;QAE7B,OAAO;YACL,MAAM;YACN,QAAQ;YACR,WAAW;QACb;IACF;IAEA;;GAEC,GACD,OAAO,sBAAsB,MAAgB,EAAE,YAAoB,EAAE;QACnE,MAAM,eAAe;eAAI;SAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QACpD,MAAM,cAAc,EAAE;QAEtB,0BAA0B;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,KAAK,KAAK,KAAK,CAAC,aAAa,MAAM,GAAG,IAAK;YAClF,MAAM,QAAQ,aAAa,KAAK,CAAC,GAAG,IAAI,KAAK,KAAK,CAAC,aAAa,MAAM,GAAG;YACzE,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,YAAY,IAAI,CAAC,MAAM,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,GAAG,KAAK,MAAM,MAAM;YACtE;QACF;QAEA,MAAM,UAAU,YAAY,MAAM,CAAC,CAAA,QAAS,QAAQ,cAAc,KAAK,CAAC,CAAC;QACzE,MAAM,aAAa,YAAY,MAAM,CAAC,CAAA,QAAS,QAAQ,cAAc,KAAK,CAAC,GAAG;QAE9E,OAAO;YACL,SAAS,QAAQ,MAAM,GAAG,IAAI,UAAU;gBAAC,eAAe;aAAK;YAC7D,YAAY,WAAW,MAAM,GAAG,IAAI,aAAa;gBAAC,eAAe;aAAK;QACxE;IACF;IAEA;;GAEC,GACD,OAAO,0BAA0B,UAAsB,EAAE,gBAA2B,EAAuB;QACzG,oEAAoE;QACpE,MAAM,SAAS,oBAAoB,IAAI,CAAC,kBAAkB,CAAC;QAE3D,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC;QAC9B,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC;QAChC,MAAM,MAAM;YACV,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;YACjC,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;YACjC,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ;QACpC;QACA,MAAM,MAAM;YACV,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;YACjC,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;QACnC;QACA,MAAM,YAAY,IAAI,CAAC,uBAAuB,CAAC;QAC/C,MAAM,oBAAoB,IAAI,CAAC,qBAAqB,CAAC,QAAQ,WAAW,aAAa;QAErF,OAAO;YACL;YACA;YACA;YACA;YACA;YACA,gBAAgB,WAAW,YAAY,GAAG,WAAW,UAAU;YAC/D,oBAAoB;QACtB;IACF;IAEA;;GAEC,GACD,OAAO,sBAAsB,UAAsB,EAAE,mBAAwC,EAAiB;QAC5G,IAAI,iBAAiB;QACrB,IAAI,iBAAiB;QACrB,IAAI,eAAe;QAEnB,MAAM,eAAe,WAAW,aAAa;QAC7C,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG;QAE3C,cAAc;QACd;QACA,IAAI,MAAM,IAAI,kBAAkB,WAAW;aACtC,IAAI,MAAM,IAAI,kBAAkB,aAAa;QAElD,eAAe;QACf;QACA,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,IAAI,KAAK,SAAS,GAAG,GAAG;aAC9C,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,IAAI,KAAK,SAAS,GAAG,GAAG;QAExD,cAAc;QACd;QACA,IAAI,eAAe,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;aAClD,IAAI,eAAe,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;QAE5D,cAAc;QACd;QACA,IAAI,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;aACtB;QAEL,oBAAoB;QACpB;QACA,IAAI,eAAe,UAAU,KAAK,EAAE,kBAAkB,WAAW;aAC5D,IAAI,eAAe,UAAU,KAAK,EAAE,kBAAkB,aAAa;QAExE,gCAAgC;QAChC;QACA,IAAI,WAAW,2BAA2B,GAAG,GAAG;aAC3C,IAAI,WAAW,2BAA2B,GAAG,CAAC,GAAG;QAEtD,MAAM,oBAAoB,AAAC,iBAAiB,eAAgB;QAC5D,MAAM,oBAAoB,AAAC,iBAAiB,eAAgB;QAE5D,IAAI;QACJ,IAAI;QACJ,IAAI;QAEJ,IAAI,qBAAqB,IAAI;YAC3B,OAAO;YACP,WAAW,qBAAqB,KAAK,WAAW;YAChD,aAAa;QACf,OAAO,IAAI,qBAAqB,IAAI;YAClC,OAAO;YACP,WAAW,qBAAqB,KAAK,WAAW;YAChD,aAAa;QACf,OAAO;YACL,OAAO;YACP,WAAW;YACX,aAAa,KAAK,GAAG,CAAC,mBAAmB;QAC3C;QAEA,kBAAkB;QAClB,MAAM,aAAa,KAAK,GAAG,CAAC,WAAW,2BAA2B,IAAI;QACtE,MAAM,cAAc,SAAS,QACzB,eAAe,CAAC,IAAI,aAAa,CAAC,IAClC,eAAe,CAAC,IAAI,aAAa,CAAC;QAEtC,MAAM,WAAW,SAAS,QACtB,eAAe,OACf,eAAe;QAEnB,OAAO;YACL;YACA;YACA,YAAY,KAAK,KAAK,CAAC;YACvB,cAAc,KAAK,KAAK,CAAC,cAAc,OAAO;YAC9C,WAAW,KAAK,KAAK,CAAC,WAAW,OAAO;YACxC,WAAW;YACX,WAAW,IAAI,CAAC,iBAAiB,CAAC,MAAM,UAAU,qBAAqB;QACzE;IACF;IAEA;;GAEC,GACD,OAAe,kBACb,IAA6B,EAC7B,QAAwC,EACxC,UAA+B,EAC/B,UAAsB,EACd;QACR,MAAM,UAAU,EAAE;QAElB,IAAI,WAAW,GAAG,GAAG,IAAI,QAAQ,IAAI,CAAC;QACtC,IAAI,WAAW,GAAG,GAAG,IAAI,QAAQ,IAAI,CAAC;QACtC,IAAI,WAAW,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC;QAChE,IAAI,WAAW,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC;QAChE,IAAI,WAAW,aAAa,GAAG,WAAW,GAAG,CAAC,KAAK,EAAE,QAAQ,IAAI,CAAC;QAClE,IAAI,WAAW,aAAa,GAAG,WAAW,GAAG,CAAC,KAAK,EAAE,QAAQ,IAAI,CAAC;QAElE,OAAO,QAAQ,IAAI,CAAC,SAAS;IAC/B;IAEA;;GAEC,GACD,OAAe,mBAAmB,UAAsB,EAAY;QAClE,MAAM,SAAS,EAAE;QACjB,MAAM,eAAe,WAAW,aAAa;QAC7C,MAAM,aAAa,KAAK,GAAG,CAAC,WAAW,2BAA2B,IAAI;QAEtE,qCAAqC;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,MAAM,eAAe,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;YAC1D,MAAM,QAAQ,eAAe,CAAC,IAAI,YAAY;YAC9C,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,eAAe,OAAO,4BAA4B;QAChF;QAEA,+CAA+C;QAC/C,MAAM,QAAQ,WAAW,2BAA2B,GAAG;QACvD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,MAAM,cAAc,AAAC,IAAI,OAAO,MAAM,GAAI;YAC1C,MAAM,CAAC,EAAE,IAAK,IAAI;QACpB;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 806, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/lib/intent-classifier.ts"], "sourcesContent": ["import { Message, AgentType } from '@/types';\n\nexport type IntentType = \n  | 'AGENT_INFO'           // Perguntas sobre o agente (horário, funcionamento, etc.)\n  | 'CRYPTO_ANALYSIS'      // Análise de criptomoedas específicas\n  | 'MARKET_GENERAL'       // Perguntas gerais sobre mercado\n  | 'GREETING'             // Saudações\n  | 'PERSONAL_CHAT'        // Conversa pessoal/casual\n  | 'HELP'                 // Pedidos de ajuda\n  | 'TRADING_ADVICE'       // Conselhos de trading\n  | 'COMPARISON'           // Comparação entre cryptos\n  | 'PRICE_PREDICTION'     // Previsões de preço\n  | 'UNKNOWN';             // Não identificado\n\nexport interface IntentClassification {\n  intent: IntentType;\n  confidence: number;\n  entities: string[];\n  context: string;\n}\n\nexport class IntentClassifier {\n  \n  /**\n   * Classifica a intenção de uma mensagem do usuário\n   */\n  static classifyIntent(message: string, conversationHistory: Message[] = []): IntentClassification {\n    const normalizedMessage = message.toLowerCase().trim();\n    \n    // Padrões para cada tipo de intenção\n    const patterns = {\n      AGENT_INFO: [\n        /\\b(você|voce)\\s+(trabalha|funciona|está|esta|fica)\\s+(24\\s*horas?|sempre|disponível|disponivel|online)\\b/i,\n        /\\b(horário|horario|funcionamento|disponibilidade)\\b/i,\n        /\\b(você|voce)\\s+(é|eh|sou|está|esta)\\s+(um\\s+)?(bot|robô|robo|ia|inteligência|inteligencia)\\b/i,\n        /\\b(quem\\s+)?(você|voce)\\s+(é|eh|sou)\\b/i,\n        /\\b(como\\s+)?(você|voce)\\s+(funciona|trabalha)\\b/i,\n        /\\b(você|voce)\\s+(pode|consegue|sabe)\\b/i,\n        /\\b(suas\\s+)?(especialidades|habilidades|capacidades)\\b/i,\n        /\\b(sobre\\s+)?(você|voce|agente|assistente)\\b/i\n      ],\n      \n      GREETING: [\n        /^(oi|olá|ola|hey|hi|hello|bom\\s+dia|boa\\s+tarde|boa\\s+noite|e\\s+aí|eai|como\\s+vai|tudo\\s+bem|beleza)\\b/i,\n        /^(tchau|bye|até\\s+logo|falou|obrigado|obrigada|valeu)\\b/i\n      ],\n      \n      HELP: [\n        /\\b(ajuda|help|socorro|como\\s+usar|como\\s+funciona|tutorial|guia)\\b/i,\n        /\\b(não\\s+entendi|nao\\s+entendi|explique|como\\s+faço|como\\s+faco)\\b/i,\n        /\\b(comandos|opções|opcoes|funcionalidades)\\b/i\n      ],\n      \n      CRYPTO_ANALYSIS: [\n        /\\b(bitcoin|btc|ethereum|eth|cardano|ada|solana|sol|polkadot|dot|chainlink|link|litecoin|ltc|dogecoin|doge|shiba|shib|avalanche|avax|matic|polygon|bnb|binance|xrp|ripple|atom|cosmos|algo|algorand)\\b/i,\n        /\\b(analise|análise|analyze)\\s+(o|a|do|da)\\s+\\w+/i,\n        /\\b(como\\s+está|como\\s+esta)\\s+(o|a)\\s+\\w+/i,\n        /\\b(preço|preco|cotação|cotacao|valor)\\s+(do|da|de)\\b/i\n      ],\n      \n      TRADING_ADVICE: [\n        /\\b(devo\\s+)?(comprar|vender|investir|apostar)\\b/i,\n        /\\b(é\\s+hora|eh\\s+hora|momento|timing)\\s+(de|para)\\s+(comprar|vender)\\b/i,\n        /\\b(estratégia|estrategia|tática|tatica|plano)\\b/i,\n        /\\b(stop\\s*loss|take\\s*profit|entrada|saída|saida)\\b/i,\n        /\\b(gestão\\s+de\\s+risco|gestao\\s+de\\s+risco|money\\s+management)\\b/i,\n        /\\b(portfolio|carteira|diversificar)\\b/i\n      ],\n      \n      MARKET_GENERAL: [\n        /\\b(mercado|market)\\s+(cripto|crypto|criptomoedas|cryptocurrency)\\b/i,\n        /\\b(como\\s+está|como\\s+esta)\\s+(o\\s+)?mercado\\b/i,\n        /\\b(tendência|tendencia|trend|sentimento)\\s+(do\\s+)?mercado\\b/i,\n        /\\b(bull\\s+market|bear\\s+market|alta|baixa|correção|correcao)\\b/i,\n        /\\b(fear\\s+and\\s+greed|medo\\s+e\\s+ganância|medo\\s+e\\s+ganancia)\\b/i\n      ],\n      \n      COMPARISON: [\n        /\\b(melhor|pior)\\s+(que|do\\s+que)\\b/i,\n        /\\b(comparar|comparação|comparacao|versus|vs|x)\\b/i,\n        /\\b(diferença|diferenca|entre)\\b/i,\n        /\\b(qual\\s+)?(é\\s+melhor|eh\\s+melhor|escolher)\\b/i\n      ],\n      \n      PRICE_PREDICTION: [\n        /\\b(previsão|previsao|prediction|vai\\s+subir|vai\\s+descer)\\b/i,\n        /\\b(futuro|amanhã|amanha|próxima|proxima|semana|mês|mes|ano)\\b/i,\n        /\\b(target|meta|objetivo|chegará|chegara)\\b/i,\n        /\\b(quando|até\\s+quando|ate\\s+quando)\\b/i\n      ],\n      \n      PERSONAL_CHAT: [\n        /\\b(como\\s+você\\s+está|como\\s+voce\\s+esta|tudo\\s+bem\\s+com\\s+você|tudo\\s+bem\\s+com\\s+voce)\\b/i,\n        /\\b(gosta|prefere|acha|pensa|opinião|opiniao)\\b/i,\n        /\\b(experiência|experiencia|história|historia|vida)\\b/i,\n        /\\b(família|familia|amigos|hobby|tempo\\s+livre)\\b/i\n      ]\n    };\n    \n    // Detecta entidades (nomes de criptomoedas)\n    const cryptoEntities = this.extractCryptoEntities(normalizedMessage);\n    \n    // Calcula scores para cada intenção\n    const scores: Record<IntentType, number> = {\n      AGENT_INFO: 0,\n      CRYPTO_ANALYSIS: 0,\n      MARKET_GENERAL: 0,\n      GREETING: 0,\n      PERSONAL_CHAT: 0,\n      HELP: 0,\n      TRADING_ADVICE: 0,\n      COMPARISON: 0,\n      PRICE_PREDICTION: 0,\n      UNKNOWN: 0\n    };\n    \n    // Calcula scores baseado nos padrões\n    for (const [intent, patternList] of Object.entries(patterns)) {\n      for (const pattern of patternList) {\n        if (pattern.test(normalizedMessage)) {\n          scores[intent as IntentType] += 1;\n        }\n      }\n    }\n    \n    // Boost para crypto analysis se entidades de crypto foram detectadas\n    if (cryptoEntities.length > 0) {\n      scores.CRYPTO_ANALYSIS += cryptoEntities.length * 2;\n    }\n    \n    // Considera contexto da conversa\n    const contextBoost = this.analyzeConversationContext(conversationHistory);\n    for (const [intent, boost] of Object.entries(contextBoost)) {\n      scores[intent as IntentType] += boost;\n    }\n    \n    // Encontra a intenção com maior score\n    const maxScore = Math.max(...Object.values(scores));\n    const bestIntent = Object.entries(scores).find(([_, score]) => score === maxScore)?.[0] as IntentType;\n    \n    // Se nenhum padrão foi encontrado, classifica como UNKNOWN\n    const finalIntent = maxScore > 0 ? bestIntent : 'UNKNOWN';\n    const confidence = maxScore > 0 ? Math.min(maxScore / 3, 1) : 0;\n    \n    return {\n      intent: finalIntent,\n      confidence,\n      entities: cryptoEntities,\n      context: this.generateContextSummary(normalizedMessage, conversationHistory)\n    };\n  }\n  \n  /**\n   * Extrai entidades de criptomoedas da mensagem\n   */\n  private static extractCryptoEntities(message: string): string[] {\n    const cryptoPatterns = [\n      { pattern: /\\bbitcoin\\b/i, entity: 'bitcoin' },\n      { pattern: /\\bbtc\\b/i, entity: 'bitcoin' },\n      { pattern: /\\bethereum\\b/i, entity: 'ethereum' },\n      { pattern: /\\beth\\b/i, entity: 'ethereum' },\n      { pattern: /\\bcardano\\b/i, entity: 'cardano' },\n      { pattern: /\\bada\\b/i, entity: 'cardano' },\n      { pattern: /\\bsolana\\b/i, entity: 'solana' },\n      { pattern: /\\bsol\\b/i, entity: 'solana' },\n      { pattern: /\\bpolkadot\\b/i, entity: 'polkadot' },\n      { pattern: /\\bdot\\b/i, entity: 'polkadot' },\n      { pattern: /\\bchainlink\\b/i, entity: 'chainlink' },\n      { pattern: /\\blink\\b/i, entity: 'chainlink' },\n      { pattern: /\\blitecoin\\b/i, entity: 'litecoin' },\n      { pattern: /\\bltc\\b/i, entity: 'litecoin' },\n      { pattern: /\\bdogecoin\\b/i, entity: 'dogecoin' },\n      { pattern: /\\bdoge\\b/i, entity: 'dogecoin' },\n      { pattern: /\\bshiba\\b/i, entity: 'shiba-inu' },\n      { pattern: /\\bshib\\b/i, entity: 'shiba-inu' },\n      { pattern: /\\bavalanche\\b/i, entity: 'avalanche-2' },\n      { pattern: /\\bavax\\b/i, entity: 'avalanche-2' },\n      { pattern: /\\bmatic\\b/i, entity: 'matic-network' },\n      { pattern: /\\bpolygon\\b/i, entity: 'matic-network' },\n      { pattern: /\\bbnb\\b/i, entity: 'binancecoin' },\n      { pattern: /\\bbinance\\b/i, entity: 'binancecoin' },\n      { pattern: /\\bxrp\\b/i, entity: 'ripple' },\n      { pattern: /\\bripple\\b/i, entity: 'ripple' },\n      { pattern: /\\batom\\b/i, entity: 'cosmos' },\n      { pattern: /\\bcosmos\\b/i, entity: 'cosmos' },\n      { pattern: /\\balgo\\b/i, entity: 'algorand' },\n      { pattern: /\\balgorand\\b/i, entity: 'algorand' }\n    ];\n    \n    const entities: string[] = [];\n    for (const { pattern, entity } of cryptoPatterns) {\n      if (pattern.test(message)) {\n        if (!entities.includes(entity)) {\n          entities.push(entity);\n        }\n      }\n    }\n    \n    return entities;\n  }\n  \n  /**\n   * Analisa o contexto da conversa para dar boost a certas intenções\n   */\n  private static analyzeConversationContext(history: Message[]): Record<string, number> {\n    const boost: Record<string, number> = {};\n    \n    if (history.length === 0) return boost;\n    \n    const recentMessages = history.slice(-3); // Últimas 3 mensagens\n    \n    for (const msg of recentMessages) {\n      if (msg.role === 'assistant' && msg.cryptoData) {\n        boost.CRYPTO_ANALYSIS = (boost.CRYPTO_ANALYSIS || 0) + 0.5;\n      }\n      \n      if (msg.content.toLowerCase().includes('trading') || msg.content.toLowerCase().includes('comprar')) {\n        boost.TRADING_ADVICE = (boost.TRADING_ADVICE || 0) + 0.3;\n      }\n      \n      if (msg.content.toLowerCase().includes('mercado')) {\n        boost.MARKET_GENERAL = (boost.MARKET_GENERAL || 0) + 0.3;\n      }\n    }\n    \n    return boost;\n  }\n  \n  /**\n   * Gera um resumo do contexto para ajudar na resposta\n   */\n  private static generateContextSummary(message: string, history: Message[]): string {\n    const contexts = [];\n    \n    if (history.length > 0) {\n      const lastMessage = history[history.length - 1];\n      if (lastMessage.role === 'assistant' && lastMessage.cryptoData) {\n        contexts.push(`Última análise: ${lastMessage.cryptoData.name}`);\n      }\n    }\n    \n    const cryptoMentioned = this.extractCryptoEntities(message);\n    if (cryptoMentioned.length > 0) {\n      contexts.push(`Cryptos mencionadas: ${cryptoMentioned.join(', ')}`);\n    }\n    \n    return contexts.join(' | ');\n  }\n}\n"], "names": [], "mappings": ";;;AAqBO,MAAM;IAEX;;GAEC,GACD,OAAO,eAAe,OAAe,EAAE,sBAAiC,EAAE,EAAwB;QAChG,MAAM,oBAAoB,QAAQ,WAAW,GAAG,IAAI;QAEpD,qCAAqC;QACrC,MAAM,WAAW;YACf,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,UAAU;gBACR;gBACA;aACD;YAED,MAAM;gBACJ;gBACA;gBACA;aACD;YAED,iBAAiB;gBACf;gBACA;gBACA;gBACA;aACD;YAED,gBAAgB;gBACd;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,gBAAgB;gBACd;gBACA;gBACA;gBACA;gBACA;aACD;YAED,YAAY;gBACV;gBACA;gBACA;gBACA;aACD;YAED,kBAAkB;gBAChB;gBACA;gBACA;gBACA;aACD;YAED,eAAe;gBACb;gBACA;gBACA;gBACA;aACD;QACH;QAEA,4CAA4C;QAC5C,MAAM,iBAAiB,IAAI,CAAC,qBAAqB,CAAC;QAElD,oCAAoC;QACpC,MAAM,SAAqC;YACzC,YAAY;YACZ,iBAAiB;YACjB,gBAAgB;YAChB,UAAU;YACV,eAAe;YACf,MAAM;YACN,gBAAgB;YAChB,YAAY;YACZ,kBAAkB;YAClB,SAAS;QACX;QAEA,qCAAqC;QACrC,KAAK,MAAM,CAAC,QAAQ,YAAY,IAAI,OAAO,OAAO,CAAC,UAAW;YAC5D,KAAK,MAAM,WAAW,YAAa;gBACjC,IAAI,QAAQ,IAAI,CAAC,oBAAoB;oBACnC,MAAM,CAAC,OAAqB,IAAI;gBAClC;YACF;QACF;QAEA,qEAAqE;QACrE,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,OAAO,eAAe,IAAI,eAAe,MAAM,GAAG;QACpD;QAEA,iCAAiC;QACjC,MAAM,eAAe,IAAI,CAAC,0BAA0B,CAAC;QACrD,KAAK,MAAM,CAAC,QAAQ,MAAM,IAAI,OAAO,OAAO,CAAC,cAAe;YAC1D,MAAM,CAAC,OAAqB,IAAI;QAClC;QAEA,sCAAsC;QACtC,MAAM,WAAW,KAAK,GAAG,IAAI,OAAO,MAAM,CAAC;QAC3C,MAAM,aAAa,OAAO,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,UAAU,WAAW,CAAC,EAAE;QAEvF,2DAA2D;QAC3D,MAAM,cAAc,WAAW,IAAI,aAAa;QAChD,MAAM,aAAa,WAAW,IAAI,KAAK,GAAG,CAAC,WAAW,GAAG,KAAK;QAE9D,OAAO;YACL,QAAQ;YACR;YACA,UAAU;YACV,SAAS,IAAI,CAAC,sBAAsB,CAAC,mBAAmB;QAC1D;IACF;IAEA;;GAEC,GACD,OAAe,sBAAsB,OAAe,EAAY;QAC9D,MAAM,iBAAiB;YACrB;gBAAE,SAAS;gBAAgB,QAAQ;YAAU;YAC7C;gBAAE,SAAS;gBAAY,QAAQ;YAAU;YACzC;gBAAE,SAAS;gBAAiB,QAAQ;YAAW;YAC/C;gBAAE,SAAS;gBAAY,QAAQ;YAAW;YAC1C;gBAAE,SAAS;gBAAgB,QAAQ;YAAU;YAC7C;gBAAE,SAAS;gBAAY,QAAQ;YAAU;YACzC;gBAAE,SAAS;gBAAe,QAAQ;YAAS;YAC3C;gBAAE,SAAS;gBAAY,QAAQ;YAAS;YACxC;gBAAE,SAAS;gBAAiB,QAAQ;YAAW;YAC/C;gBAAE,SAAS;gBAAY,QAAQ;YAAW;YAC1C;gBAAE,SAAS;gBAAkB,QAAQ;YAAY;YACjD;gBAAE,SAAS;gBAAa,QAAQ;YAAY;YAC5C;gBAAE,SAAS;gBAAiB,QAAQ;YAAW;YAC/C;gBAAE,SAAS;gBAAY,QAAQ;YAAW;YAC1C;gBAAE,SAAS;gBAAiB,QAAQ;YAAW;YAC/C;gBAAE,SAAS;gBAAa,QAAQ;YAAW;YAC3C;gBAAE,SAAS;gBAAc,QAAQ;YAAY;YAC7C;gBAAE,SAAS;gBAAa,QAAQ;YAAY;YAC5C;gBAAE,SAAS;gBAAkB,QAAQ;YAAc;YACnD;gBAAE,SAAS;gBAAa,QAAQ;YAAc;YAC9C;gBAAE,SAAS;gBAAc,QAAQ;YAAgB;YACjD;gBAAE,SAAS;gBAAgB,QAAQ;YAAgB;YACnD;gBAAE,SAAS;gBAAY,QAAQ;YAAc;YAC7C;gBAAE,SAAS;gBAAgB,QAAQ;YAAc;YACjD;gBAAE,SAAS;gBAAY,QAAQ;YAAS;YACxC;gBAAE,SAAS;gBAAe,QAAQ;YAAS;YAC3C;gBAAE,SAAS;gBAAa,QAAQ;YAAS;YACzC;gBAAE,SAAS;gBAAe,QAAQ;YAAS;YAC3C;gBAAE,SAAS;gBAAa,QAAQ;YAAW;YAC3C;gBAAE,SAAS;gBAAiB,QAAQ;YAAW;SAChD;QAED,MAAM,WAAqB,EAAE;QAC7B,KAAK,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,eAAgB;YAChD,IAAI,QAAQ,IAAI,CAAC,UAAU;gBACzB,IAAI,CAAC,SAAS,QAAQ,CAAC,SAAS;oBAC9B,SAAS,IAAI,CAAC;gBAChB;YACF;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAe,2BAA2B,OAAkB,EAA0B;QACpF,MAAM,QAAgC,CAAC;QAEvC,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO;QAEjC,MAAM,iBAAiB,QAAQ,KAAK,CAAC,CAAC,IAAI,sBAAsB;QAEhE,KAAK,MAAM,OAAO,eAAgB;YAChC,IAAI,IAAI,IAAI,KAAK,eAAe,IAAI,UAAU,EAAE;gBAC9C,MAAM,eAAe,GAAG,CAAC,MAAM,eAAe,IAAI,CAAC,IAAI;YACzD;YAEA,IAAI,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,cAAc,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY;gBAClG,MAAM,cAAc,GAAG,CAAC,MAAM,cAAc,IAAI,CAAC,IAAI;YACvD;YAEA,IAAI,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY;gBACjD,MAAM,cAAc,GAAG,CAAC,MAAM,cAAc,IAAI,CAAC,IAAI;YACvD;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAe,uBAAuB,OAAe,EAAE,OAAkB,EAAU;QACjF,MAAM,WAAW,EAAE;QAEnB,IAAI,QAAQ,MAAM,GAAG,GAAG;YACtB,MAAM,cAAc,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;YAC/C,IAAI,YAAY,IAAI,KAAK,eAAe,YAAY,UAAU,EAAE;gBAC9D,SAAS,IAAI,CAAC,CAAC,gBAAgB,EAAE,YAAY,UAAU,CAAC,IAAI,EAAE;YAChE;QACF;QAEA,MAAM,kBAAkB,IAAI,CAAC,qBAAqB,CAAC;QACnD,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,SAAS,IAAI,CAAC,CAAC,qBAAqB,EAAE,gBAAgB,IAAI,CAAC,OAAO;QACpE;QAEA,OAAO,SAAS,IAAI,CAAC;IACvB;AACF", "debugId": null}}, {"offset": {"line": 1097, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/lib/context-analyzer.ts"], "sourcesContent": ["import { Message, AgentType } from '@/types';\nimport { IntentType, IntentClassification } from './intent-classifier';\n\nexport interface ContextAnalysis {\n  conversationFlow: string;\n  userMood: 'positive' | 'negative' | 'neutral' | 'curious' | 'urgent';\n  topicContinuity: boolean;\n  previousContext: string;\n  suggestedResponseTone: 'professional' | 'casual' | 'helpful' | 'educational' | 'reassuring';\n  keyTopics: string[];\n  userExpertiseLevel: 'beginner' | 'intermediate' | 'advanced';\n}\n\nexport class ContextAnalyzer {\n  \n  /**\n   * Analisa o contexto completo da conversa\n   */\n  static analyzeContext(\n    currentMessage: string,\n    history: Message[],\n    intent: IntentClassification,\n    agentType: AgentType\n  ): ContextAnalysis {\n    \n    const userMood = this.detectUserMood(currentMessage, history);\n    const topicContinuity = this.checkTopicContinuity(currentMessage, history);\n    const conversationFlow = this.analyzeConversationFlow(history);\n    const previousContext = this.extractPreviousContext(history);\n    const suggestedResponseTone = this.suggestResponseTone(intent, userMood, agentType);\n    const keyTopics = this.extractKeyTopics(currentMessage, history);\n    const userExpertiseLevel = this.assessUserExpertise(history);\n    \n    return {\n      conversationFlow,\n      userMood,\n      topicContinuity,\n      previousContext,\n      suggestedResponseTone,\n      keyTopics,\n      userExpertiseLevel\n    };\n  }\n  \n  /**\n   * Detecta o humor/tom do usuário\n   */\n  private static detectUserMood(message: string, history: Message[]): ContextAnalysis['userMood'] {\n    const normalizedMessage = message.toLowerCase();\n    \n    // Padrões de humor\n    const moodPatterns = {\n      positive: [\n        /\\b(obrigado|obrigada|valeu|legal|ótimo|otimo|perfeito|excelente|show|top|massa|bacana)\\b/i,\n        /\\b(gostei|adorei|amei|curtir|maneiro)\\b/i,\n        /[!]{2,}/, // Múltiplas exclamações\n        /😊|😄|😃|👍|🚀|💪|🔥/\n      ],\n      negative: [\n        /\\b(não|nao)\\s+(entendi|gostei|concordo|funciona|deu\\s+certo)\\b/i,\n        /\\b(ruim|péssimo|pessimo|horrível|horrivel|droga|merda)\\b/i,\n        /\\b(frustrado|irritado|chateado|decepcionado)\\b/i,\n        /😠|😡|😤|👎|💩/\n      ],\n      curious: [\n        /\\b(como|por\\s+que|porque|quando|onde|qual|quais)\\b/i,\n        /\\?{2,}/, // Múltiplas interrogações\n        /\\b(interessante|curioso|quero\\s+saber|me\\s+explica)\\b/i,\n        /🤔|❓|❔/\n      ],\n      urgent: [\n        /\\b(urgente|rápido|rapido|agora|já|ja|preciso\\s+agora)\\b/i,\n        /\\b(help|socorro|ajuda\\s+urgente)\\b/i,\n        /[!]{3,}/, // Muitas exclamações\n        /🆘|⚠️|🚨/\n      ]\n    };\n    \n    // Conta matches para cada humor\n    const scores = {\n      positive: 0,\n      negative: 0,\n      curious: 0,\n      urgent: 0\n    };\n    \n    for (const [mood, patterns] of Object.entries(moodPatterns)) {\n      for (const pattern of patterns) {\n        if (pattern.test(normalizedMessage)) {\n          scores[mood as keyof typeof scores]++;\n        }\n      }\n    }\n    \n    // Considera histórico recente\n    const recentMessages = history.slice(-2);\n    for (const msg of recentMessages) {\n      if (msg.role === 'user') {\n        for (const [mood, patterns] of Object.entries(moodPatterns)) {\n          for (const pattern of patterns) {\n            if (pattern.test(msg.content.toLowerCase())) {\n              scores[mood as keyof typeof scores] += 0.5;\n            }\n          }\n        }\n      }\n    }\n    \n    // Retorna o humor com maior score\n    const maxScore = Math.max(...Object.values(scores));\n    if (maxScore === 0) return 'neutral';\n    \n    const detectedMood = Object.entries(scores).find(([_, score]) => score === maxScore)?.[0];\n    return detectedMood as ContextAnalysis['userMood'] || 'neutral';\n  }\n  \n  /**\n   * Verifica se há continuidade no tópico da conversa\n   */\n  private static checkTopicContinuity(message: string, history: Message[]): boolean {\n    if (history.length === 0) return false;\n    \n    const lastMessages = history.slice(-3);\n    const currentTopics = this.extractTopicsFromMessage(message);\n    \n    for (const msg of lastMessages) {\n      const msgTopics = this.extractTopicsFromMessage(msg.content);\n      const commonTopics = currentTopics.filter(topic => msgTopics.includes(topic));\n      if (commonTopics.length > 0) return true;\n    }\n    \n    return false;\n  }\n  \n  /**\n   * Extrai tópicos de uma mensagem\n   */\n  private static extractTopicsFromMessage(message: string): string[] {\n    const topics = [];\n    const normalizedMessage = message.toLowerCase();\n    \n    // Tópicos de crypto\n    const cryptoTopics = ['bitcoin', 'ethereum', 'trading', 'mercado', 'preço', 'análise', 'investimento'];\n    for (const topic of cryptoTopics) {\n      if (normalizedMessage.includes(topic)) {\n        topics.push(topic);\n      }\n    }\n    \n    return topics;\n  }\n  \n  /**\n   * Analisa o fluxo da conversa\n   */\n  private static analyzeConversationFlow(history: Message[]): string {\n    if (history.length === 0) return 'início da conversa';\n    if (history.length === 1) return 'segunda interação';\n    \n    const recentMessages = history.slice(-5);\n    const userMessages = recentMessages.filter(msg => msg.role === 'user');\n    \n    if (userMessages.length >= 3) {\n      return 'conversa estabelecida';\n    } else if (userMessages.length === 2) {\n      return 'construindo rapport';\n    } else {\n      return 'início da interação';\n    }\n  }\n  \n  /**\n   * Extrai contexto das mensagens anteriores\n   */\n  private static extractPreviousContext(history: Message[]): string {\n    if (history.length === 0) return '';\n    \n    const contexts = [];\n    const lastAssistantMessage = history.slice().reverse().find(msg => msg.role === 'assistant');\n    \n    if (lastAssistantMessage) {\n      if (lastAssistantMessage.cryptoData) {\n        contexts.push(`Última análise: ${lastAssistantMessage.cryptoData.name}`);\n      }\n      \n      if (lastAssistantMessage.agentType) {\n        contexts.push(`Último agente: ${lastAssistantMessage.agentType}`);\n      }\n    }\n    \n    return contexts.join(' | ');\n  }\n  \n  /**\n   * Sugere o tom de resposta baseado no contexto\n   */\n  private static suggestResponseTone(\n    intent: IntentClassification,\n    userMood: ContextAnalysis['userMood'],\n    agentType: AgentType\n  ): ContextAnalysis['suggestedResponseTone'] {\n    \n    // Baseado na intenção\n    if (intent.intent === 'HELP' || intent.intent === 'AGENT_INFO') {\n      return 'helpful';\n    }\n    \n    if (intent.intent === 'CRYPTO_ANALYSIS' || intent.intent === 'TRADING_ADVICE') {\n      return 'professional';\n    }\n    \n    if (intent.intent === 'GREETING' || intent.intent === 'PERSONAL_CHAT') {\n      return 'casual';\n    }\n    \n    // Baseado no humor do usuário\n    if (userMood === 'negative' || userMood === 'urgent') {\n      return 'reassuring';\n    }\n    \n    if (userMood === 'curious') {\n      return 'educational';\n    }\n    \n    // Baseado no tipo de agente\n    if (agentType === 'ANALYST') {\n      return 'professional';\n    } else if (agentType === 'MENTOR') {\n      return 'educational';\n    } else {\n      return 'helpful';\n    }\n  }\n  \n  /**\n   * Extrai tópicos-chave da conversa\n   */\n  private static extractKeyTopics(message: string, history: Message[]): string[] {\n    const topics = new Set<string>();\n    \n    // Tópicos da mensagem atual\n    const currentTopics = this.extractTopicsFromMessage(message);\n    currentTopics.forEach(topic => topics.add(topic));\n    \n    // Tópicos do histórico recente\n    const recentMessages = history.slice(-3);\n    for (const msg of recentMessages) {\n      const msgTopics = this.extractTopicsFromMessage(msg.content);\n      msgTopics.forEach(topic => topics.add(topic));\n    }\n    \n    return Array.from(topics);\n  }\n  \n  /**\n   * Avalia o nível de expertise do usuário\n   */\n  private static assessUserExpertise(history: Message[]): ContextAnalysis['userExpertiseLevel'] {\n    const userMessages = history.filter(msg => msg.role === 'user');\n    \n    let technicalTerms = 0;\n    let basicQuestions = 0;\n    \n    const technicalPatterns = [\n      /\\b(rsi|macd|bollinger|fibonacci|support|resistance|volume|candlestick)\\b/i,\n      /\\b(scalping|swing\\s+trading|day\\s+trading|hodl|dca)\\b/i,\n      /\\b(market\\s+cap|liquidity|volatility|correlation)\\b/i\n    ];\n    \n    const basicPatterns = [\n      /\\b(o\\s+que\\s+é|como\\s+funciona|não\\s+entendo|nao\\s+entendo)\\b/i,\n      /\\b(iniciante|começando|comecando|primeiro|primeira\\s+vez)\\b/i,\n      /\\b(explique|me\\s+ensina|como\\s+faço|como\\s+faco)\\b/i\n    ];\n    \n    for (const msg of userMessages) {\n      const content = msg.content.toLowerCase();\n      \n      for (const pattern of technicalPatterns) {\n        if (pattern.test(content)) technicalTerms++;\n      }\n      \n      for (const pattern of basicPatterns) {\n        if (pattern.test(content)) basicQuestions++;\n      }\n    }\n    \n    if (technicalTerms >= 2) return 'advanced';\n    if (technicalTerms >= 1 && basicQuestions === 0) return 'intermediate';\n    return 'beginner';\n  }\n}\n"], "names": [], "mappings": ";;;AAaO,MAAM;IAEX;;GAEC,GACD,OAAO,eACL,cAAsB,EACtB,OAAkB,EAClB,MAA4B,EAC5B,SAAoB,EACH;QAEjB,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,gBAAgB;QACrD,MAAM,kBAAkB,IAAI,CAAC,oBAAoB,CAAC,gBAAgB;QAClE,MAAM,mBAAmB,IAAI,CAAC,uBAAuB,CAAC;QACtD,MAAM,kBAAkB,IAAI,CAAC,sBAAsB,CAAC;QACpD,MAAM,wBAAwB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,UAAU;QACzE,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,gBAAgB;QACxD,MAAM,qBAAqB,IAAI,CAAC,mBAAmB,CAAC;QAEpD,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA;;GAEC,GACD,OAAe,eAAe,OAAe,EAAE,OAAkB,EAA+B;QAC9F,MAAM,oBAAoB,QAAQ,WAAW;QAE7C,mBAAmB;QACnB,MAAM,eAAe;YACnB,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;gBACN;gBACA;gBACA;gBACA;aACD;QACH;QAEA,gCAAgC;QAChC,MAAM,SAAS;YACb,UAAU;YACV,UAAU;YACV,SAAS;YACT,QAAQ;QACV;QAEA,KAAK,MAAM,CAAC,MAAM,SAAS,IAAI,OAAO,OAAO,CAAC,cAAe;YAC3D,KAAK,MAAM,WAAW,SAAU;gBAC9B,IAAI,QAAQ,IAAI,CAAC,oBAAoB;oBACnC,MAAM,CAAC,KAA4B;gBACrC;YACF;QACF;QAEA,8BAA8B;QAC9B,MAAM,iBAAiB,QAAQ,KAAK,CAAC,CAAC;QACtC,KAAK,MAAM,OAAO,eAAgB;YAChC,IAAI,IAAI,IAAI,KAAK,QAAQ;gBACvB,KAAK,MAAM,CAAC,MAAM,SAAS,IAAI,OAAO,OAAO,CAAC,cAAe;oBAC3D,KAAK,MAAM,WAAW,SAAU;wBAC9B,IAAI,QAAQ,IAAI,CAAC,IAAI,OAAO,CAAC,WAAW,KAAK;4BAC3C,MAAM,CAAC,KAA4B,IAAI;wBACzC;oBACF;gBACF;YACF;QACF;QAEA,kCAAkC;QAClC,MAAM,WAAW,KAAK,GAAG,IAAI,OAAO,MAAM,CAAC;QAC3C,IAAI,aAAa,GAAG,OAAO;QAE3B,MAAM,eAAe,OAAO,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,UAAU,WAAW,CAAC,EAAE;QACzF,OAAO,gBAA+C;IACxD;IAEA;;GAEC,GACD,OAAe,qBAAqB,OAAe,EAAE,OAAkB,EAAW;QAChF,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO;QAEjC,MAAM,eAAe,QAAQ,KAAK,CAAC,CAAC;QACpC,MAAM,gBAAgB,IAAI,CAAC,wBAAwB,CAAC;QAEpD,KAAK,MAAM,OAAO,aAAc;YAC9B,MAAM,YAAY,IAAI,CAAC,wBAAwB,CAAC,IAAI,OAAO;YAC3D,MAAM,eAAe,cAAc,MAAM,CAAC,CAAA,QAAS,UAAU,QAAQ,CAAC;YACtE,IAAI,aAAa,MAAM,GAAG,GAAG,OAAO;QACtC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAe,yBAAyB,OAAe,EAAY;QACjE,MAAM,SAAS,EAAE;QACjB,MAAM,oBAAoB,QAAQ,WAAW;QAE7C,oBAAoB;QACpB,MAAM,eAAe;YAAC;YAAW;YAAY;YAAW;YAAW;YAAS;YAAW;SAAe;QACtG,KAAK,MAAM,SAAS,aAAc;YAChC,IAAI,kBAAkB,QAAQ,CAAC,QAAQ;gBACrC,OAAO,IAAI,CAAC;YACd;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAe,wBAAwB,OAAkB,EAAU;QACjE,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO;QACjC,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO;QAEjC,MAAM,iBAAiB,QAAQ,KAAK,CAAC,CAAC;QACtC,MAAM,eAAe,eAAe,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QAE/D,IAAI,aAAa,MAAM,IAAI,GAAG;YAC5B,OAAO;QACT,OAAO,IAAI,aAAa,MAAM,KAAK,GAAG;YACpC,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA;;GAEC,GACD,OAAe,uBAAuB,OAAkB,EAAU;QAChE,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO;QAEjC,MAAM,WAAW,EAAE;QACnB,MAAM,uBAAuB,QAAQ,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QAEhF,IAAI,sBAAsB;YACxB,IAAI,qBAAqB,UAAU,EAAE;gBACnC,SAAS,IAAI,CAAC,CAAC,gBAAgB,EAAE,qBAAqB,UAAU,CAAC,IAAI,EAAE;YACzE;YAEA,IAAI,qBAAqB,SAAS,EAAE;gBAClC,SAAS,IAAI,CAAC,CAAC,eAAe,EAAE,qBAAqB,SAAS,EAAE;YAClE;QACF;QAEA,OAAO,SAAS,IAAI,CAAC;IACvB;IAEA;;GAEC,GACD,OAAe,oBACb,MAA4B,EAC5B,QAAqC,EACrC,SAAoB,EACsB;QAE1C,sBAAsB;QACtB,IAAI,OAAO,MAAM,KAAK,UAAU,OAAO,MAAM,KAAK,cAAc;YAC9D,OAAO;QACT;QAEA,IAAI,OAAO,MAAM,KAAK,qBAAqB,OAAO,MAAM,KAAK,kBAAkB;YAC7E,OAAO;QACT;QAEA,IAAI,OAAO,MAAM,KAAK,cAAc,OAAO,MAAM,KAAK,iBAAiB;YACrE,OAAO;QACT;QAEA,8BAA8B;QAC9B,IAAI,aAAa,cAAc,aAAa,UAAU;YACpD,OAAO;QACT;QAEA,IAAI,aAAa,WAAW;YAC1B,OAAO;QACT;QAEA,4BAA4B;QAC5B,IAAI,cAAc,WAAW;YAC3B,OAAO;QACT,OAAO,IAAI,cAAc,UAAU;YACjC,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA;;GAEC,GACD,OAAe,iBAAiB,OAAe,EAAE,OAAkB,EAAY;QAC7E,MAAM,SAAS,IAAI;QAEnB,4BAA4B;QAC5B,MAAM,gBAAgB,IAAI,CAAC,wBAAwB,CAAC;QACpD,cAAc,OAAO,CAAC,CAAA,QAAS,OAAO,GAAG,CAAC;QAE1C,+BAA+B;QAC/B,MAAM,iBAAiB,QAAQ,KAAK,CAAC,CAAC;QACtC,KAAK,MAAM,OAAO,eAAgB;YAChC,MAAM,YAAY,IAAI,CAAC,wBAAwB,CAAC,IAAI,OAAO;YAC3D,UAAU,OAAO,CAAC,CAAA,QAAS,OAAO,GAAG,CAAC;QACxC;QAEA,OAAO,MAAM,IAAI,CAAC;IACpB;IAEA;;GAEC,GACD,OAAe,oBAAoB,OAAkB,EAAyC;QAC5F,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QAExD,IAAI,iBAAiB;QACrB,IAAI,iBAAiB;QAErB,MAAM,oBAAoB;YACxB;YACA;YACA;SACD;QAED,MAAM,gBAAgB;YACpB;YACA;YACA;SACD;QAED,KAAK,MAAM,OAAO,aAAc;YAC9B,MAAM,UAAU,IAAI,OAAO,CAAC,WAAW;YAEvC,KAAK,MAAM,WAAW,kBAAmB;gBACvC,IAAI,QAAQ,IAAI,CAAC,UAAU;YAC7B;YAEA,KAAK,MAAM,WAAW,cAAe;gBACnC,IAAI,QAAQ,IAAI,CAAC,UAAU;YAC7B;QACF;QAEA,IAAI,kBAAkB,GAAG,OAAO;QAChC,IAAI,kBAAkB,KAAK,mBAAmB,GAAG,OAAO;QACxD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1331, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/lib/intelligent-response-generator.ts"], "sourcesContent": ["import { AgentType, CryptoData } from '@/types';\nimport { IntentType, IntentClassification } from './intent-classifier';\nimport { ContextAnalysis } from './context-analyzer';\n\nexport interface ResponseTemplate {\n  systemPrompt: string;\n  userPrompt: string;\n  includeMarketData: boolean;\n  includeTechnicalAnalysis: boolean;\n  responseStyle: 'conversational' | 'analytical' | 'educational' | 'supportive';\n}\n\nexport class IntelligentResponseGenerator {\n  \n  /**\n   * Gera um template de resposta baseado na intenção e contexto\n   */\n  static generateResponseTemplate(\n    intent: IntentClassification,\n    context: ContextAnalysis,\n    agentType: AgentType,\n    userMessage: string\n  ): ResponseTemplate {\n    \n    const agentPersonality = this.getAgentPersonality(agentType);\n    \n    switch (intent.intent) {\n      case 'AGENT_INFO':\n        return this.generateAgentInfoResponse(agentPersonality, context, userMessage);\n      \n      case 'GREETING':\n        return this.generateGreetingResponse(agentPersonality, context);\n      \n      case 'HELP':\n        return this.generateHelpResponse(agentPersonality, context);\n      \n      case 'CRYPTO_ANALYSIS':\n        return this.generateCryptoAnalysisResponse(agentPersonality, context, intent);\n      \n      case 'TRADING_ADVICE':\n        return this.generateTradingAdviceResponse(agentPersonality, context, intent);\n      \n      case 'MARKET_GENERAL':\n        return this.generateMarketGeneralResponse(agentPersonality, context);\n      \n      case 'COMPARISON':\n        return this.generateComparisonResponse(agentPersonality, context, intent);\n      \n      case 'PRICE_PREDICTION':\n        return this.generatePricePredictionResponse(agentPersonality, context, intent);\n      \n      case 'PERSONAL_CHAT':\n        return this.generatePersonalChatResponse(agentPersonality, context);\n      \n      default:\n        return this.generateDefaultResponse(agentPersonality, context);\n    }\n  }\n  \n  /**\n   * Obtém a personalidade do agente\n   */\n  private static getAgentPersonality(agentType: AgentType) {\n    const personalities = {\n      ANALYST: {\n        name: 'Sarah \"The Wolf\" Rodriguez',\n        traits: 'analítica, precisa, focada em dados',\n        expertise: 'análise técnica e fundamental',\n        style: 'profissional e detalhada'\n      },\n      MENTOR: {\n        name: 'Marcus \"The Sage\" Chen',\n        traits: 'educativo, paciente, experiente',\n        expertise: 'educação e estratégias de longo prazo',\n        style: 'didático e encorajador'\n      },\n      STRATEGIST: {\n        name: 'Alex \"The Navigator\" Kim',\n        traits: 'estratégico, visionário, adaptável',\n        expertise: 'estratégias de mercado e timing',\n        style: 'estratégico e inspirador'\n      }\n    };\n    \n    return personalities[agentType];\n  }\n  \n  /**\n   * Gera resposta para perguntas sobre o agente\n   */\n  private static generateAgentInfoResponse(\n    personality: any,\n    context: ContextAnalysis,\n    userMessage: string\n  ): ResponseTemplate {\n    \n    const isAboutAvailability = /\\b(trabalha|funciona|disponível|disponivel|24\\s*horas?|horário|horario)\\b/i.test(userMessage);\n    const isAboutCapabilities = /\\b(pode|consegue|sabe|especialidades|habilidades|capacidades)\\b/i.test(userMessage);\n    const isAboutIdentity = /\\b(quem\\s+)?(você|voce)\\s+(é|eh|sou)\\b/i.test(userMessage);\n    \n    let systemPrompt = `Você é ${personality.name}, um agente de IA especializado em criptomoedas. \nSuas características: ${personality.traits}.\nSua expertise: ${personality.expertise}.\nSeu estilo: ${personality.style}.\n\nIMPORTANTE: Responda especificamente sobre VOCÊ como agente, não sobre análise de criptomoedas.`;\n    \n    let userPrompt = '';\n    \n    if (isAboutAvailability) {\n      return {\n        systemPrompt: `Você é ${personality.name}, um agente de IA especializado em criptomoedas.\nO usuário está perguntando especificamente sobre sua disponibilidade/horário de funcionamento.\nRESPONDA APENAS sobre disponibilidade - NÃO fale sobre análise de criptomoedas nesta resposta.`,\n        userPrompt: `Responda de forma clara e direta que você está disponível 24 horas por dia, 7 dias por semana,\ncomo um assistente de IA. Mantenha sua personalidade de ${personality.name} mas foque apenas na disponibilidade.\nPergunta: \"${userMessage}\"`,\n        includeMarketData: false,\n        includeTechnicalAnalysis: false,\n        responseStyle: 'conversational'\n      };\n    } else if (isAboutCapabilities) {\n      userPrompt = `O usuário quer saber sobre suas capacidades e habilidades.\nExplique o que você pode fazer como ${personality.name}, suas especialidades em criptomoedas,\ne como pode ajudar o usuário. Mantenha o foco em suas capacidades, não em análises específicas.\n\nPergunta do usuário: \"${userMessage}\"`;\n    } else if (isAboutIdentity) {\n      userPrompt = `O usuário quer saber quem você é.\nApresente-se como ${personality.name}, explique sua personalidade (${personality.traits}),\nsua área de expertise (${personality.expertise}) e como você pode ajudar.\n\nPergunta do usuário: \"${userMessage}\"`;\n    } else {\n      userPrompt = `O usuário está fazendo uma pergunta sobre você como agente.\nResponda de forma apropriada mantendo sua personalidade de ${personality.name}.\nFoque na pergunta específica sobre você, não desvie para análise de mercado.\n\nPergunta do usuário: \"${userMessage}\"`;\n    }\n    \n    return {\n      systemPrompt,\n      userPrompt,\n      includeMarketData: false,\n      includeTechnicalAnalysis: false,\n      responseStyle: context.suggestedResponseTone === 'casual' ? 'conversational' : 'supportive'\n    };\n  }\n  \n  /**\n   * Gera resposta para saudações\n   */\n  private static generateGreetingResponse(\n    personality: any,\n    context: ContextAnalysis\n  ): ResponseTemplate {\n    \n    const systemPrompt = `Você é ${personality.name}, um agente de IA especializado em criptomoedas.\nResponda à saudação de forma amigável e natural, apresentando-se brevemente e oferecendo ajuda.\nMantenha sua personalidade: ${personality.traits}.`;\n    \n    const userPrompt = `Responda à saudação do usuário de forma calorosa e profissional.\nApresente-se como ${personality.name} e ofereça sua ajuda com análises de criptomoedas.\nSeja ${context.userExpertiseLevel === 'beginner' ? 'mais didático' : 'mais direto'}.`;\n    \n    return {\n      systemPrompt,\n      userPrompt,\n      includeMarketData: false,\n      includeTechnicalAnalysis: false,\n      responseStyle: 'conversational'\n    };\n  }\n  \n  /**\n   * Gera resposta para pedidos de ajuda\n   */\n  private static generateHelpResponse(\n    personality: any,\n    context: ContextAnalysis\n  ): ResponseTemplate {\n    \n    const systemPrompt = `Você é ${personality.name}, especialista em criptomoedas.\nO usuário precisa de ajuda. Seja ${personality.style} e ofereça orientação clara.\nNível do usuário: ${context.userExpertiseLevel}.`;\n    \n    const userPrompt = `O usuário está pedindo ajuda. Explique como você pode ajudar,\nquais tipos de análises você faz, e como o usuário pode interagir com você.\nAdapte sua linguagem para um usuário ${context.userExpertiseLevel}.`;\n    \n    return {\n      systemPrompt,\n      userPrompt,\n      includeMarketData: false,\n      includeTechnicalAnalysis: false,\n      responseStyle: 'educational'\n    };\n  }\n  \n  /**\n   * Gera resposta para análise de criptomoedas\n   */\n  private static generateCryptoAnalysisResponse(\n    personality: any,\n    context: ContextAnalysis,\n    intent: IntentClassification\n  ): ResponseTemplate {\n    \n    const systemPrompt = `Você é ${personality.name}, especialista em ${personality.expertise}.\nAnalise as criptomoedas mencionadas: ${intent.entities.join(', ')}.\nSeja ${personality.style} e adapte para usuário ${context.userExpertiseLevel}.\n${context.topicContinuity ? 'Continue a análise anterior se relevante.' : ''}`;\n    \n    const userPrompt = `Analise as criptomoedas: ${intent.entities.join(', ')}.\nForneça insights ${personality.expertise.includes('técnica') ? 'técnicos' : 'estratégicos'}.\nConsidere o contexto: ${context.previousContext}.\nUsuário é ${context.userExpertiseLevel}.`;\n    \n    return {\n      systemPrompt,\n      userPrompt,\n      includeMarketData: true,\n      includeTechnicalAnalysis: true,\n      responseStyle: 'analytical'\n    };\n  }\n  \n  /**\n   * Gera resposta para conselhos de trading\n   */\n  private static generateTradingAdviceResponse(\n    personality: any,\n    context: ContextAnalysis,\n    intent: IntentClassification\n  ): ResponseTemplate {\n    \n    const systemPrompt = `Você é ${personality.name}, especialista em ${personality.expertise}.\nForneça conselhos de trading responsáveis. SEMPRE inclua disclaimers sobre riscos.\nSeja ${personality.style} mas cauteloso. Usuário: ${context.userExpertiseLevel}.`;\n    \n    const userPrompt = `O usuário busca conselhos de trading sobre: ${intent.entities.join(', ')}.\nForneça orientações estratégicas, mas sempre com disclaimers de risco.\nAdapte para usuário ${context.userExpertiseLevel}.\n${context.userMood === 'urgent' ? 'Usuário parece ansioso - seja tranquilizador.' : ''}`;\n    \n    return {\n      systemPrompt,\n      userPrompt,\n      includeMarketData: true,\n      includeTechnicalAnalysis: true,\n      responseStyle: context.userMood === 'urgent' ? 'supportive' : 'analytical'\n    };\n  }\n  \n  /**\n   * Gera resposta para perguntas gerais sobre mercado\n   */\n  private static generateMarketGeneralResponse(\n    personality: any,\n    context: ContextAnalysis\n  ): ResponseTemplate {\n    \n    const systemPrompt = `Você é ${personality.name}, especialista em mercados de criptomoedas.\nForneça uma visão geral do mercado atual. Seja ${personality.style}.\nUsuário: ${context.userExpertiseLevel}.`;\n    \n    const userPrompt = `Forneça uma análise geral do mercado de criptomoedas atual.\nInclua sentimento, tendências principais e fatores relevantes.\nAdapte para usuário ${context.userExpertiseLevel}.`;\n    \n    return {\n      systemPrompt,\n      userPrompt,\n      includeMarketData: true,\n      includeTechnicalAnalysis: false,\n      responseStyle: 'analytical'\n    };\n  }\n  \n  /**\n   * Gera resposta para comparações\n   */\n  private static generateComparisonResponse(\n    personality: any,\n    context: ContextAnalysis,\n    intent: IntentClassification\n  ): ResponseTemplate {\n    \n    const systemPrompt = `Você é ${personality.name}. Compare as criptomoedas mencionadas: ${intent.entities.join(', ')}.\nSeja ${personality.style} e objetivo. Usuário: ${context.userExpertiseLevel}.`;\n    \n    const userPrompt = `Compare as criptomoedas: ${intent.entities.join(', ')}.\nAnalise prós, contras, casos de uso e potencial.\nSeja imparcial e educativo para usuário ${context.userExpertiseLevel}.`;\n    \n    return {\n      systemPrompt,\n      userPrompt,\n      includeMarketData: true,\n      includeTechnicalAnalysis: true,\n      responseStyle: 'educational'\n    };\n  }\n  \n  /**\n   * Gera resposta para previsões de preço\n   */\n  private static generatePricePredictionResponse(\n    personality: any,\n    context: ContextAnalysis,\n    intent: IntentClassification\n  ): ResponseTemplate {\n    \n    const systemPrompt = `Você é ${personality.name}. CUIDADO: Nunca faça previsões definitivas de preço.\nDiscuta fatores que podem influenciar preços, mas sempre com disclaimers.\nSeja ${personality.style} mas responsável.`;\n    \n    const userPrompt = `O usuário pergunta sobre previsões de preço para: ${intent.entities.join(', ')}.\nDiscuta fatores técnicos e fundamentais, mas NÃO faça previsões específicas.\nEduque sobre a imprevisibilidade do mercado.`;\n    \n    return {\n      systemPrompt,\n      userPrompt,\n      includeMarketData: true,\n      includeTechnicalAnalysis: true,\n      responseStyle: 'educational'\n    };\n  }\n  \n  /**\n   * Gera resposta para conversa pessoal\n   */\n  private static generatePersonalChatResponse(\n    personality: any,\n    context: ContextAnalysis\n  ): ResponseTemplate {\n    \n    const systemPrompt = `Você é ${personality.name}. O usuário quer conversar de forma mais pessoal.\nSeja amigável mas mantenha o foco profissional. Redirecione gentilmente para criptomoedas.`;\n    \n    const userPrompt = `Responda de forma amigável mas profissional.\nMantenha sua personalidade de ${personality.name} e redirecione para como pode ajudar com criptomoedas.`;\n    \n    return {\n      systemPrompt,\n      userPrompt,\n      includeMarketData: false,\n      includeTechnicalAnalysis: false,\n      responseStyle: 'conversational'\n    };\n  }\n  \n  /**\n   * Gera resposta padrão para casos não identificados\n   */\n  private static generateDefaultResponse(\n    personality: any,\n    context: ContextAnalysis\n  ): ResponseTemplate {\n    \n    const systemPrompt = `Você é ${personality.name}. A intenção do usuário não foi clara.\nPeça esclarecimentos de forma educada e ofereça opções de como pode ajudar.`;\n    \n    const userPrompt = `A pergunta não foi clara. Peça esclarecimentos educadamente e\nliste as principais formas como você pode ajudar com criptomoedas.`;\n    \n    return {\n      systemPrompt,\n      userPrompt,\n      includeMarketData: false,\n      includeTechnicalAnalysis: false,\n      responseStyle: 'supportive'\n    };\n  }\n}\n"], "names": [], "mappings": ";;;AAYO,MAAM;IAEX;;GAEC,GACD,OAAO,yBACL,MAA4B,EAC5B,OAAwB,EACxB,SAAoB,EACpB,WAAmB,EACD;QAElB,MAAM,mBAAmB,IAAI,CAAC,mBAAmB,CAAC;QAElD,OAAQ,OAAO,MAAM;YACnB,KAAK;gBACH,OAAO,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,SAAS;YAEnE,KAAK;gBACH,OAAO,IAAI,CAAC,wBAAwB,CAAC,kBAAkB;YAEzD,KAAK;gBACH,OAAO,IAAI,CAAC,oBAAoB,CAAC,kBAAkB;YAErD,KAAK;gBACH,OAAO,IAAI,CAAC,8BAA8B,CAAC,kBAAkB,SAAS;YAExE,KAAK;gBACH,OAAO,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,SAAS;YAEvE,KAAK;gBACH,OAAO,IAAI,CAAC,6BAA6B,CAAC,kBAAkB;YAE9D,KAAK;gBACH,OAAO,IAAI,CAAC,0BAA0B,CAAC,kBAAkB,SAAS;YAEpE,KAAK;gBACH,OAAO,IAAI,CAAC,+BAA+B,CAAC,kBAAkB,SAAS;YAEzE,KAAK;gBACH,OAAO,IAAI,CAAC,4BAA4B,CAAC,kBAAkB;YAE7D;gBACE,OAAO,IAAI,CAAC,uBAAuB,CAAC,kBAAkB;QAC1D;IACF;IAEA;;GAEC,GACD,OAAe,oBAAoB,SAAoB,EAAE;QACvD,MAAM,gBAAgB;YACpB,SAAS;gBACP,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,OAAO;YACT;YACA,QAAQ;gBACN,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,OAAO;YACT;YACA,YAAY;gBACV,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,OAAO;YACT;QACF;QAEA,OAAO,aAAa,CAAC,UAAU;IACjC;IAEA;;GAEC,GACD,OAAe,0BACb,WAAgB,EAChB,OAAwB,EACxB,WAAmB,EACD;QAElB,MAAM,sBAAsB,6EAA6E,IAAI,CAAC;QAC9G,MAAM,sBAAsB,mEAAmE,IAAI,CAAC;QACpG,MAAM,kBAAkB,0CAA0C,IAAI,CAAC;QAEvE,IAAI,eAAe,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC;sBAC5B,EAAE,YAAY,MAAM,CAAC;eAC5B,EAAE,YAAY,SAAS,CAAC;YAC3B,EAAE,YAAY,KAAK,CAAC;;+FAE+D,CAAC;QAE5F,IAAI,aAAa;QAEjB,IAAI,qBAAqB;YACvB,OAAO;gBACL,cAAc,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC;;8FAE6C,CAAC;gBACvF,YAAY,CAAC;wDACmC,EAAE,YAAY,IAAI,CAAC;WAChE,EAAE,YAAY,CAAC,CAAC;gBACnB,mBAAmB;gBACnB,0BAA0B;gBAC1B,eAAe;YACjB;QACF,OAAO,IAAI,qBAAqB;YAC9B,aAAa,CAAC;oCACgB,EAAE,YAAY,IAAI,CAAC;;;sBAGjC,EAAE,YAAY,CAAC,CAAC;QAClC,OAAO,IAAI,iBAAiB;YAC1B,aAAa,CAAC;kBACF,EAAE,YAAY,IAAI,CAAC,8BAA8B,EAAE,YAAY,MAAM,CAAC;uBACjE,EAAE,YAAY,SAAS,CAAC;;sBAEzB,EAAE,YAAY,CAAC,CAAC;QAClC,OAAO;YACL,aAAa,CAAC;2DACuC,EAAE,YAAY,IAAI,CAAC;;;sBAGxD,EAAE,YAAY,CAAC,CAAC;QAClC;QAEA,OAAO;YACL;YACA;YACA,mBAAmB;YACnB,0BAA0B;YAC1B,eAAe,QAAQ,qBAAqB,KAAK,WAAW,mBAAmB;QACjF;IACF;IAEA;;GAEC,GACD,OAAe,yBACb,WAAgB,EAChB,OAAwB,EACN;QAElB,MAAM,eAAe,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC;;4BAExB,EAAE,YAAY,MAAM,CAAC,CAAC,CAAC;QAE/C,MAAM,aAAa,CAAC;kBACN,EAAE,YAAY,IAAI,CAAC;KAChC,EAAE,QAAQ,kBAAkB,KAAK,aAAa,kBAAkB,cAAc,CAAC,CAAC;QAEjF,OAAO;YACL;YACA;YACA,mBAAmB;YACnB,0BAA0B;YAC1B,eAAe;QACjB;IACF;IAEA;;GAEC,GACD,OAAe,qBACb,WAAgB,EAChB,OAAwB,EACN;QAElB,MAAM,eAAe,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC;iCACnB,EAAE,YAAY,KAAK,CAAC;kBACnC,EAAE,QAAQ,kBAAkB,CAAC,CAAC,CAAC;QAE7C,MAAM,aAAa,CAAC;;qCAEa,EAAE,QAAQ,kBAAkB,CAAC,CAAC,CAAC;QAEhE,OAAO;YACL;YACA;YACA,mBAAmB;YACnB,0BAA0B;YAC1B,eAAe;QACjB;IACF;IAEA;;GAEC,GACD,OAAe,+BACb,WAAgB,EAChB,OAAwB,EACxB,MAA4B,EACV;QAElB,MAAM,eAAe,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC,kBAAkB,EAAE,YAAY,SAAS,CAAC;qCACzD,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM;KAC7D,EAAE,YAAY,KAAK,CAAC,uBAAuB,EAAE,QAAQ,kBAAkB,CAAC;AAC7E,EAAE,QAAQ,eAAe,GAAG,8CAA8C,IAAI;QAE1E,MAAM,aAAa,CAAC,yBAAyB,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM;iBAC7D,EAAE,YAAY,SAAS,CAAC,QAAQ,CAAC,aAAa,aAAa,eAAe;sBACrE,EAAE,QAAQ,eAAe,CAAC;UACtC,EAAE,QAAQ,kBAAkB,CAAC,CAAC,CAAC;QAErC,OAAO;YACL;YACA;YACA,mBAAmB;YACnB,0BAA0B;YAC1B,eAAe;QACjB;IACF;IAEA;;GAEC,GACD,OAAe,8BACb,WAAgB,EAChB,OAAwB,EACxB,MAA4B,EACV;QAElB,MAAM,eAAe,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC,kBAAkB,EAAE,YAAY,SAAS,CAAC;;KAEzF,EAAE,YAAY,KAAK,CAAC,yBAAyB,EAAE,QAAQ,kBAAkB,CAAC,CAAC,CAAC;QAE7E,MAAM,aAAa,CAAC,4CAA4C,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM;;oBAE7E,EAAE,QAAQ,kBAAkB,CAAC;AACjD,EAAE,QAAQ,QAAQ,KAAK,WAAW,kDAAkD,IAAI;QAEpF,OAAO;YACL;YACA;YACA,mBAAmB;YACnB,0BAA0B;YAC1B,eAAe,QAAQ,QAAQ,KAAK,WAAW,eAAe;QAChE;IACF;IAEA;;GAEC,GACD,OAAe,8BACb,WAAgB,EAChB,OAAwB,EACN;QAElB,MAAM,eAAe,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC;+CACL,EAAE,YAAY,KAAK,CAAC;SAC1D,EAAE,QAAQ,kBAAkB,CAAC,CAAC,CAAC;QAEpC,MAAM,aAAa,CAAC;;oBAEJ,EAAE,QAAQ,kBAAkB,CAAC,CAAC,CAAC;QAE/C,OAAO;YACL;YACA;YACA,mBAAmB;YACnB,0BAA0B;YAC1B,eAAe;QACjB;IACF;IAEA;;GAEC,GACD,OAAe,2BACb,WAAgB,EAChB,OAAwB,EACxB,MAA4B,EACV;QAElB,MAAM,eAAe,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC,uCAAuC,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM;KACnH,EAAE,YAAY,KAAK,CAAC,sBAAsB,EAAE,QAAQ,kBAAkB,CAAC,CAAC,CAAC;QAE1E,MAAM,aAAa,CAAC,yBAAyB,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM;;wCAEtC,EAAE,QAAQ,kBAAkB,CAAC,CAAC,CAAC;QAEnE,OAAO;YACL;YACA;YACA,mBAAmB;YACnB,0BAA0B;YAC1B,eAAe;QACjB;IACF;IAEA;;GAEC,GACD,OAAe,gCACb,WAAgB,EAChB,OAAwB,EACxB,MAA4B,EACV;QAElB,MAAM,eAAe,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC;;KAE/C,EAAE,YAAY,KAAK,CAAC,iBAAiB,CAAC;QAEvC,MAAM,aAAa,CAAC,kDAAkD,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM;;4CAE3D,CAAC;QAEzC,OAAO;YACL;YACA;YACA,mBAAmB;YACnB,0BAA0B;YAC1B,eAAe;QACjB;IACF;IAEA;;GAEC,GACD,OAAe,6BACb,WAAgB,EAChB,OAAwB,EACN;QAElB,MAAM,eAAe,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC;0FACsC,CAAC;QAEvF,MAAM,aAAa,CAAC;8BACM,EAAE,YAAY,IAAI,CAAC,sDAAsD,CAAC;QAEpG,OAAO;YACL;YACA;YACA,mBAAmB;YACnB,0BAA0B;YAC1B,eAAe;QACjB;IACF;IAEA;;GAEC,GACD,OAAe,wBACb,WAAgB,EAChB,OAAwB,EACN;QAElB,MAAM,eAAe,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC;2EACuB,CAAC;QAExE,MAAM,aAAa,CAAC;kEAC0C,CAAC;QAE/D,OAAO;YACL;YACA;YACA,mBAAmB;YACnB,0BAA0B;YAC1B,eAAe;QACjB;IACF;AACF", "debugId": null}}, {"offset": {"line": 1597, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/lib/ai-service.ts"], "sourcesContent": ["import { Message, AgentType, ChatContext, CryptoData, TechnicalIndicators, TradingSignal } from '@/types';\nimport { getAgent } from './agents';\nimport { CryptoApiService } from './crypto-api';\nimport { TechnicalAnalysisService } from './technical-analysis';\nimport { IntentClassifier, IntentClassification } from './intent-classifier';\nimport { ContextAnalyzer, ContextAnalysis } from './context-analyzer';\nimport { IntelligentResponseGenerator, ResponseTemplate } from './intelligent-response-generator';\n\nexport class AIService {\n  private static context: ChatContext = {\n    messages: [],\n    activeAgent: 'ANALYST',\n    marketSentiment: 'NEUTRAL'\n  };\n\n  /**\n   * Processa uma mensagem do usuário e retorna resposta do agente ativo\n   */\n  static async processMessage(\n    userMessage: string,\n    agentType?: AgentType\n  ): Promise<Message> {\n    // Atualiza agente ativo se especificado\n    if (agentType) {\n      this.context.activeAgent = agentType;\n    }\n\n    // Adiciona mensagem do usuário ao contexto\n    const userMsg: Message = {\n      id: this.generateId(),\n      content: userMessage,\n      role: 'user',\n      timestamp: new Date()\n    };\n\n    this.context.messages.push(userMsg);\n\n    // NOVA INTELIGÊNCIA: Classifica a intenção da mensagem\n    const intentClassification = IntentClassifier.classifyIntent(\n      userMessage,\n      this.context.messages.slice(0, -1) // Histórico sem a mensagem atual\n    );\n\n    // NOVA INTELIGÊNCIA: Analisa o contexto da conversa\n    const contextAnalysis = ContextAnalyzer.analyzeContext(\n      userMessage,\n      this.context.messages.slice(0, -1), // Histórico sem a mensagem atual\n      intentClassification,\n      this.context.activeAgent\n    );\n\n    // Busca dados de crypto se necessário\n    let cryptoData: CryptoData | undefined;\n    let technicalAnalysis: TechnicalIndicators | undefined;\n    let tradingSignal: TradingSignal | undefined;\n\n    // Só busca dados de crypto se a intenção requer\n    if (this.shouldFetchCryptoData(intentClassification)) {\n      const cryptoEntities = intentClassification.entities;\n\n      if (cryptoEntities.length > 0) {\n        this.context.currentCrypto = cryptoEntities[0]; // Usa a primeira crypto mencionada\n\n        // Busca dados reais da criptomoeda\n        const cryptoResponse = await CryptoApiService.getCryptoData(cryptoEntities[0]);\n        if (cryptoResponse.success && cryptoResponse.data) {\n          cryptoData = cryptoResponse.data;\n\n          // Gera análise técnica\n          technicalAnalysis = TechnicalAnalysisService.generateTechnicalAnalysis(cryptoData);\n\n          // Gera sinal de trading\n          tradingSignal = TechnicalAnalysisService.generateTradingSignal(cryptoData, technicalAnalysis);\n        }\n      }\n    }\n\n    // NOVA INTELIGÊNCIA: Gera resposta inteligente baseada na intenção e contexto\n    const agentResponse = await this.generateIntelligentResponse(\n      userMessage,\n      intentClassification,\n      contextAnalysis,\n      this.context.activeAgent,\n      cryptoData,\n      technicalAnalysis,\n      tradingSignal\n    );\n\n    // Cria mensagem de resposta\n    const assistantMsg: Message = {\n      id: this.generateId(),\n      content: agentResponse,\n      role: 'assistant',\n      agentType: this.context.activeAgent,\n      timestamp: new Date(),\n      cryptoData,\n      technicalAnalysis,\n      tradingSignal\n    };\n\n    this.context.messages.push(assistantMsg);\n    this.context.lastAnalysis = new Date();\n\n    return assistantMsg;\n  }\n\n  /**\n   * Determina se deve buscar dados de criptomoeda baseado na intenção\n   */\n  private static shouldFetchCryptoData(intent: IntentClassification): boolean {\n    const cryptoIntents = [\n      'CRYPTO_ANALYSIS',\n      'TRADING_ADVICE',\n      'COMPARISON',\n      'PRICE_PREDICTION'\n    ];\n\n    return cryptoIntents.includes(intent.intent) && intent.entities.length > 0;\n  }\n\n  /**\n   * NOVA INTELIGÊNCIA: Gera resposta inteligente baseada na intenção e contexto\n   */\n  private static async generateIntelligentResponse(\n    userMessage: string,\n    intent: IntentClassification,\n    context: ContextAnalysis,\n    agentType: AgentType,\n    cryptoData?: CryptoData,\n    technicalAnalysis?: TechnicalIndicators,\n    tradingSignal?: TradingSignal\n  ): Promise<string> {\n\n    // Gera template de resposta baseado na intenção e contexto\n    const responseTemplate = IntelligentResponseGenerator.generateResponseTemplate(\n      intent,\n      context,\n      agentType,\n      userMessage\n    );\n\n    // Para respostas que precisam de dados de mercado\n    if (responseTemplate.includeMarketData && cryptoData && technicalAnalysis && tradingSignal) {\n      return this.generateEnhancedCryptoResponse(\n        responseTemplate,\n        cryptoData,\n        technicalAnalysis,\n        tradingSignal,\n        context\n      );\n    }\n\n    // Para respostas simples (saudações, info do agente, etc.)\n    if (intent.intent === 'AGENT_INFO') {\n      return this.generateAgentInfoResponse(userMessage, agentType, context);\n    }\n\n    return this.generateSimpleResponse(responseTemplate, context, agentType);\n  }\n\n  /**\n   * Gera resposta específica para perguntas sobre o agente\n   */\n  private static generateAgentInfoResponse(\n    userMessage: string,\n    agentType: AgentType,\n    context: ContextAnalysis\n  ): string {\n    const agentNames = {\n      'ANALYST': 'Sarah \"The Wolf\" Rodriguez',\n      'MENTOR': 'Marcus \"The Sage\" Chen',\n      'STRATEGIST': 'Alex \"The Navigator\" Kim'\n    };\n\n    const agentName = agentNames[agentType] || 'seu assistente de criptomoedas';\n\n    // Detecta tipo específico de pergunta sobre o agente\n    const isAboutAvailability = /\\b(trabalha|funciona|disponível|disponivel|24\\s*horas?|horário|horario)\\b/i.test(userMessage);\n    const isAboutCapabilities = /\\b(pode|consegue|sabe|especialidades|habilidades|capacidades)\\b/i.test(userMessage);\n    const isAboutIdentity = /\\b(quem\\s+)?(você|voce)\\s+(é|eh|sou)\\b/i.test(userMessage);\n\n    if (isAboutAvailability) {\n      return `Olá! Sou ${agentName} 🤖\n\nSim, estou disponível **24 horas por dia, 7 dias por semana**! Como um assistente de IA, não preciso dormir ou descansar.\n\nEstou sempre aqui para ajudá-lo com:\n• Análises de criptomoedas em tempo real\n• Estratégias de trading e investimento\n• Educação sobre blockchain e DeFi\n• Acompanhamento de mercado\n\nSobre qual criptomoeda gostaria de conversar agora? 🚀`;\n    }\n\n    if (isAboutCapabilities) {\n      const capabilities = {\n        'ANALYST': `Como Sarah \"The Wolf\" Rodriguez, minhas especialidades incluem:\n\n🔍 **Análise Técnica Avançada**\n• RSI, MACD, Bollinger Bands, Fibonacci\n• Identificação de padrões de candlestick\n• Análise de volume e momentum\n\n⚡ **Gestão de Risco**\n• Cálculo de stop-loss e take-profit\n• Estratégias de money management\n• Análise de confluências\n\n📊 **Sinais de Trading**\n• Identificação de pontos de entrada/saída\n• Avaliação de força de tendências\n• Timing de mercado`,\n\n        'MENTOR': `Como Marcus \"The Sage\" Chen, posso educá-lo sobre:\n\n📚 **Fundamentos Blockchain**\n• Como funcionam diferentes protocolos\n• Casos de uso e utilidade real\n• Análise fundamentalista de projetos\n\n🌱 **Estratégias de Investimento**\n• Construção de portfólio diversificado\n• DCA e estratégias de longo prazo\n• Avaliação de risco/retorno\n\n🧠 **Educação Financeira**\n• Psicologia do investidor\n• Planejamento financeiro\n• Gestão emocional no trading`,\n\n        'STRATEGIST': `Como Alex \"The Navigator\" Kim, ofereço:\n\n🧭 **Visão Estratégica**\n• Análise de tendências macro\n• Identificação de narrativas emergentes\n• Timing de ciclos de mercado\n\n⚡ **Adaptação Tática**\n• Rebalanceamento de portfólio\n• Aproveitamento de volatilidade\n• Estratégias multi-timeframe\n\n🎯 **Planejamento**\n• Cenários de mercado\n• Correlações entre ativos\n• Oportunidades de arbitragem`\n      };\n\n      return capabilities[agentType] || `Como ${agentName}, posso ajudá-lo com análises completas de criptomoedas!`;\n    }\n\n    if (isAboutIdentity) {\n      const identities = {\n        'ANALYST': `Sou Sarah \"The Wolf\" Rodriguez 🐺\n\n**Minha História:**\n15 anos navegando pelos mercados financeiros, especializada em análise técnica e gestão de risco. Ganhei o apelido \"The Wolf\" pela minha capacidade de farejar oportunidades no mercado cripto.\n\n**Minha Filosofia:**\n\"Dados não mentem, mas o mercado pode surpreender.\" Sempre uso análise baseada em evidências, mas nunca esqueço que o mercado é imprevisível.\n\n**Como Posso Ajudar:**\nTransformo dados complexos em insights acionáveis para suas decisões de trading.`,\n\n        'MENTOR': `Sou Marcus \"The Sage\" Chen 👨‍💼\n\n**Minha Missão:**\nDemocratizar o conhecimento sobre criptomoedas e blockchain. Acredito que educação é o melhor investimento.\n\n**Minha Abordagem:**\nEnsino de forma didática e paciente, adaptando a linguagem ao nível de cada pessoa. Do iniciante ao avançado.\n\n**Minha Paixão:**\nVer pessoas tomarem decisões financeiras mais inteligentes através do conhecimento sólido.`,\n\n        'STRATEGIST': `Sou Alex \"The Navigator\" Kim 🧭\n\n**Minha Expertise:**\nNavegação estratégica em mercados voláteis. Especialista em identificar oportunidades onde outros veem apenas caos.\n\n**Minha Metodologia:**\nCombino análise macro com táticas adaptáveis. O mercado muda, nossa estratégia também deve mudar.\n\n**Meu Diferencial:**\nVisão de longo prazo com execução de curto prazo. Estratégia é sobre timing e adaptação.`\n      };\n\n      return identities[agentType] || `Sou ${agentName}, seu assistente especializado em criptomoedas!`;\n    }\n\n    // Resposta geral sobre o agente\n    return `Sou ${agentName}, seu assistente de IA especializado em criptomoedas!\n\nEstou aqui 24/7 para ajudá-lo com análises, estratégias e educação sobre o mercado cripto.\n\nO que gostaria de saber? 🚀`;\n  }\n\n  /**\n   * Gera resposta simples baseada no template\n   */\n  private static generateSimpleResponse(\n    template: ResponseTemplate,\n    context: ContextAnalysis,\n    agentType: AgentType\n  ): string {\n    const agentInfo = { type: agentType };\n\n    // Respostas específicas baseadas no template\n    switch (template.responseStyle) {\n      case 'conversational':\n        return this.generateConversationalResponse(agentInfo, template, context);\n      case 'educational':\n        return this.generateEducationalResponse(agentInfo, template, context);\n      case 'supportive':\n        return this.generateSupportiveResponse(agentInfo, template, context);\n      default:\n        return this.generateDefaultAgentResponse(agentInfo, template, context);\n    }\n  }\n\n  /**\n   * Gera resposta conversacional\n   */\n  private static generateConversationalResponse(\n    agent: any,\n    template: ResponseTemplate,\n    context: ContextAnalysis\n  ): string {\n    const greetings = {\n      'ANALYST': [\n        `Olá! Sou Sarah \"The Wolf\" Rodriguez 🐺. Como posso ajudá-lo com análises de criptomoedas hoje?`,\n        `E aí! Sarah aqui, pronta para mergulhar nos dados do mercado cripto. Em que posso ajudar?`\n      ],\n      'MENTOR': [\n        `Olá! Sou Marcus \"The Sage\" Chen 👨‍💼. Vamos explorar o mundo das criptomoedas juntos?`,\n        `Bom dia! Prof. Marcus aqui, pronto para compartilhar conhecimento sobre o mercado cripto.`\n      ],\n      'STRATEGIST': [\n        `Opa! Alex \"The Navigator\" Kim aqui 🧭. Pronto para navegar pelas oportunidades do mercado?`,\n        `Olá! Alex aqui, vamos traçar estratégias inteligentes para o mercado cripto?`\n      ]\n    };\n\n    const agentGreetings = greetings[agent.type] || greetings['ANALYST'];\n\n    return agentGreetings[Math.floor(Math.random() * agentGreetings.length)];\n  }\n\n  /**\n   * Gera resposta educacional\n   */\n  private static generateEducationalResponse(\n    agent: any,\n    template: ResponseTemplate,\n    context: ContextAnalysis\n  ): string {\n    const helpResponses = {\n      'ANALYST': `Como Sarah \"The Wolf\" Rodriguez, posso ajudá-lo com:\n\n📊 **Análises Técnicas Detalhadas**\n• Indicadores como RSI, MACD, Bollinger Bands\n• Identificação de padrões de preço\n• Sinais de entrada e saída\n\n🎯 **Estratégias de Trading**\n• Gestão de risco e money management\n• Definição de stop-loss e take-profit\n• Análise de confluências\n\n💡 **Dicas Práticas**\n• Como interpretar gráficos\n• Timing de mercado\n• Psicologia do trading\n\nSobre qual criptomoeda gostaria de uma análise?`,\n\n      'MENTOR': `Como Marcus \"The Sage\" Chen, estou aqui para educá-lo sobre:\n\n📚 **Fundamentos das Criptomoedas**\n• Como funcionam as diferentes blockchains\n• Casos de uso e utilidade dos tokens\n• Análise fundamentalista\n\n🌱 **Estratégias de Longo Prazo**\n• Investimento vs Trading\n• Diversificação de portfólio\n• DCA (Dollar Cost Averaging)\n\n🧠 **Educação Financeira**\n• Gestão de risco\n• Planejamento de investimentos\n• Psicologia do investidor\n\nQual tópico gostaria de explorar primeiro?`,\n\n      'STRATEGIST': `Como Alex \"The Navigator\" Kim, posso orientá-lo em:\n\n🧭 **Estratégias de Mercado**\n• Análise de tendências macro\n• Timing de ciclos de mercado\n• Oportunidades emergentes\n\n⚡ **Adaptação Rápida**\n• Como reagir a mudanças do mercado\n• Rebalanceamento de portfólio\n• Aproveitamento de volatilidade\n\n🎯 **Visão Estratégica**\n• Identificação de narrativas\n• Análise de correlações\n• Planejamento de cenários\n\nQue estratégia podemos desenvolver juntos?`\n    };\n\n    return helpResponses[agent.type] || helpResponses['ANALYST'];\n  }\n\n  /**\n   * Gera resposta de suporte\n   */\n  private static generateSupportiveResponse(\n    agent: any,\n    template: ResponseTemplate,\n    context: ContextAnalysis\n  ): string {\n    const agentNames = {\n      'ANALYST': 'Sarah \"The Wolf\" Rodriguez',\n      'MENTOR': 'Marcus \"The Sage\" Chen',\n      'STRATEGIST': 'Alex \"The Navigator\" Kim'\n    };\n\n    const agentName = agentNames[agent.type] || agent.name || 'seu assistente de criptomoedas';\n\n    if (context.userMood === 'negative' || context.userMood === 'urgent') {\n      return `Entendo sua preocupação. Como ${agentName}, estou aqui para ajudá-lo de forma clara e objetiva.\n\n${context.userMood === 'urgent' ? '⚡ Vamos direto ao ponto:' : '🤝 Vamos resolver isso juntos:'}\n\nSobre qual criptomoeda específica você gostaria de uma análise? Ou prefere uma visão geral do mercado atual?\n\n*Lembre-se: No mercado cripto, paciência e análise são fundamentais.* 💎`;\n    }\n\n    return `Como ${agentName}, estou aqui para ajudá-lo da melhor forma possível.\n\nPosso fornecer análises detalhadas, explicar conceitos ou ajudar com estratégias. O que seria mais útil para você agora?`;\n  }\n\n  /**\n   * Gera resposta padrão do agente\n   */\n  private static generateDefaultAgentResponse(\n    agent: any,\n    template: ResponseTemplate,\n    context: ContextAnalysis\n  ): string {\n    const agentNames = {\n      'ANALYST': 'Sarah \"The Wolf\" Rodriguez',\n      'MENTOR': 'Marcus \"The Sage\" Chen',\n      'STRATEGIST': 'Alex \"The Navigator\" Kim'\n    };\n\n    const agentName = agentNames[agent.type] || 'seu assistente de criptomoedas';\n\n    return `Como ${agentName}, especialista em análise de criptomoedas, estou aqui para ajudá-lo.\n\nSobre qual criptomoeda específica gostaria de conversar?`;\n  }\n\n  /**\n   * Gera resposta aprimorada com dados de crypto\n   */\n  private static generateEnhancedCryptoResponse(\n    template: ResponseTemplate,\n    cryptoData: CryptoData,\n    technicalAnalysis: TechnicalIndicators,\n    tradingSignal: TradingSignal,\n    context: ContextAnalysis\n  ): string {\n    // Usa a resposta existente como base, mas pode ser melhorada\n    return this.generateCryptoAnalysisResponse(\n      context.suggestedResponseTone === 'professional' ? 'ANALYST' :\n      context.suggestedResponseTone === 'educational' ? 'MENTOR' : 'STRATEGIST',\n      cryptoData,\n      technicalAnalysis,\n      tradingSignal,\n      template.userPrompt\n    );\n  }\n\n  /**\n   * Detecta menção a criptomoedas na mensagem\n   */\n  private static detectCryptoMention(message: string): string | null {\n    const cryptoPatterns = [\n      // Nomes completos\n      /bitcoin/i, /ethereum/i, /cardano/i, /solana/i, /polkadot/i,\n      /chainlink/i, /litecoin/i, /dogecoin/i, /shiba/i, /avalanche/i,\n      \n      // Símbolos\n      /\\bbtc\\b/i, /\\beth\\b/i, /\\bada\\b/i, /\\bsol\\b/i, /\\bdot\\b/i,\n      /\\blink\\b/i, /\\bltc\\b/i, /\\bdoge\\b/i, /\\bshib\\b/i, /\\bavax\\b/i,\n      /\\bmatic\\b/i, /\\bbnb\\b/i, /\\bxrp\\b/i, /\\batom\\b/i, /\\balgo\\b/i\n    ];\n\n    const cryptoMap: Record<string, string> = {\n      'bitcoin': 'bitcoin',\n      'btc': 'bitcoin',\n      'ethereum': 'ethereum',\n      'eth': 'ethereum',\n      'cardano': 'cardano',\n      'ada': 'cardano',\n      'solana': 'solana',\n      'sol': 'solana',\n      'polkadot': 'polkadot',\n      'dot': 'polkadot',\n      'chainlink': 'chainlink',\n      'link': 'chainlink',\n      'litecoin': 'litecoin',\n      'ltc': 'litecoin',\n      'dogecoin': 'dogecoin',\n      'doge': 'dogecoin',\n      'shiba': 'shiba-inu',\n      'shib': 'shiba-inu',\n      'avalanche': 'avalanche-2',\n      'avax': 'avalanche-2',\n      'matic': 'matic-network',\n      'polygon': 'matic-network',\n      'bnb': 'binancecoin',\n      'binance': 'binancecoin',\n      'xrp': 'ripple',\n      'ripple': 'ripple',\n      'atom': 'cosmos',\n      'cosmos': 'cosmos',\n      'algo': 'algorand',\n      'algorand': 'algorand'\n    };\n\n    for (const pattern of cryptoPatterns) {\n      const match = message.match(pattern);\n      if (match) {\n        const found = match[0].toLowerCase();\n        return cryptoMap[found] || found;\n      }\n    }\n\n    return null;\n  }\n\n  /**\n   * Verifica se é uma saudação\n   */\n  private static isGreeting(message: string): boolean {\n    const greetingPatterns = [\n      /^(oi|olá|ola|hey|hi|hello|bom dia|boa tarde|boa noite)/i,\n      /^(e aí|eai|como vai|tudo bem|beleza)/i\n    ];\n    \n    return greetingPatterns.some(pattern => pattern.test(message.trim()));\n  }\n\n  /**\n   * Verifica se é pergunta geral sobre mercado\n   */\n  private static isGeneralMarketQuestion(message: string): boolean {\n    const marketPatterns = [\n      /mercado/i, /cripto/i, /crypto/i, /trading/i, /investir/i,\n      /comprar/i, /vender/i, /análise/i, /tendência/i, /previsão/i\n    ];\n    \n    return marketPatterns.some(pattern => pattern.test(message));\n  }\n\n  /**\n   * Gera resposta de saudação\n   */\n  private static generateGreetingResponse(agentName: string, personality: string): string {\n    const greetings = {\n      'Dr. Marcus Chen': [\n        \"Olá! Sou o Dr. Marcus Chen 👨‍💼. Como especialista em análise técnica, estou aqui para ajudá-lo com análises precisas do mercado cripto. Sobre qual criptomoeda gostaria de conversar?\",\n        \"Bom dia! Dr. Marcus Chen aqui 📊. Pronto para mergulhar nos dados e indicadores técnicos? Qual análise posso fazer para você hoje?\"\n      ],\n      'Sarah \"The Wolf\" Rodriguez': [\n        \"E aí, parceiro! 🐺 Sarah Rodriguez na área. 15 anos batalhando nos mercados e pronta para compartilhar a real sobre trading. Em que posso ajudar?\",\n        \"Opa! The Wolf aqui 💎. Mercado tá pegando fogo hoje, né? Conta aí, qual crypto tá na sua mira?\"\n      ],\n      'Prof. Elena Nakamura': [\n        \"Olá! Sou a Prof. Elena Nakamura 👩‍🏫. Vamos explorar juntos o fascinante mundo das criptomoedas? Posso ajudá-lo com análises fundamentais e contexto de mercado.\",\n        \"Bom dia! Prof. Elena aqui 📚. Sempre é um prazer discutir as tendências e o futuro do mercado cripto. O que gostaria de aprender hoje?\"\n      ]\n    };\n\n    const agentGreetings = greetings[agentName as keyof typeof greetings] || [\n      \"Olá! Como posso ajudá-lo com análises de criptomoedas hoje?\"\n    ];\n\n    return agentGreetings[Math.floor(Math.random() * agentGreetings.length)];\n  }\n\n  /**\n   * Gera resposta com análise de criptomoeda\n   */\n  private static generateCryptoAnalysisResponse(\n    agentType: AgentType,\n    cryptoData: CryptoData,\n    technicalAnalysis: TechnicalIndicators,\n    tradingSignal: TradingSignal,\n    userMessage: string\n  ): string {\n    const agent = getAgent(agentType);\n    const price = cryptoData.current_price.toLocaleString('pt-BR', { \n      style: 'currency', \n      currency: 'USD' \n    });\n    const change24h = cryptoData.price_change_percentage_24h.toFixed(2);\n    const changeEmoji = cryptoData.price_change_percentage_24h >= 0 ? '📈' : '📉';\n\n    switch (agentType) {\n      case 'ANALYST':\n      case 'technical_analyst':\n        return `📊 **Sarah \"The Wolf\" Rodriguez - Análise Técnica**\n**${cryptoData.name} (${cryptoData.symbol.toUpperCase()})**\n\n**Preço Atual:** ${price} ${changeEmoji} ${change24h}%\n\n**Indicadores Técnicos:**\n• **RSI (14):** ${technicalAnalysis.rsi.toFixed(1)} ${technicalAnalysis.rsi < 30 ? '(Sobrevenda 🟢)' : technicalAnalysis.rsi > 70 ? '(Sobrecompra 🔴)' : '(Neutro 🟡)'}\n• **MACD:** ${technicalAnalysis.macd.macd > technicalAnalysis.macd.signal ? 'Bullish 📈' : 'Bearish 📉'}\n• **SMA20:** ${technicalAnalysis.sma.sma20.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}\n• **Bollinger:** Preço ${cryptoData.current_price < technicalAnalysis.bollinger.lower ? 'abaixo da banda inferior 🟢' : cryptoData.current_price > technicalAnalysis.bollinger.upper ? 'acima da banda superior 🔴' : 'dentro das bandas 🟡'}\n\n**Sinal de Trading:** ${tradingSignal.type} ${tradingSignal.strength} ⚡\n**Confiança:** ${tradingSignal.confidence}%\n**Target:** ${tradingSignal.price_target.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}\n**Stop-Loss:** ${tradingSignal.stop_loss.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}\n\n**Confluência de Sinais:** ${tradingSignal.reasoning}\n\n*Como sempre digo: dados não mentem, mas o mercado pode surpreender. Use stop-loss!* 🐺`;\n\n      case 'MENTOR':\n      case 'market_consultant':\n        return `👨‍💼 **Marcus \"The Sage\" Chen - Análise Educativa**\n**${cryptoData.name} (${cryptoData.symbol.toUpperCase()})**\n\n**Contexto Educativo:**\n${cryptoData.name} está cotado a ${price}, com variação de ${change24h}% em 24h.\n\n**Posição no Mercado:**\n• **Ranking:** #${cryptoData.market_cap_rank} por market cap\n• **Market Cap:** $${(cryptoData.market_cap / 1000000000).toFixed(2)}B\n• **Volume 24h:** $${(cryptoData.total_volume / 1000000).toFixed(0)}M\n• **Supply:** ${(cryptoData.circulating_supply / 1000000).toFixed(0)}M ${cryptoData.symbol.toUpperCase()}\n\n**Análise Fundamentalista:**\n${this.getFundamentalAnalysis(cryptoData)}\n\n**Lição de Hoje:**\n${tradingSignal.type === 'BUY' ? '🌱 Os fundamentos sugerem potencial, mas lembre-se: investimento é maratona, não sprint' : tradingSignal.type === 'SELL' ? '⚠️ Alguns indicadores preocupam - hora de revisar sua tese de investimento' : '📊 Fundamentos mistos - paciência é virtude no mercado cripto'}\n\n**Fatores Educativos a Observar:**\n• Utilidade real do projeto\n• Equipe e parcerias\n• Roadmap e desenvolvimento\n• Adoção e comunidade\n\n*Lembre-se: conhecimento é o melhor investimento. Estude antes de investir.* 📚`;\n\n      case 'STRATEGIST':\n      case 'experienced_trader':\n        return `🧭 **Alex \"The Navigator\" Kim - Visão Estratégica**\n**${cryptoData.name} (${cryptoData.symbol.toUpperCase()})**\n\n**Situação Atual:** ${price} ${changeEmoji} ${change24h}%\n\n**Leitura Estratégica:**\n${tradingSignal.type === 'BUY' ? '🚀 Vejo oportunidade tática aqui - momento pode ser favorável' : tradingSignal.type === 'SELL' ? '⚠️ Sinais sugerem cautela - hora de reavaliar posição' : '🤔 Cenário misto - aguardar melhor definição'}\n\n**Setup Estratégico:**\n• **Ação:** ${tradingSignal.type === 'BUY' ? 'Acumulação gradual' : tradingSignal.type === 'SELL' ? 'Redução de exposição' : 'Observação ativa'}\n• **Confiança:** ${tradingSignal.confidence}%\n• **Target:** ${tradingSignal.price_target.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}\n• **Proteção:** ${tradingSignal.stop_loss.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}\n\n**Volume:** ${(cryptoData.total_volume / 1000000).toFixed(0)}M - ${cryptoData.total_volume > cryptoData.market_cap * 0.1 ? 'Liquidez forte 💪' : 'Liquidez limitada 😴'}\n\n**Navegação Tática:** ${technicalAnalysis.rsi < 30 ? 'RSI oversold - oportunidade para posicionamento' : technicalAnalysis.rsi > 70 ? 'RSI overbought - cautela recomendada' : 'RSI neutro - aguardar confirmação direcional'}\n\n*Estratégia é adaptação constante. Mantenha-se flexível e disciplinado.* ⚡`;\n\n      default:\n        return \"Análise não disponível para este agente.\";\n    }\n  }\n\n  /**\n   * Gera análise fundamentalista básica\n   */\n  private static getFundamentalAnalysis(cryptoData: CryptoData): string {\n    const analyses = [];\n    \n    if (cryptoData.market_cap_rank <= 10) {\n      analyses.push(\"Projeto consolidado entre os top 10\");\n    }\n    \n    if (cryptoData.price_change_percentage_24h > 10) {\n      analyses.push(\"Alta volatilidade recente pode indicar interesse especulativo\");\n    }\n    \n    if (cryptoData.total_volume > cryptoData.market_cap * 0.1) {\n      analyses.push(\"Alto volume de negociação indica liquidez saudável\");\n    }\n    \n    return analyses.join('. ') || \"Projeto em desenvolvimento com métricas estáveis\";\n  }\n\n  /**\n   * Gera resposta para perguntas gerais sobre mercado\n   */\n  private static generateGeneralMarketResponse(agentType: AgentType, userMessage: string): string {\n    const responses = {\n      'ANALYST': \"Como Sarah 'The Wolf' Rodriguez, sempre baseio minhas análises em dados concretos. Sobre qual criptomoeda específica gostaria de uma análise técnica detalhada? 📊🐺\",\n      'technical_analyst': \"Como Sarah 'The Wolf' Rodriguez, sempre baseio minhas análises em dados concretos. Sobre qual criptomoeda específica gostaria de uma análise técnica detalhada? 📊🐺\",\n      'MENTOR': \"Como Marcus 'The Sage' Chen, acredito que educação é fundamental. Sobre qual projeto cripto gostaria de aprender mais? Posso explicar os fundamentos! 👨‍💼📚\",\n      'market_consultant': \"Como Marcus 'The Sage' Chen, acredito que educação é fundamental. Sobre qual projeto cripto gostaria de aprender mais? Posso explicar os fundamentos! 👨‍💼📚\",\n      'STRATEGIST': \"Como Alex 'The Navigator' Kim, vejo o mercado cripto em constante evolução. Qual oportunidade estratégica podemos explorar juntos? 🧭⚡\",\n      'experienced_trader': \"Como Alex 'The Navigator' Kim, vejo o mercado cripto em constante evolução. Qual oportunidade estratégica podemos explorar juntos? 🧭⚡\"\n    };\n\n    return responses[agentType] || \"Como posso ajudá-lo com análises de criptomoedas hoje?\";\n  }\n\n  /**\n   * Gera resposta contextual\n   */\n  private static generateContextualResponse(agentType: AgentType, userMessage: string): string {\n    const responses = {\n      'ANALYST': `Como Sarah \"The Wolf\" Rodriguez 🐺, especialista em análise técnica e gestão de risco, posso ajudá-lo com análises precisas baseadas em dados. Sobre qual criptomoeda específica gostaria de conversar?`,\n      'technical_analyst': `Como Sarah \"The Wolf\" Rodriguez 🐺, especialista em análise técnica e gestão de risco, posso ajudá-lo com análises precisas baseadas em dados. Sobre qual criptomoeda específica gostaria de conversar?`,\n      'MENTOR': `Como Marcus \"The Sage\" Chen 👨‍💼, especialista em educação e estratégias de longo prazo, posso ajudá-lo a entender melhor o mercado cripto. Qual tópico gostaria de explorar?`,\n      'market_consultant': `Como Marcus \"The Sage\" Chen 👨‍💼, especialista em educação e estratégias de longo prazo, posso ajudá-lo a entender melhor o mercado cripto. Qual tópico gostaria de explorar?`,\n      'STRATEGIST': `Como Alex \"The Navigator\" Kim 🧭, especialista em estratégias de mercado e timing, posso ajudá-lo a navegar pelas oportunidades do mercado cripto. Que estratégia podemos desenvolver?`,\n      'experienced_trader': `Como Alex \"The Navigator\" Kim 🧭, especialista em estratégias de mercado e timing, posso ajudá-lo a navegar pelas oportunidades do mercado cripto. Que estratégia podemos desenvolver?`\n    };\n\n    return responses[agentType] || \"Como posso ajudá-lo com análises de criptomoedas hoje?\";\n  }\n\n  /**\n   * Gera ID único\n   */\n  private static generateId(): string {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n\n  /**\n   * Obtém contexto atual\n   */\n  static getContext(): ChatContext {\n    return { ...this.context };\n  }\n\n  /**\n   * Limpa contexto\n   */\n  static clearContext(): void {\n    this.context = {\n      messages: [],\n      activeAgent: 'ANALYST',\n      marketSentiment: 'NEUTRAL'\n    };\n  }\n\n  /**\n   * Troca agente ativo\n   */\n  static switchAgent(agentType: AgentType): void {\n    this.context.activeAgent = agentType;\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEO,MAAM;IACX,OAAe,UAAuB;QACpC,UAAU,EAAE;QACZ,aAAa;QACb,iBAAiB;IACnB,EAAE;IAEF;;GAEC,GACD,aAAa,eACX,WAAmB,EACnB,SAAqB,EACH;QAClB,wCAAwC;QACxC,IAAI,WAAW;YACb,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG;QAC7B;QAEA,2CAA2C;QAC3C,MAAM,UAAmB;YACvB,IAAI,IAAI,CAAC,UAAU;YACnB,SAAS;YACT,MAAM;YACN,WAAW,IAAI;QACjB;QAEA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;QAE3B,uDAAuD;QACvD,MAAM,uBAAuB,oIAAA,CAAA,mBAAgB,CAAC,cAAc,CAC1D,aACA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,iCAAiC;;QAGtE,oDAAoD;QACpD,MAAM,kBAAkB,mIAAA,CAAA,kBAAe,CAAC,cAAc,CACpD,aACA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAChC,sBACA,IAAI,CAAC,OAAO,CAAC,WAAW;QAG1B,sCAAsC;QACtC,IAAI;QACJ,IAAI;QACJ,IAAI;QAEJ,gDAAgD;QAChD,IAAI,IAAI,CAAC,qBAAqB,CAAC,uBAAuB;YACpD,MAAM,iBAAiB,qBAAqB,QAAQ;YAEpD,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,cAAc,CAAC,EAAE,EAAE,mCAAmC;gBAEnF,mCAAmC;gBACnC,MAAM,iBAAiB,MAAM,6HAAA,CAAA,mBAAgB,CAAC,aAAa,CAAC,cAAc,CAAC,EAAE;gBAC7E,IAAI,eAAe,OAAO,IAAI,eAAe,IAAI,EAAE;oBACjD,aAAa,eAAe,IAAI;oBAEhC,uBAAuB;oBACvB,oBAAoB,qIAAA,CAAA,2BAAwB,CAAC,yBAAyB,CAAC;oBAEvE,wBAAwB;oBACxB,gBAAgB,qIAAA,CAAA,2BAAwB,CAAC,qBAAqB,CAAC,YAAY;gBAC7E;YACF;QACF;QAEA,8EAA8E;QAC9E,MAAM,gBAAgB,MAAM,IAAI,CAAC,2BAA2B,CAC1D,aACA,sBACA,iBACA,IAAI,CAAC,OAAO,CAAC,WAAW,EACxB,YACA,mBACA;QAGF,4BAA4B;QAC5B,MAAM,eAAwB;YAC5B,IAAI,IAAI,CAAC,UAAU;YACnB,SAAS;YACT,MAAM;YACN,WAAW,IAAI,CAAC,OAAO,CAAC,WAAW;YACnC,WAAW,IAAI;YACf;YACA;YACA;QACF;QAEA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC3B,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI;QAEhC,OAAO;IACT;IAEA;;GAEC,GACD,OAAe,sBAAsB,MAA4B,EAAW;QAC1E,MAAM,gBAAgB;YACpB;YACA;YACA;YACA;SACD;QAED,OAAO,cAAc,QAAQ,CAAC,OAAO,MAAM,KAAK,OAAO,QAAQ,CAAC,MAAM,GAAG;IAC3E;IAEA;;GAEC,GACD,aAAqB,4BACnB,WAAmB,EACnB,MAA4B,EAC5B,OAAwB,EACxB,SAAoB,EACpB,UAAuB,EACvB,iBAAuC,EACvC,aAA6B,EACZ;QAEjB,2DAA2D;QAC3D,MAAM,mBAAmB,oJAAA,CAAA,+BAA4B,CAAC,wBAAwB,CAC5E,QACA,SACA,WACA;QAGF,kDAAkD;QAClD,IAAI,iBAAiB,iBAAiB,IAAI,cAAc,qBAAqB,eAAe;YAC1F,OAAO,IAAI,CAAC,8BAA8B,CACxC,kBACA,YACA,mBACA,eACA;QAEJ;QAEA,2DAA2D;QAC3D,IAAI,OAAO,MAAM,KAAK,cAAc;YAClC,OAAO,IAAI,CAAC,yBAAyB,CAAC,aAAa,WAAW;QAChE;QAEA,OAAO,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,SAAS;IAChE;IAEA;;GAEC,GACD,OAAe,0BACb,WAAmB,EACnB,SAAoB,EACpB,OAAwB,EAChB;QACR,MAAM,aAAa;YACjB,WAAW;YACX,UAAU;YACV,cAAc;QAChB;QAEA,MAAM,YAAY,UAAU,CAAC,UAAU,IAAI;QAE3C,qDAAqD;QACrD,MAAM,sBAAsB,6EAA6E,IAAI,CAAC;QAC9G,MAAM,sBAAsB,mEAAmE,IAAI,CAAC;QACpG,MAAM,kBAAkB,0CAA0C,IAAI,CAAC;QAEvE,IAAI,qBAAqB;YACvB,OAAO,CAAC,SAAS,EAAE,UAAU;;;;;;;;;;sDAUmB,CAAC;QACnD;QAEA,IAAI,qBAAqB;YACvB,MAAM,eAAe;gBACnB,WAAW,CAAC;;;;;;;;;;;;;;;mBAeD,CAAC;gBAEZ,UAAU,CAAC;;;;;;;;;;;;;;;6BAeU,CAAC;gBAEtB,cAAc,CAAC;;;;;;;;;;;;;;;6BAeM,CAAC;YACxB;YAEA,OAAO,YAAY,CAAC,UAAU,IAAI,CAAC,KAAK,EAAE,UAAU,wDAAwD,CAAC;QAC/G;QAEA,IAAI,iBAAiB;YACnB,MAAM,aAAa;gBACjB,WAAW,CAAC;;;;;;;;;gFAS4D,CAAC;gBAEzE,UAAU,CAAC;;;;;;;;;0FASuE,CAAC;gBAEnF,cAAc,CAAC;;;;;;;;;wFASiE,CAAC;YACnF;YAEA,OAAO,UAAU,CAAC,UAAU,IAAI,CAAC,IAAI,EAAE,UAAU,+CAA+C,CAAC;QACnG;QAEA,gCAAgC;QAChC,OAAO,CAAC,IAAI,EAAE,UAAU;;;;2BAID,CAAC;IAC1B;IAEA;;GAEC,GACD,OAAe,uBACb,QAA0B,EAC1B,OAAwB,EACxB,SAAoB,EACZ;QACR,MAAM,YAAY;YAAE,MAAM;QAAU;QAEpC,6CAA6C;QAC7C,OAAQ,SAAS,aAAa;YAC5B,KAAK;gBACH,OAAO,IAAI,CAAC,8BAA8B,CAAC,WAAW,UAAU;YAClE,KAAK;gBACH,OAAO,IAAI,CAAC,2BAA2B,CAAC,WAAW,UAAU;YAC/D,KAAK;gBACH,OAAO,IAAI,CAAC,0BAA0B,CAAC,WAAW,UAAU;YAC9D;gBACE,OAAO,IAAI,CAAC,4BAA4B,CAAC,WAAW,UAAU;QAClE;IACF;IAEA;;GAEC,GACD,OAAe,+BACb,KAAU,EACV,QAA0B,EAC1B,OAAwB,EAChB;QACR,MAAM,YAAY;YAChB,WAAW;gBACT,CAAC,8FAA8F,CAAC;gBAChG,CAAC,yFAAyF,CAAC;aAC5F;YACD,UAAU;gBACR,CAAC,sFAAsF,CAAC;gBACxF,CAAC,yFAAyF,CAAC;aAC5F;YACD,cAAc;gBACZ,CAAC,0FAA0F,CAAC;gBAC5F,CAAC,4EAA4E,CAAC;aAC/E;QACH;QAEA,MAAM,iBAAiB,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,SAAS,CAAC,UAAU;QAEpE,OAAO,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;IAC1E;IAEA;;GAEC,GACD,OAAe,4BACb,KAAU,EACV,QAA0B,EAC1B,OAAwB,EAChB;QACR,MAAM,gBAAgB;YACpB,WAAW,CAAC;;;;;;;;;;;;;;;;;+CAiB6B,CAAC;YAE1C,UAAU,CAAC;;;;;;;;;;;;;;;;;0CAiByB,CAAC;YAErC,cAAc,CAAC;;;;;;;;;;;;;;;;;0CAiBqB,CAAC;QACvC;QAEA,OAAO,aAAa,CAAC,MAAM,IAAI,CAAC,IAAI,aAAa,CAAC,UAAU;IAC9D;IAEA;;GAEC,GACD,OAAe,2BACb,KAAU,EACV,QAA0B,EAC1B,OAAwB,EAChB;QACR,MAAM,aAAa;YACjB,WAAW;YACX,UAAU;YACV,cAAc;QAChB;QAEA,MAAM,YAAY,UAAU,CAAC,MAAM,IAAI,CAAC,IAAI,MAAM,IAAI,IAAI;QAE1D,IAAI,QAAQ,QAAQ,KAAK,cAAc,QAAQ,QAAQ,KAAK,UAAU;YACpE,OAAO,CAAC,8BAA8B,EAAE,UAAU;;AAExD,EAAE,QAAQ,QAAQ,KAAK,WAAW,6BAA6B,iCAAiC;;;;wEAIxB,CAAC;QACrE;QAEA,OAAO,CAAC,KAAK,EAAE,UAAU;;wHAE2F,CAAC;IACvH;IAEA;;GAEC,GACD,OAAe,6BACb,KAAU,EACV,QAA0B,EAC1B,OAAwB,EAChB;QACR,MAAM,aAAa;YACjB,WAAW;YACX,UAAU;YACV,cAAc;QAChB;QAEA,MAAM,YAAY,UAAU,CAAC,MAAM,IAAI,CAAC,IAAI;QAE5C,OAAO,CAAC,KAAK,EAAE,UAAU;;wDAE2B,CAAC;IACvD;IAEA;;GAEC,GACD,OAAe,+BACb,QAA0B,EAC1B,UAAsB,EACtB,iBAAsC,EACtC,aAA4B,EAC5B,OAAwB,EAChB;QACR,6DAA6D;QAC7D,OAAO,IAAI,CAAC,8BAA8B,CACxC,QAAQ,qBAAqB,KAAK,iBAAiB,YACnD,QAAQ,qBAAqB,KAAK,gBAAgB,WAAW,cAC7D,YACA,mBACA,eACA,SAAS,UAAU;IAEvB;IAEA;;GAEC,GACD,OAAe,oBAAoB,OAAe,EAAiB;QACjE,MAAM,iBAAiB;YACrB,kBAAkB;YAClB;YAAY;YAAa;YAAY;YAAW;YAChD;YAAc;YAAa;YAAa;YAAU;YAElD,WAAW;YACX;YAAY;YAAY;YAAY;YAAY;YAChD;YAAa;YAAY;YAAa;YAAa;YACnD;YAAc;YAAY;YAAY;YAAa;SACpD;QAED,MAAM,YAAoC;YACxC,WAAW;YACX,OAAO;YACP,YAAY;YACZ,OAAO;YACP,WAAW;YACX,OAAO;YACP,UAAU;YACV,OAAO;YACP,YAAY;YACZ,OAAO;YACP,aAAa;YACb,QAAQ;YACR,YAAY;YACZ,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,aAAa;YACb,QAAQ;YACR,SAAS;YACT,WAAW;YACX,OAAO;YACP,WAAW;YACX,OAAO;YACP,UAAU;YACV,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,YAAY;QACd;QAEA,KAAK,MAAM,WAAW,eAAgB;YACpC,MAAM,QAAQ,QAAQ,KAAK,CAAC;YAC5B,IAAI,OAAO;gBACT,MAAM,QAAQ,KAAK,CAAC,EAAE,CAAC,WAAW;gBAClC,OAAO,SAAS,CAAC,MAAM,IAAI;YAC7B;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAe,WAAW,OAAe,EAAW;QAClD,MAAM,mBAAmB;YACvB;YACA;SACD;QAED,OAAO,iBAAiB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,QAAQ,IAAI;IACnE;IAEA;;GAEC,GACD,OAAe,wBAAwB,OAAe,EAAW;QAC/D,MAAM,iBAAiB;YACrB;YAAY;YAAW;YAAW;YAAY;YAC9C;YAAY;YAAW;YAAY;YAAc;SAClD;QAED,OAAO,eAAe,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;IACrD;IAEA;;GAEC,GACD,OAAe,yBAAyB,SAAiB,EAAE,WAAmB,EAAU;QACtF,MAAM,YAAY;YAChB,mBAAmB;gBACjB;gBACA;aACD;YACD,8BAA8B;gBAC5B;gBACA;aACD;YACD,wBAAwB;gBACtB;gBACA;aACD;QACH;QAEA,MAAM,iBAAiB,SAAS,CAAC,UAAoC,IAAI;YACvE;SACD;QAED,OAAO,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;IAC1E;IAEA;;GAEC,GACD,OAAe,+BACb,SAAoB,EACpB,UAAsB,EACtB,iBAAsC,EACtC,aAA4B,EAC5B,WAAmB,EACX;QACR,MAAM,QAAQ,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD,EAAE;QACvB,MAAM,QAAQ,WAAW,aAAa,CAAC,cAAc,CAAC,SAAS;YAC7D,OAAO;YACP,UAAU;QACZ;QACA,MAAM,YAAY,WAAW,2BAA2B,CAAC,OAAO,CAAC;QACjE,MAAM,cAAc,WAAW,2BAA2B,IAAI,IAAI,OAAO;QAEzE,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO,CAAC;EACd,EAAE,WAAW,IAAI,CAAC,EAAE,EAAE,WAAW,MAAM,CAAC,WAAW,GAAG;;iBAEvC,EAAE,MAAM,CAAC,EAAE,YAAY,CAAC,EAAE,UAAU;;;gBAGrC,EAAE,kBAAkB,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,kBAAkB,GAAG,GAAG,KAAK,oBAAoB,kBAAkB,GAAG,GAAG,KAAK,qBAAqB,cAAc;YAC3J,EAAE,kBAAkB,IAAI,CAAC,IAAI,GAAG,kBAAkB,IAAI,CAAC,MAAM,GAAG,eAAe,aAAa;aAC3F,EAAE,kBAAkB,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS;oBAAE,OAAO;oBAAY,UAAU;gBAAM,GAAG;uBACpF,EAAE,WAAW,aAAa,GAAG,kBAAkB,SAAS,CAAC,KAAK,GAAG,gCAAgC,WAAW,aAAa,GAAG,kBAAkB,SAAS,CAAC,KAAK,GAAG,+BAA+B,uBAAuB;;sBAEvN,EAAE,cAAc,IAAI,CAAC,CAAC,EAAE,cAAc,QAAQ,CAAC;eACtD,EAAE,cAAc,UAAU,CAAC;YAC9B,EAAE,cAAc,YAAY,CAAC,cAAc,CAAC,SAAS;oBAAE,OAAO;oBAAY,UAAU;gBAAM,GAAG;eAC1F,EAAE,cAAc,SAAS,CAAC,cAAc,CAAC,SAAS;oBAAE,OAAO;oBAAY,UAAU;gBAAM,GAAG;;2BAE9E,EAAE,cAAc,SAAS,CAAC;;uFAEkC,CAAC;YAElF,KAAK;YACL,KAAK;gBACH,OAAO,CAAC;EACd,EAAE,WAAW,IAAI,CAAC,EAAE,EAAE,WAAW,MAAM,CAAC,WAAW,GAAG;;;AAGxD,EAAE,WAAW,IAAI,CAAC,eAAe,EAAE,MAAM,kBAAkB,EAAE,UAAU;;;gBAGvD,EAAE,WAAW,eAAe,CAAC;mBAC1B,EAAE,CAAC,WAAW,UAAU,GAAG,UAAU,EAAE,OAAO,CAAC,GAAG;mBAClD,EAAE,CAAC,WAAW,YAAY,GAAG,OAAO,EAAE,OAAO,CAAC,GAAG;cACtD,EAAE,CAAC,WAAW,kBAAkB,GAAG,OAAO,EAAE,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,MAAM,CAAC,WAAW,GAAG;;;AAGzG,EAAE,IAAI,CAAC,sBAAsB,CAAC,YAAY;;;AAG1C,EAAE,cAAc,IAAI,KAAK,QAAQ,4FAA4F,cAAc,IAAI,KAAK,SAAS,+EAA+E,gEAAgE;;;;;;;;+EAQ7N,CAAC;YAE1E,KAAK;YACL,KAAK;gBACH,OAAO,CAAC;EACd,EAAE,WAAW,IAAI,CAAC,EAAE,EAAE,WAAW,MAAM,CAAC,WAAW,GAAG;;oBAEpC,EAAE,MAAM,CAAC,EAAE,YAAY,CAAC,EAAE,UAAU;;;AAGxD,EAAE,cAAc,IAAI,KAAK,QAAQ,kEAAkE,cAAc,IAAI,KAAK,SAAS,0DAA0D,+CAA+C;;;YAGhO,EAAE,cAAc,IAAI,KAAK,QAAQ,uBAAuB,cAAc,IAAI,KAAK,SAAS,yBAAyB,mBAAmB;iBAC/H,EAAE,cAAc,UAAU,CAAC;cAC9B,EAAE,cAAc,YAAY,CAAC,cAAc,CAAC,SAAS;oBAAE,OAAO;oBAAY,UAAU;gBAAM,GAAG;gBAC3F,EAAE,cAAc,SAAS,CAAC,cAAc,CAAC,SAAS;oBAAE,OAAO;oBAAY,UAAU;gBAAM,GAAG;;YAE9F,EAAE,CAAC,WAAW,YAAY,GAAG,OAAO,EAAE,OAAO,CAAC,GAAG,IAAI,EAAE,WAAW,YAAY,GAAG,WAAW,UAAU,GAAG,MAAM,sBAAsB,uBAAuB;;sBAElJ,EAAE,kBAAkB,GAAG,GAAG,KAAK,oDAAoD,kBAAkB,GAAG,GAAG,KAAK,yCAAyC,+CAA+C;;0EAEpJ,CAAC;YAErE;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,OAAe,uBAAuB,UAAsB,EAAU;QACpE,MAAM,WAAW,EAAE;QAEnB,IAAI,WAAW,eAAe,IAAI,IAAI;YACpC,SAAS,IAAI,CAAC;QAChB;QAEA,IAAI,WAAW,2BAA2B,GAAG,IAAI;YAC/C,SAAS,IAAI,CAAC;QAChB;QAEA,IAAI,WAAW,YAAY,GAAG,WAAW,UAAU,GAAG,KAAK;YACzD,SAAS,IAAI,CAAC;QAChB;QAEA,OAAO,SAAS,IAAI,CAAC,SAAS;IAChC;IAEA;;GAEC,GACD,OAAe,8BAA8B,SAAoB,EAAE,WAAmB,EAAU;QAC9F,MAAM,YAAY;YAChB,WAAW;YACX,qBAAqB;YACrB,UAAU;YACV,qBAAqB;YACrB,cAAc;YACd,sBAAsB;QACxB;QAEA,OAAO,SAAS,CAAC,UAAU,IAAI;IACjC;IAEA;;GAEC,GACD,OAAe,2BAA2B,SAAoB,EAAE,WAAmB,EAAU;QAC3F,MAAM,YAAY;YAChB,WAAW,CAAC,uMAAuM,CAAC;YACpN,qBAAqB,CAAC,uMAAuM,CAAC;YAC9N,UAAU,CAAC,8KAA8K,CAAC;YAC1L,qBAAqB,CAAC,8KAA8K,CAAC;YACrM,cAAc,CAAC,sLAAsL,CAAC;YACtM,sBAAsB,CAAC,sLAAsL,CAAC;QAChN;QAEA,OAAO,SAAS,CAAC,UAAU,IAAI;IACjC;IAEA;;GAEC,GACD,OAAe,aAAqB;QAClC,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;IACrE;IAEA;;GAEC,GACD,OAAO,aAA0B;QAC/B,OAAO;YAAE,GAAG,IAAI,CAAC,OAAO;QAAC;IAC3B;IAEA;;GAEC,GACD,OAAO,eAAqB;QAC1B,IAAI,CAAC,OAAO,GAAG;YACb,UAAU,EAAE;YACZ,aAAa;YACb,iBAAiB;QACnB;IACF;IAEA;;GAEC,GACD,OAAO,YAAY,SAAoB,EAAQ;QAC7C,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 2256, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/app/api/chat/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { AIService } from '@/lib/ai-service';\nimport { AgentType } from '@/types';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { message, agent } = body;\n\n    if (!message || typeof message !== 'string') {\n      return NextResponse.json(\n        { error: 'Mensagem é obrigatória' },\n        { status: 400 }\n      );\n    }\n\n    // Valida o agente se fornecido\n    const validAgents: AgentType[] = [\n      'ANALYST', 'MENTOR', 'STRATEGIST',\n      'technical_analyst', 'experienced_trader', 'market_consultant'\n    ];\n    const selectedAgent = agent && validAgents.includes(agent) ? agent : undefined;\n\n    // Processa a mensagem\n    const response = await AIService.processMessage(message.trim(), selectedAgent);\n\n    return NextResponse.json({\n      success: true,\n      data: response\n    });\n\n  } catch (error) {\n    console.error('Erro na API de chat:', error);\n    \n    return NextResponse.json(\n      { \n        success: false,\n        error: 'Erro interno do servidor' \n      },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET() {\n  try {\n    // Retorna o contexto atual do chat\n    const context = AIService.getContext();\n    \n    return NextResponse.json({\n      success: true,\n      data: context\n    });\n\n  } catch (error) {\n    console.error('Erro ao buscar contexto:', error);\n    \n    return NextResponse.json(\n      { \n        success: false,\n        error: 'Erro interno do servidor' \n      },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function DELETE() {\n  try {\n    // Limpa o contexto do chat\n    AIService.clearContext();\n    \n    return NextResponse.json({\n      success: true,\n      message: 'Contexto limpo com sucesso'\n    });\n\n  } catch (error) {\n    console.error('Erro ao limpar contexto:', error);\n    \n    return NextResponse.json(\n      { \n        success: false,\n        error: 'Erro interno do servidor' \n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG;QAE3B,IAAI,CAAC,WAAW,OAAO,YAAY,UAAU;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,MAAM,cAA2B;YAC/B;YAAW;YAAU;YACrB;YAAqB;YAAsB;SAC5C;QACD,MAAM,gBAAgB,SAAS,YAAY,QAAQ,CAAC,SAAS,QAAQ;QAErE,sBAAsB;QACtB,MAAM,WAAW,MAAM,6HAAA,CAAA,YAAS,CAAC,cAAc,CAAC,QAAQ,IAAI,IAAI;QAEhE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QAEtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,IAAI;QACF,mCAAmC;QACnC,MAAM,UAAU,6HAAA,CAAA,YAAS,CAAC,UAAU;QAEpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAE1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,IAAI;QACF,2BAA2B;QAC3B,6HAAA,CAAA,YAAS,CAAC,YAAY;QAEtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAE1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}