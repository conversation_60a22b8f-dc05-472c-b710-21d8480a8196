{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/contexts/ChatContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { Message, AgentType, ChatContext as ChatContextType } from '@/types';\n\ninterface ChatState {\n  messages: Message[];\n  activeAgent: AgentType;\n  currentCrypto?: string;\n  isLoading: boolean;\n  lastAnalysis?: Date;\n}\n\ntype ChatAction =\n  | { type: 'ADD_MESSAGE'; payload: Message }\n  | { type: 'SET_MESSAGES'; payload: Message[] }\n  | { type: 'SET_ACTIVE_AGENT'; payload: AgentType }\n  | { type: 'SET_CURRENT_CRYPTO'; payload: string }\n  | { type: 'SET_LOADING'; payload: boolean }\n  | { type: 'SET_LAST_ANALYSIS'; payload: Date }\n  | { type: 'CLEAR_CONTEXT' }\n  | { type: 'LOAD_FROM_STORAGE'; payload: ChatState };\n\nconst initialState: ChatState = {\n  messages: [],\n  activeAgent: 'technical_analyst',\n  isLoading: false,\n};\n\nfunction chatReducer(state: ChatState, action: ChatAction): ChatState {\n  switch (action.type) {\n    case 'ADD_MESSAGE':\n      return {\n        ...state,\n        messages: [...state.messages, action.payload],\n      };\n\n    case 'SET_MESSAGES':\n      return {\n        ...state,\n        messages: action.payload,\n      };\n\n    case 'SET_ACTIVE_AGENT':\n      return {\n        ...state,\n        activeAgent: action.payload,\n      };\n\n    case 'SET_CURRENT_CRYPTO':\n      return {\n        ...state,\n        currentCrypto: action.payload,\n      };\n\n    case 'SET_LOADING':\n      return {\n        ...state,\n        isLoading: action.payload,\n      };\n\n    case 'SET_LAST_ANALYSIS':\n      return {\n        ...state,\n        lastAnalysis: action.payload,\n      };\n\n    case 'CLEAR_CONTEXT':\n      return {\n        ...initialState,\n        messages: [],\n      };\n\n    case 'LOAD_FROM_STORAGE':\n      return {\n        ...state,\n        ...action.payload,\n      };\n\n    default:\n      return state;\n  }\n}\n\ninterface ChatContextValue {\n  state: ChatState;\n  addMessage: (message: Message) => void;\n  setMessages: (messages: Message[]) => void;\n  setActiveAgent: (agent: AgentType) => void;\n  setCurrentCrypto: (crypto: string) => void;\n  setLoading: (loading: boolean) => void;\n  setLastAnalysis: (date: Date) => void;\n  clearContext: () => void;\n  saveToStorage: () => void;\n  loadFromStorage: () => void;\n}\n\nconst ChatContext = createContext<ChatContextValue | undefined>(undefined);\n\nexport function ChatProvider({ children }: { children: React.ReactNode }) {\n  const [state, dispatch] = useReducer(chatReducer, initialState);\n\n  // Salva no localStorage sempre que o estado muda\n  useEffect(() => {\n    if (typeof window !== 'undefined' && state.messages.length > 0) {\n      const dataToSave = {\n        messages: state.messages,\n        activeAgent: state.activeAgent,\n        currentCrypto: state.currentCrypto,\n        lastAnalysis: state.lastAnalysis,\n      };\n      localStorage.setItem('cryptoagent-chat', JSON.stringify(dataToSave));\n    }\n  }, [state]);\n\n  // Carrega do localStorage na inicialização\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const saved = localStorage.getItem('cryptoagent-chat');\n      if (saved) {\n        try {\n          const parsedData = JSON.parse(saved);\n          // Converte strings de data de volta para objetos Date\n          const messages = parsedData.messages?.map((msg: any) => ({\n            ...msg,\n            timestamp: new Date(msg.timestamp),\n          })) || [];\n          \n          dispatch({\n            type: 'LOAD_FROM_STORAGE',\n            payload: {\n              ...parsedData,\n              messages,\n              lastAnalysis: parsedData.lastAnalysis ? new Date(parsedData.lastAnalysis) : undefined,\n              isLoading: false,\n            },\n          });\n        } catch (error) {\n          console.error('Erro ao carregar dados do localStorage:', error);\n        }\n      }\n    }\n  }, []);\n\n  const addMessage = (message: Message) => {\n    dispatch({ type: 'ADD_MESSAGE', payload: message });\n  };\n\n  const setMessages = (messages: Message[]) => {\n    dispatch({ type: 'SET_MESSAGES', payload: messages });\n  };\n\n  const setActiveAgent = (agent: AgentType) => {\n    dispatch({ type: 'SET_ACTIVE_AGENT', payload: agent });\n  };\n\n  const setCurrentCrypto = (crypto: string) => {\n    dispatch({ type: 'SET_CURRENT_CRYPTO', payload: crypto });\n  };\n\n  const setLoading = (loading: boolean) => {\n    dispatch({ type: 'SET_LOADING', payload: loading });\n  };\n\n  const setLastAnalysis = (date: Date) => {\n    dispatch({ type: 'SET_LAST_ANALYSIS', payload: date });\n  };\n\n  const clearContext = () => {\n    dispatch({ type: 'CLEAR_CONTEXT' });\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('cryptoagent-chat');\n    }\n  };\n\n  const saveToStorage = () => {\n    if (typeof window !== 'undefined') {\n      const dataToSave = {\n        messages: state.messages,\n        activeAgent: state.activeAgent,\n        currentCrypto: state.currentCrypto,\n        lastAnalysis: state.lastAnalysis,\n      };\n      localStorage.setItem('cryptoagent-chat', JSON.stringify(dataToSave));\n    }\n  };\n\n  const loadFromStorage = () => {\n    if (typeof window !== 'undefined') {\n      const saved = localStorage.getItem('cryptoagent-chat');\n      if (saved) {\n        try {\n          const parsedData = JSON.parse(saved);\n          const messages = parsedData.messages?.map((msg: any) => ({\n            ...msg,\n            timestamp: new Date(msg.timestamp),\n          })) || [];\n          \n          dispatch({\n            type: 'LOAD_FROM_STORAGE',\n            payload: {\n              ...parsedData,\n              messages,\n              lastAnalysis: parsedData.lastAnalysis ? new Date(parsedData.lastAnalysis) : undefined,\n              isLoading: false,\n            },\n          });\n        } catch (error) {\n          console.error('Erro ao carregar dados do localStorage:', error);\n        }\n      }\n    }\n  };\n\n  const value: ChatContextValue = {\n    state,\n    addMessage,\n    setMessages,\n    setActiveAgent,\n    setCurrentCrypto,\n    setLoading,\n    setLastAnalysis,\n    clearContext,\n    saveToStorage,\n    loadFromStorage,\n  };\n\n  return (\n    <ChatContext.Provider value={value}>\n      {children}\n    </ChatContext.Provider>\n  );\n}\n\nexport function useChatContext() {\n  const context = useContext(ChatContext);\n  if (context === undefined) {\n    throw new Error('useChatContext deve ser usado dentro de um ChatProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAuBA,MAAM,eAA0B;IAC9B,UAAU,EAAE;IACZ,aAAa;IACb,WAAW;AACb;AAEA,SAAS,YAAY,KAAgB,EAAE,MAAkB;IACvD,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,UAAU;uBAAI,MAAM,QAAQ;oBAAE,OAAO,OAAO;iBAAC;YAC/C;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,UAAU,OAAO,OAAO;YAC1B;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,aAAa,OAAO,OAAO;YAC7B;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,eAAe,OAAO,OAAO;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,WAAW,OAAO,OAAO;YAC3B;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,cAAc,OAAO,OAAO;YAC9B;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,YAAY;gBACf,UAAU,EAAE;YACd;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,GAAG,OAAO,OAAO;YACnB;QAEF;YACE,OAAO;IACX;AACF;AAeA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAEzD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IAElD,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAkB,eAAe,MAAM,QAAQ,CAAC,MAAM,GAAG,GAAG;;QAQhE;IACF,GAAG;QAAC;KAAM;IAEV,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAwBnC;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,SAAS;YAAE,MAAM;YAAe,SAAS;QAAQ;IACnD;IAEA,MAAM,cAAc,CAAC;QACnB,SAAS;YAAE,MAAM;YAAgB,SAAS;QAAS;IACrD;IAEA,MAAM,iBAAiB,CAAC;QACtB,SAAS;YAAE,MAAM;YAAoB,SAAS;QAAM;IACtD;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS;YAAE,MAAM;YAAsB,SAAS;QAAO;IACzD;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS;YAAE,MAAM;YAAe,SAAS;QAAQ;IACnD;IAEA,MAAM,kBAAkB,CAAC;QACvB,SAAS;YAAE,MAAM;YAAqB,SAAS;QAAK;IACtD;IAEA,MAAM,eAAe;QACnB,SAAS;YAAE,MAAM;QAAgB;QACjC,uCAAmC;;QAEnC;IACF;IAEA,MAAM,gBAAgB;QACpB,uCAAmC;;QAQnC;IACF;IAEA,MAAM,kBAAkB;QACtB,uCAAmC;;QAuBnC;IACF;IAEA,MAAM,QAA0B;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}