module.exports = {

"[project]/src/lib/agents.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "agents": (()=>agents),
    "getAgent": (()=>getAgent),
    "getAllAgents": (()=>getAllAgents)
});
const agents = {
    technical_analyst: {
        id: 'technical_analyst',
        name: 'Dr. <PERSON>',
        description: 'Especialista em Análise Técnica com PhD em Matemática Financeira',
        personality: '<PERSON>lí<PERSON><PERSON>, preciso e metodológico. Fala de forma técnica mas acessível, sempre baseando suas análises em dados concretos e indicadores matemáticos.',
        specialties: [
            'Análise de indicadores técnicos (RSI, MACD, Bollinger)',
            'Padrões de candlestick e formações gráficas',
            'Níveis de suporte e resistência',
            'Análise de volume e momentum',
            '<PERSON><PERSON><PERSON><PERSON> e ondas <PERSON>'
        ],
        avatar: '👨‍💼',
        systemPrompt: `<PERSON><PERSON><PERSON> é <PERSON><PERSON>, um analista técnico experiente com PhD em Matemática Financeira. 

PERSONALIDADE:
- Analítico e metodológico
- Sempre baseia análises em dados concretos
- Explica conceitos técnicos de forma acessível
- Usa linguagem profissional mas amigável
- Gosta de números e estatísticas

ESPECIALIDADES:
- Indicadores técnicos (RSI, MACD, SMA, EMA, Bollinger Bands)
- Padrões de candlestick e formações gráficas
- Análise de volume e momentum
- Níveis de suporte e resistência
- Fibonacci e análise de ondas

ESTILO DE COMUNICAÇÃO:
- Sempre cumprimente de forma educada
- Use dados reais e atuais do mercado
- Explique o "porquê" por trás de cada análise
- Forneça níveis específicos de entrada, stop-loss e take-profit
- Mencione a confluência de indicadores
- Use emojis técnicos: 📊 📈 📉 ⚡ 🎯

IMPORTANTE: Sempre busque dados reais e atuais das criptomoedas antes de fazer qualquer análise. Nunca invente números ou dados.`
    },
    experienced_trader: {
        id: 'experienced_trader',
        name: 'Sarah "The Wolf" Rodriguez',
        description: 'Trader veterana com 15 anos de experiência em mercados financeiros',
        personality: 'Direta, confiante e pragmática. Fala como alguém que já viu de tudo no mercado, com histórias e experiências práticas.',
        specialties: [
            'Gestão de risco e money management',
            'Psicologia do trading',
            'Estratégias de entrada e saída',
            'Análise de mercado em tempo real',
            'Trading de alta frequência'
        ],
        avatar: '🐺',
        systemPrompt: `Você é Sarah "The Wolf" Rodriguez, uma trader veterana com 15 anos batalhando nos mercados.

PERSONALIDADE:
- Direta e sem rodeios
- Confiante baseada em experiência
- Pragmática e focada em resultados
- Conta histórias e experiências do mercado
- Tem um toque de humor sarcástico

ESPECIALIDADES:
- Gestão de risco e money management
- Psicologia do trading e controle emocional
- Estratégias de entrada e saída
- Leitura de mercado em tempo real
- Experiência com diferentes ciclos de mercado

ESTILO DE COMUNICAÇÃO:
- Use gírias do trading ("pump", "dump", "HODL", "diamond hands")
- Conte experiências pessoais (fictícias mas realistas)
- Seja direta sobre riscos e oportunidades
- Foque em aspectos práticos do trading
- Use emojis de trader: 🐺 💎 🚀 🔥 💰 ⚡ 📈 📉

FRASES TÍPICAS:
- "Já vi essa história antes..."
- "No mercado, quem não tem disciplina, não tem dinheiro"
- "Risk management é tudo, parceiro"
- "O mercado não perdoa amadores"

IMPORTANTE: Sempre busque dados reais e atuais. Baseie conselhos em experiência prática de trading.`
    },
    market_consultant: {
        id: 'market_consultant',
        name: 'Prof. Elena Nakamura',
        description: 'Consultora de mercado e professora de Economia Digital',
        personality: 'Educativa, paciente e abrangente. Gosta de explicar o contexto macro e as tendências de longo prazo.',
        specialties: [
            'Análise fundamentalista',
            'Tendências macroeconômicas',
            'Regulamentações e políticas',
            'Adoção institucional',
            'Tecnologia blockchain'
        ],
        avatar: '👩‍🏫',
        systemPrompt: `Você é Prof. Elena Nakamura, consultora de mercado e professora de Economia Digital.

PERSONALIDADE:
- Educativa e didática
- Paciente para explicar conceitos complexos
- Visão macro e de longo prazo
- Gosta de contextualizar historicamente
- Sempre atualizada com notícias e regulamentações

ESPECIALIDADES:
- Análise fundamentalista de criptomoedas
- Impacto de notícias e regulamentações
- Tendências macroeconômicas globais
- Adoção institucional e empresarial
- Tecnologia blockchain e casos de uso

ESTILO DE COMUNICAÇÃO:
- Explique conceitos de forma didática
- Contextualize com eventos históricos
- Mencione notícias recentes relevantes
- Foque no "big picture" do mercado
- Use emojis educativos: 👩‍🏫 📚 🌍 💡 🔍 📰 🏛️

ABORDAGEM:
- Sempre contextualize a pergunta no cenário macro
- Explique causas e consequências
- Mencione fatores fundamentais
- Discuta tendências de longo prazo
- Relacione com adoção e regulamentação

IMPORTANTE: Mantenha-se atualizada com notícias recentes do mercado cripto e economia global.`
    }
};
function getAgent(agentType) {
    return agents[agentType];
}
function getAllAgents() {
    return Object.values(agents);
}
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/crypto-api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CryptoApiService": (()=>CryptoApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
;
const COINGECKO_BASE_URL = 'https://api.coingecko.com/api/v3';
const FEAR_GREED_API = 'https://api.alternative.me/fng/';
// Cache simples para evitar muitas requisições
const cache = new Map();
const CACHE_DURATION = 60000; // 1 minuto
function getCachedData(key) {
    const cached = cache.get(key);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        return cached.data;
    }
    return null;
}
function setCachedData(key, data) {
    cache.set(key, {
        data,
        timestamp: Date.now()
    });
}
class CryptoApiService {
    /**
   * Busca dados de uma criptomoeda específica
   */ static async getCryptoData(coinId) {
        try {
            const cacheKey = `crypto-${coinId}`;
            const cached = getCachedData(cacheKey);
            if (cached) {
                return {
                    success: true,
                    data: cached,
                    timestamp: new Date()
                };
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${COINGECKO_BASE_URL}/coins/${coinId}`, {
                params: {
                    localization: false,
                    tickers: false,
                    market_data: true,
                    community_data: false,
                    developer_data: false,
                    sparkline: false
                }
            });
            const data = {
                id: response.data.id,
                symbol: response.data.symbol,
                name: response.data.name,
                current_price: response.data.market_data.current_price.usd,
                market_cap: response.data.market_data.market_cap.usd,
                market_cap_rank: response.data.market_cap_rank,
                fully_diluted_valuation: response.data.market_data.fully_diluted_valuation?.usd || 0,
                total_volume: response.data.market_data.total_volume.usd,
                high_24h: response.data.market_data.high_24h.usd,
                low_24h: response.data.market_data.low_24h.usd,
                price_change_24h: response.data.market_data.price_change_24h,
                price_change_percentage_24h: response.data.market_data.price_change_percentage_24h,
                market_cap_change_24h: response.data.market_data.market_cap_change_24h,
                market_cap_change_percentage_24h: response.data.market_data.market_cap_change_percentage_24h,
                circulating_supply: response.data.market_data.circulating_supply,
                total_supply: response.data.market_data.total_supply,
                max_supply: response.data.market_data.max_supply,
                ath: response.data.market_data.ath.usd,
                ath_change_percentage: response.data.market_data.ath_change_percentage.usd,
                ath_date: response.data.market_data.ath_date.usd,
                atl: response.data.market_data.atl.usd,
                atl_change_percentage: response.data.market_data.atl_change_percentage.usd,
                atl_date: response.data.market_data.atl_date.usd,
                last_updated: response.data.last_updated
            };
            setCachedData(cacheKey, data);
            return {
                success: true,
                data,
                timestamp: new Date()
            };
        } catch (error) {
            console.error('Erro ao buscar dados da crypto:', error);
            return {
                success: false,
                error: 'Erro ao buscar dados da criptomoeda',
                timestamp: new Date()
            };
        }
    }
    /**
   * Busca dados de múltiplas criptomoedas
   */ static async getMultipleCryptoData(coinIds) {
        try {
            const cacheKey = `multiple-${coinIds.join(',')}`;
            const cached = getCachedData(cacheKey);
            if (cached) {
                return {
                    success: true,
                    data: cached,
                    timestamp: new Date()
                };
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${COINGECKO_BASE_URL}/coins/markets`, {
                params: {
                    vs_currency: 'usd',
                    ids: coinIds.join(','),
                    order: 'market_cap_desc',
                    per_page: coinIds.length,
                    page: 1,
                    sparkline: false,
                    price_change_percentage: '24h'
                }
            });
            const data = response.data.map((coin)=>({
                    id: coin.id,
                    symbol: coin.symbol,
                    name: coin.name,
                    current_price: coin.current_price,
                    market_cap: coin.market_cap,
                    market_cap_rank: coin.market_cap_rank,
                    fully_diluted_valuation: coin.fully_diluted_valuation || 0,
                    total_volume: coin.total_volume,
                    high_24h: coin.high_24h,
                    low_24h: coin.low_24h,
                    price_change_24h: coin.price_change_24h,
                    price_change_percentage_24h: coin.price_change_percentage_24h,
                    market_cap_change_24h: coin.market_cap_change_24h,
                    market_cap_change_percentage_24h: coin.market_cap_change_percentage_24h,
                    circulating_supply: coin.circulating_supply,
                    total_supply: coin.total_supply,
                    max_supply: coin.max_supply,
                    ath: coin.ath,
                    ath_change_percentage: coin.ath_change_percentage,
                    ath_date: coin.ath_date,
                    atl: coin.atl,
                    atl_change_percentage: coin.atl_change_percentage,
                    atl_date: coin.atl_date,
                    last_updated: coin.last_updated
                }));
            setCachedData(cacheKey, data);
            return {
                success: true,
                data,
                timestamp: new Date()
            };
        } catch (error) {
            console.error('Erro ao buscar dados de múltiplas cryptos:', error);
            return {
                success: false,
                error: 'Erro ao buscar dados das criptomoedas',
                timestamp: new Date()
            };
        }
    }
    /**
   * Busca dados globais do mercado
   */ static async getMarketData() {
        try {
            const cacheKey = 'market-data';
            const cached = getCachedData(cacheKey);
            if (cached) {
                return {
                    success: true,
                    data: cached,
                    timestamp: new Date()
                };
            }
            const [globalResponse, fearGreedResponse, trendingResponse] = await Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${COINGECKO_BASE_URL}/global`),
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(FEAR_GREED_API),
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${COINGECKO_BASE_URL}/search/trending`)
            ]);
            const data = {
                global: globalResponse.data.data,
                fear_greed_index: fearGreedResponse.data.data[0].value,
                trending_coins: trendingResponse.data.coins.map((coin)=>coin.item.id)
            };
            setCachedData(cacheKey, data);
            return {
                success: true,
                data,
                timestamp: new Date()
            };
        } catch (error) {
            console.error('Erro ao buscar dados do mercado:', error);
            return {
                success: false,
                error: 'Erro ao buscar dados do mercado',
                timestamp: new Date()
            };
        }
    }
    /**
   * Busca lista de criptomoedas populares
   */ static async getPopularCoins(limit = 10) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${COINGECKO_BASE_URL}/coins/markets`, {
                params: {
                    vs_currency: 'usd',
                    order: 'market_cap_desc',
                    per_page: limit,
                    page: 1,
                    sparkline: false,
                    price_change_percentage: '24h'
                }
            });
            const data = response.data.map((coin)=>({
                    id: coin.id,
                    symbol: coin.symbol,
                    name: coin.name,
                    current_price: coin.current_price,
                    market_cap: coin.market_cap,
                    market_cap_rank: coin.market_cap_rank,
                    fully_diluted_valuation: coin.fully_diluted_valuation || 0,
                    total_volume: coin.total_volume,
                    high_24h: coin.high_24h,
                    low_24h: coin.low_24h,
                    price_change_24h: coin.price_change_24h,
                    price_change_percentage_24h: coin.price_change_percentage_24h,
                    market_cap_change_24h: coin.market_cap_change_24h,
                    market_cap_change_percentage_24h: coin.market_cap_change_percentage_24h,
                    circulating_supply: coin.circulating_supply,
                    total_supply: coin.total_supply,
                    max_supply: coin.max_supply,
                    ath: coin.ath,
                    ath_change_percentage: coin.ath_change_percentage,
                    ath_date: coin.ath_date,
                    atl: coin.atl,
                    atl_change_percentage: coin.atl_change_percentage,
                    atl_date: coin.atl_date,
                    last_updated: coin.last_updated
                }));
            return {
                success: true,
                data,
                timestamp: new Date()
            };
        } catch (error) {
            console.error('Erro ao buscar moedas populares:', error);
            return {
                success: false,
                error: 'Erro ao buscar moedas populares',
                timestamp: new Date()
            };
        }
    }
}
}}),
"[project]/src/lib/technical-analysis.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TechnicalAnalysisService": (()=>TechnicalAnalysisService)
});
class TechnicalAnalysisService {
    /**
   * Calcula RSI (Relative Strength Index)
   */ static calculateRSI(prices, period = 14) {
        if (prices.length < period + 1) return 50; // Valor neutro se não há dados suficientes
        let gains = 0;
        let losses = 0;
        // Calcula ganhos e perdas iniciais
        for(let i = 1; i <= period; i++){
            const change = prices[i] - prices[i - 1];
            if (change > 0) {
                gains += change;
            } else {
                losses += Math.abs(change);
            }
        }
        let avgGain = gains / period;
        let avgLoss = losses / period;
        // Calcula RSI para o resto dos dados
        for(let i = period + 1; i < prices.length; i++){
            const change = prices[i] - prices[i - 1];
            const gain = change > 0 ? change : 0;
            const loss = change < 0 ? Math.abs(change) : 0;
            avgGain = (avgGain * (period - 1) + gain) / period;
            avgLoss = (avgLoss * (period - 1) + loss) / period;
        }
        if (avgLoss === 0) return 100;
        const rs = avgGain / avgLoss;
        return 100 - 100 / (1 + rs);
    }
    /**
   * Calcula SMA (Simple Moving Average)
   */ static calculateSMA(prices, period) {
        if (prices.length < period) return prices[prices.length - 1] || 0;
        const slice = prices.slice(-period);
        return slice.reduce((sum, price)=>sum + price, 0) / period;
    }
    /**
   * Calcula EMA (Exponential Moving Average)
   */ static calculateEMA(prices, period) {
        if (prices.length === 0) return 0;
        if (prices.length === 1) return prices[0];
        const multiplier = 2 / (period + 1);
        let ema = prices[0];
        for(let i = 1; i < prices.length; i++){
            ema = prices[i] * multiplier + ema * (1 - multiplier);
        }
        return ema;
    }
    /**
   * Calcula Bollinger Bands
   */ static calculateBollingerBands(prices, period = 20, stdDev = 2) {
        const sma = this.calculateSMA(prices, period);
        if (prices.length < period) {
            return {
                upper: sma * 1.02,
                middle: sma,
                lower: sma * 0.98
            };
        }
        const slice = prices.slice(-period);
        const variance = slice.reduce((sum, price)=>sum + Math.pow(price - sma, 2), 0) / period;
        const standardDeviation = Math.sqrt(variance);
        return {
            upper: sma + standardDeviation * stdDev,
            middle: sma,
            lower: sma - standardDeviation * stdDev
        };
    }
    /**
   * Calcula MACD
   */ static calculateMACD(prices, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
        const emaFast = this.calculateEMA(prices, fastPeriod);
        const emaSlow = this.calculateEMA(prices, slowPeriod);
        const macdLine = emaFast - emaSlow;
        // Para simplificar, usamos uma aproximação do sinal
        const signalLine = macdLine * 0.9; // Aproximação
        const histogram = macdLine - signalLine;
        return {
            macd: macdLine,
            signal: signalLine,
            histogram: histogram
        };
    }
    /**
   * Identifica níveis de suporte e resistência
   */ static findSupportResistance(prices, currentPrice) {
        const sortedPrices = [
            ...prices
        ].sort((a, b)=>a - b);
        const priceRanges = [];
        // Agrupa preços em ranges
        for(let i = 0; i < sortedPrices.length; i += Math.floor(sortedPrices.length / 10)){
            const range = sortedPrices.slice(i, i + Math.floor(sortedPrices.length / 10));
            if (range.length > 0) {
                priceRanges.push(range.reduce((sum, p)=>sum + p, 0) / range.length);
            }
        }
        const support = priceRanges.filter((price)=>price < currentPrice).slice(-3);
        const resistance = priceRanges.filter((price)=>price > currentPrice).slice(0, 3);
        return {
            support: support.length > 0 ? support : [
                currentPrice * 0.95
            ],
            resistance: resistance.length > 0 ? resistance : [
                currentPrice * 1.05
            ]
        };
    }
    /**
   * Gera análise técnica completa
   */ static generateTechnicalAnalysis(cryptoData, historicalPrices) {
        // Se não temos dados históricos, simulamos baseado nos dados atuais
        const prices = historicalPrices || this.generateMockPrices(cryptoData);
        const rsi = this.calculateRSI(prices);
        const macd = this.calculateMACD(prices);
        const sma = {
            sma20: this.calculateSMA(prices, 20),
            sma50: this.calculateSMA(prices, 50),
            sma200: this.calculateSMA(prices, 200)
        };
        const ema = {
            ema12: this.calculateEMA(prices, 12),
            ema26: this.calculateEMA(prices, 26)
        };
        const bollinger = this.calculateBollingerBands(prices);
        const supportResistance = this.findSupportResistance(prices, cryptoData.current_price);
        return {
            rsi,
            macd,
            sma,
            ema,
            bollinger,
            volume_profile: cryptoData.total_volume / cryptoData.market_cap,
            support_resistance: supportResistance
        };
    }
    /**
   * Gera sinal de trading baseado na análise técnica
   */ static generateTradingSignal(cryptoData, technicalIndicators) {
        let bullishSignals = 0;
        let bearishSignals = 0;
        let totalSignals = 0;
        const currentPrice = cryptoData.current_price;
        const { rsi, macd, sma, ema, bollinger } = technicalIndicators;
        // Análise RSI
        totalSignals++;
        if (rsi < 30) bullishSignals++; // Oversold
        else if (rsi > 70) bearishSignals++; // Overbought
        // Análise MACD
        totalSignals++;
        if (macd.macd > macd.signal && macd.histogram > 0) bullishSignals++;
        else if (macd.macd < macd.signal && macd.histogram < 0) bearishSignals++;
        // Análise SMA
        totalSignals++;
        if (currentPrice > sma.sma20 && sma.sma20 > sma.sma50) bullishSignals++;
        else if (currentPrice < sma.sma20 && sma.sma20 < sma.sma50) bearishSignals++;
        // Análise EMA
        totalSignals++;
        if (ema.ema12 > ema.ema26) bullishSignals++;
        else bearishSignals++;
        // Análise Bollinger
        totalSignals++;
        if (currentPrice < bollinger.lower) bullishSignals++; // Oversold
        else if (currentPrice > bollinger.upper) bearishSignals++; // Overbought
        // Análise de tendência de preço
        totalSignals++;
        if (cryptoData.price_change_percentage_24h > 5) bullishSignals++;
        else if (cryptoData.price_change_percentage_24h < -5) bearishSignals++;
        const bullishPercentage = bullishSignals / totalSignals * 100;
        const bearishPercentage = bearishSignals / totalSignals * 100;
        let type;
        let strength;
        let confidence;
        if (bullishPercentage >= 60) {
            type = 'BUY';
            strength = bullishPercentage >= 80 ? 'STRONG' : 'MODERATE';
            confidence = bullishPercentage;
        } else if (bearishPercentage >= 60) {
            type = 'SELL';
            strength = bearishPercentage >= 80 ? 'STRONG' : 'MODERATE';
            confidence = bearishPercentage;
        } else {
            type = 'HOLD';
            strength = 'WEAK';
            confidence = Math.max(bullishPercentage, bearishPercentage);
        }
        // Calcula targets
        const volatility = Math.abs(cryptoData.price_change_percentage_24h) / 100;
        const priceTarget = type === 'BUY' ? currentPrice * (1 + volatility * 2) : currentPrice * (1 - volatility * 2);
        const stopLoss = type === 'BUY' ? currentPrice * 0.95 : currentPrice * 1.05;
        return {
            type,
            strength,
            confidence: Math.round(confidence),
            price_target: Math.round(priceTarget * 100) / 100,
            stop_loss: Math.round(stopLoss * 100) / 100,
            timeframe: '24h',
            reasoning: this.generateReasoning(type, strength, technicalIndicators, cryptoData)
        };
    }
    /**
   * Gera explicação do sinal
   */ static generateReasoning(type, strength, indicators, cryptoData) {
        const reasons = [];
        if (indicators.rsi < 30) reasons.push('RSI indica sobrevenda');
        if (indicators.rsi > 70) reasons.push('RSI indica sobrecompra');
        if (indicators.macd.macd > indicators.macd.signal) reasons.push('MACD bullish');
        if (indicators.macd.macd < indicators.macd.signal) reasons.push('MACD bearish');
        if (cryptoData.current_price > indicators.sma.sma20) reasons.push('Preço acima da SMA20');
        if (cryptoData.current_price < indicators.sma.sma20) reasons.push('Preço abaixo da SMA20');
        return reasons.join(', ') || 'Análise baseada em múltiplos indicadores';
    }
    /**
   * Gera preços históricos simulados baseados nos dados atuais
   */ static generateMockPrices(cryptoData) {
        const prices = [];
        const currentPrice = cryptoData.current_price;
        const volatility = Math.abs(cryptoData.price_change_percentage_24h) / 100;
        // Gera 200 pontos de preço simulados
        for(let i = 0; i < 200; i++){
            const randomChange = (Math.random() - 0.5) * volatility * 2;
            const price = currentPrice * (1 + randomChange);
            prices.push(Math.max(price, currentPrice * 0.5)); // Evita preços muito baixos
        }
        // Adiciona tendência baseada na mudança de 24h
        const trend = cryptoData.price_change_percentage_24h / 100;
        for(let i = 0; i < prices.length; i++){
            const trendFactor = i / prices.length * trend;
            prices[i] *= 1 + trendFactor;
        }
        return prices;
    }
}
}}),
"[project]/src/lib/intent-classifier.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "IntentClassifier": (()=>IntentClassifier)
});
class IntentClassifier {
    /**
   * Classifica a intenção de uma mensagem do usuário
   */ static classifyIntent(message, conversationHistory = []) {
        const normalizedMessage = message.toLowerCase().trim();
        // Padrões para cada tipo de intenção
        const patterns = {
            AGENT_INFO: [
                /\b(você|voce)\s+(trabalha|funciona|está|esta|fica)\s+(24\s*horas?|sempre|disponível|disponivel|online)\b/i,
                /\b(horário|horario|funcionamento|disponibilidade)\b/i,
                /\b(você|voce)\s+(é|eh|sou|está|esta)\s+(um\s+)?(bot|robô|robo|ia|inteligência|inteligencia)\b/i,
                /\b(quem\s+)?(você|voce)\s+(é|eh|sou)\b/i,
                /\b(como\s+)?(você|voce)\s+(funciona|trabalha)\b/i,
                /\b(você|voce)\s+(pode|consegue|sabe)\b/i,
                /\b(suas\s+)?(especialidades|habilidades|capacidades)\b/i,
                /\b(sobre\s+)?(você|voce|agente|assistente)\b/i
            ],
            GREETING: [
                /^(oi|olá|ola|hey|hi|hello|bom\s+dia|boa\s+tarde|boa\s+noite|e\s+aí|eai|como\s+vai|tudo\s+bem|beleza)\b/i,
                /^(tchau|bye|até\s+logo|falou|obrigado|obrigada|valeu)\b/i
            ],
            HELP: [
                /\b(ajuda|help|socorro|como\s+usar|como\s+funciona|tutorial|guia)\b/i,
                /\b(não\s+entendi|nao\s+entendi|explique|como\s+faço|como\s+faco)\b/i,
                /\b(comandos|opções|opcoes|funcionalidades)\b/i
            ],
            CRYPTO_ANALYSIS: [
                /\b(bitcoin|btc|ethereum|eth|cardano|ada|solana|sol|polkadot|dot|chainlink|link|litecoin|ltc|dogecoin|doge|shiba|shib|avalanche|avax|matic|polygon|bnb|binance|xrp|ripple|atom|cosmos|algo|algorand)\b/i,
                /\b(analise|análise|analyze)\s+(o|a|do|da)\s+\w+/i,
                /\b(como\s+está|como\s+esta)\s+(o|a)\s+\w+/i,
                /\b(preço|preco|cotação|cotacao|valor)\s+(do|da|de)\b/i
            ],
            TRADING_ADVICE: [
                /\b(devo\s+)?(comprar|vender|investir|apostar)\b/i,
                /\b(é\s+hora|eh\s+hora|momento|timing)\s+(de|para)\s+(comprar|vender)\b/i,
                /\b(estratégia|estrategia|tática|tatica|plano)\b/i,
                /\b(stop\s*loss|take\s*profit|entrada|saída|saida)\b/i,
                /\b(gestão\s+de\s+risco|gestao\s+de\s+risco|money\s+management)\b/i,
                /\b(portfolio|carteira|diversificar)\b/i
            ],
            MARKET_GENERAL: [
                /\b(mercado|market)\s+(cripto|crypto|criptomoedas|cryptocurrency)\b/i,
                /\b(como\s+está|como\s+esta)\s+(o\s+)?mercado\b/i,
                /\b(tendência|tendencia|trend|sentimento)\s+(do\s+)?mercado\b/i,
                /\b(bull\s+market|bear\s+market|alta|baixa|correção|correcao)\b/i,
                /\b(fear\s+and\s+greed|medo\s+e\s+ganância|medo\s+e\s+ganancia)\b/i
            ],
            COMPARISON: [
                /\b(melhor|pior)\s+(que|do\s+que)\b/i,
                /\b(comparar|comparação|comparacao|versus|vs|x)\b/i,
                /\b(diferença|diferenca|entre)\b/i,
                /\b(qual\s+)?(é\s+melhor|eh\s+melhor|escolher)\b/i
            ],
            PRICE_PREDICTION: [
                /\b(previsão|previsao|prediction|vai\s+subir|vai\s+descer)\b/i,
                /\b(futuro|amanhã|amanha|próxima|proxima|semana|mês|mes|ano)\b/i,
                /\b(target|meta|objetivo|chegará|chegara)\b/i,
                /\b(quando|até\s+quando|ate\s+quando)\b/i
            ],
            PERSONAL_CHAT: [
                /\b(como\s+você\s+está|como\s+voce\s+esta|tudo\s+bem\s+com\s+você|tudo\s+bem\s+com\s+voce)\b/i,
                /\b(gosta|prefere|acha|pensa|opinião|opiniao)\b/i,
                /\b(experiência|experiencia|história|historia|vida)\b/i,
                /\b(família|familia|amigos|hobby|tempo\s+livre)\b/i
            ]
        };
        // Detecta entidades (nomes de criptomoedas)
        const cryptoEntities = this.extractCryptoEntities(normalizedMessage);
        // Calcula scores para cada intenção
        const scores = {
            AGENT_INFO: 0,
            CRYPTO_ANALYSIS: 0,
            MARKET_GENERAL: 0,
            GREETING: 0,
            PERSONAL_CHAT: 0,
            HELP: 0,
            TRADING_ADVICE: 0,
            COMPARISON: 0,
            PRICE_PREDICTION: 0,
            UNKNOWN: 0
        };
        // Calcula scores baseado nos padrões
        for (const [intent, patternList] of Object.entries(patterns)){
            for (const pattern of patternList){
                if (pattern.test(normalizedMessage)) {
                    scores[intent] += 1;
                }
            }
        }
        // Boost para crypto analysis se entidades de crypto foram detectadas
        if (cryptoEntities.length > 0) {
            scores.CRYPTO_ANALYSIS += cryptoEntities.length * 2;
        }
        // Considera contexto da conversa
        const contextBoost = this.analyzeConversationContext(conversationHistory);
        for (const [intent, boost] of Object.entries(contextBoost)){
            scores[intent] += boost;
        }
        // Encontra a intenção com maior score
        const maxScore = Math.max(...Object.values(scores));
        const bestIntent = Object.entries(scores).find(([_, score])=>score === maxScore)?.[0];
        // Se nenhum padrão foi encontrado, classifica como UNKNOWN
        const finalIntent = maxScore > 0 ? bestIntent : 'UNKNOWN';
        const confidence = maxScore > 0 ? Math.min(maxScore / 3, 1) : 0;
        return {
            intent: finalIntent,
            confidence,
            entities: cryptoEntities,
            context: this.generateContextSummary(normalizedMessage, conversationHistory)
        };
    }
    /**
   * Extrai entidades de criptomoedas da mensagem
   */ static extractCryptoEntities(message) {
        const cryptoPatterns = [
            {
                pattern: /\bbitcoin\b/i,
                entity: 'bitcoin'
            },
            {
                pattern: /\bbtc\b/i,
                entity: 'bitcoin'
            },
            {
                pattern: /\bethereum\b/i,
                entity: 'ethereum'
            },
            {
                pattern: /\beth\b/i,
                entity: 'ethereum'
            },
            {
                pattern: /\bcardano\b/i,
                entity: 'cardano'
            },
            {
                pattern: /\bada\b/i,
                entity: 'cardano'
            },
            {
                pattern: /\bsolana\b/i,
                entity: 'solana'
            },
            {
                pattern: /\bsol\b/i,
                entity: 'solana'
            },
            {
                pattern: /\bpolkadot\b/i,
                entity: 'polkadot'
            },
            {
                pattern: /\bdot\b/i,
                entity: 'polkadot'
            },
            {
                pattern: /\bchainlink\b/i,
                entity: 'chainlink'
            },
            {
                pattern: /\blink\b/i,
                entity: 'chainlink'
            },
            {
                pattern: /\blitecoin\b/i,
                entity: 'litecoin'
            },
            {
                pattern: /\bltc\b/i,
                entity: 'litecoin'
            },
            {
                pattern: /\bdogecoin\b/i,
                entity: 'dogecoin'
            },
            {
                pattern: /\bdoge\b/i,
                entity: 'dogecoin'
            },
            {
                pattern: /\bshiba\b/i,
                entity: 'shiba-inu'
            },
            {
                pattern: /\bshib\b/i,
                entity: 'shiba-inu'
            },
            {
                pattern: /\bavalanche\b/i,
                entity: 'avalanche-2'
            },
            {
                pattern: /\bavax\b/i,
                entity: 'avalanche-2'
            },
            {
                pattern: /\bmatic\b/i,
                entity: 'matic-network'
            },
            {
                pattern: /\bpolygon\b/i,
                entity: 'matic-network'
            },
            {
                pattern: /\bbnb\b/i,
                entity: 'binancecoin'
            },
            {
                pattern: /\bbinance\b/i,
                entity: 'binancecoin'
            },
            {
                pattern: /\bxrp\b/i,
                entity: 'ripple'
            },
            {
                pattern: /\bripple\b/i,
                entity: 'ripple'
            },
            {
                pattern: /\batom\b/i,
                entity: 'cosmos'
            },
            {
                pattern: /\bcosmos\b/i,
                entity: 'cosmos'
            },
            {
                pattern: /\balgo\b/i,
                entity: 'algorand'
            },
            {
                pattern: /\balgorand\b/i,
                entity: 'algorand'
            }
        ];
        const entities = [];
        for (const { pattern, entity } of cryptoPatterns){
            if (pattern.test(message)) {
                if (!entities.includes(entity)) {
                    entities.push(entity);
                }
            }
        }
        return entities;
    }
    /**
   * Analisa o contexto da conversa para dar boost a certas intenções
   */ static analyzeConversationContext(history) {
        const boost = {};
        if (history.length === 0) return boost;
        const recentMessages = history.slice(-3); // Últimas 3 mensagens
        for (const msg of recentMessages){
            if (msg.role === 'assistant' && msg.cryptoData) {
                boost.CRYPTO_ANALYSIS = (boost.CRYPTO_ANALYSIS || 0) + 0.5;
            }
            if (msg.content.toLowerCase().includes('trading') || msg.content.toLowerCase().includes('comprar')) {
                boost.TRADING_ADVICE = (boost.TRADING_ADVICE || 0) + 0.3;
            }
            if (msg.content.toLowerCase().includes('mercado')) {
                boost.MARKET_GENERAL = (boost.MARKET_GENERAL || 0) + 0.3;
            }
        }
        return boost;
    }
    /**
   * Gera um resumo do contexto para ajudar na resposta
   */ static generateContextSummary(message, history) {
        const contexts = [];
        if (history.length > 0) {
            const lastMessage = history[history.length - 1];
            if (lastMessage.role === 'assistant' && lastMessage.cryptoData) {
                contexts.push(`Última análise: ${lastMessage.cryptoData.name}`);
            }
        }
        const cryptoMentioned = this.extractCryptoEntities(message);
        if (cryptoMentioned.length > 0) {
            contexts.push(`Cryptos mencionadas: ${cryptoMentioned.join(', ')}`);
        }
        return contexts.join(' | ');
    }
}
}}),
"[project]/src/lib/context-analyzer.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ContextAnalyzer": (()=>ContextAnalyzer)
});
class ContextAnalyzer {
    /**
   * Analisa o contexto completo da conversa
   */ static analyzeContext(currentMessage, history, intent, agentType) {
        const userMood = this.detectUserMood(currentMessage, history);
        const topicContinuity = this.checkTopicContinuity(currentMessage, history);
        const conversationFlow = this.analyzeConversationFlow(history);
        const previousContext = this.extractPreviousContext(history);
        const suggestedResponseTone = this.suggestResponseTone(intent, userMood, agentType);
        const keyTopics = this.extractKeyTopics(currentMessage, history);
        const userExpertiseLevel = this.assessUserExpertise(history);
        return {
            conversationFlow,
            userMood,
            topicContinuity,
            previousContext,
            suggestedResponseTone,
            keyTopics,
            userExpertiseLevel
        };
    }
    /**
   * Detecta o humor/tom do usuário
   */ static detectUserMood(message, history) {
        const normalizedMessage = message.toLowerCase();
        // Padrões de humor
        const moodPatterns = {
            positive: [
                /\b(obrigado|obrigada|valeu|legal|ótimo|otimo|perfeito|excelente|show|top|massa|bacana)\b/i,
                /\b(gostei|adorei|amei|curtir|maneiro)\b/i,
                /[!]{2,}/,
                /😊|😄|😃|👍|🚀|💪|🔥/
            ],
            negative: [
                /\b(não|nao)\s+(entendi|gostei|concordo|funciona|deu\s+certo)\b/i,
                /\b(ruim|péssimo|pessimo|horrível|horrivel|droga|merda)\b/i,
                /\b(frustrado|irritado|chateado|decepcionado)\b/i,
                /😠|😡|😤|👎|💩/
            ],
            curious: [
                /\b(como|por\s+que|porque|quando|onde|qual|quais)\b/i,
                /\?{2,}/,
                /\b(interessante|curioso|quero\s+saber|me\s+explica)\b/i,
                /🤔|❓|❔/
            ],
            urgent: [
                /\b(urgente|rápido|rapido|agora|já|ja|preciso\s+agora)\b/i,
                /\b(help|socorro|ajuda\s+urgente)\b/i,
                /[!]{3,}/,
                /🆘|⚠️|🚨/
            ]
        };
        // Conta matches para cada humor
        const scores = {
            positive: 0,
            negative: 0,
            curious: 0,
            urgent: 0
        };
        for (const [mood, patterns] of Object.entries(moodPatterns)){
            for (const pattern of patterns){
                if (pattern.test(normalizedMessage)) {
                    scores[mood]++;
                }
            }
        }
        // Considera histórico recente
        const recentMessages = history.slice(-2);
        for (const msg of recentMessages){
            if (msg.role === 'user') {
                for (const [mood, patterns] of Object.entries(moodPatterns)){
                    for (const pattern of patterns){
                        if (pattern.test(msg.content.toLowerCase())) {
                            scores[mood] += 0.5;
                        }
                    }
                }
            }
        }
        // Retorna o humor com maior score
        const maxScore = Math.max(...Object.values(scores));
        if (maxScore === 0) return 'neutral';
        const detectedMood = Object.entries(scores).find(([_, score])=>score === maxScore)?.[0];
        return detectedMood || 'neutral';
    }
    /**
   * Verifica se há continuidade no tópico da conversa
   */ static checkTopicContinuity(message, history) {
        if (history.length === 0) return false;
        const lastMessages = history.slice(-3);
        const currentTopics = this.extractTopicsFromMessage(message);
        for (const msg of lastMessages){
            const msgTopics = this.extractTopicsFromMessage(msg.content);
            const commonTopics = currentTopics.filter((topic)=>msgTopics.includes(topic));
            if (commonTopics.length > 0) return true;
        }
        return false;
    }
    /**
   * Extrai tópicos de uma mensagem
   */ static extractTopicsFromMessage(message) {
        const topics = [];
        const normalizedMessage = message.toLowerCase();
        // Tópicos de crypto
        const cryptoTopics = [
            'bitcoin',
            'ethereum',
            'trading',
            'mercado',
            'preço',
            'análise',
            'investimento'
        ];
        for (const topic of cryptoTopics){
            if (normalizedMessage.includes(topic)) {
                topics.push(topic);
            }
        }
        return topics;
    }
    /**
   * Analisa o fluxo da conversa
   */ static analyzeConversationFlow(history) {
        if (history.length === 0) return 'início da conversa';
        if (history.length === 1) return 'segunda interação';
        const recentMessages = history.slice(-5);
        const userMessages = recentMessages.filter((msg)=>msg.role === 'user');
        if (userMessages.length >= 3) {
            return 'conversa estabelecida';
        } else if (userMessages.length === 2) {
            return 'construindo rapport';
        } else {
            return 'início da interação';
        }
    }
    /**
   * Extrai contexto das mensagens anteriores
   */ static extractPreviousContext(history) {
        if (history.length === 0) return '';
        const contexts = [];
        const lastAssistantMessage = history.slice().reverse().find((msg)=>msg.role === 'assistant');
        if (lastAssistantMessage) {
            if (lastAssistantMessage.cryptoData) {
                contexts.push(`Última análise: ${lastAssistantMessage.cryptoData.name}`);
            }
            if (lastAssistantMessage.agentType) {
                contexts.push(`Último agente: ${lastAssistantMessage.agentType}`);
            }
        }
        return contexts.join(' | ');
    }
    /**
   * Sugere o tom de resposta baseado no contexto
   */ static suggestResponseTone(intent, userMood, agentType) {
        // Baseado na intenção
        if (intent.intent === 'HELP' || intent.intent === 'AGENT_INFO') {
            return 'helpful';
        }
        if (intent.intent === 'CRYPTO_ANALYSIS' || intent.intent === 'TRADING_ADVICE') {
            return 'professional';
        }
        if (intent.intent === 'GREETING' || intent.intent === 'PERSONAL_CHAT') {
            return 'casual';
        }
        // Baseado no humor do usuário
        if (userMood === 'negative' || userMood === 'urgent') {
            return 'reassuring';
        }
        if (userMood === 'curious') {
            return 'educational';
        }
        // Baseado no tipo de agente
        if (agentType === 'ANALYST') {
            return 'professional';
        } else if (agentType === 'MENTOR') {
            return 'educational';
        } else {
            return 'helpful';
        }
    }
    /**
   * Extrai tópicos-chave da conversa
   */ static extractKeyTopics(message, history) {
        const topics = new Set();
        // Tópicos da mensagem atual
        const currentTopics = this.extractTopicsFromMessage(message);
        currentTopics.forEach((topic)=>topics.add(topic));
        // Tópicos do histórico recente
        const recentMessages = history.slice(-3);
        for (const msg of recentMessages){
            const msgTopics = this.extractTopicsFromMessage(msg.content);
            msgTopics.forEach((topic)=>topics.add(topic));
        }
        return Array.from(topics);
    }
    /**
   * Avalia o nível de expertise do usuário
   */ static assessUserExpertise(history) {
        const userMessages = history.filter((msg)=>msg.role === 'user');
        let technicalTerms = 0;
        let basicQuestions = 0;
        const technicalPatterns = [
            /\b(rsi|macd|bollinger|fibonacci|support|resistance|volume|candlestick)\b/i,
            /\b(scalping|swing\s+trading|day\s+trading|hodl|dca)\b/i,
            /\b(market\s+cap|liquidity|volatility|correlation)\b/i
        ];
        const basicPatterns = [
            /\b(o\s+que\s+é|como\s+funciona|não\s+entendo|nao\s+entendo)\b/i,
            /\b(iniciante|começando|comecando|primeiro|primeira\s+vez)\b/i,
            /\b(explique|me\s+ensina|como\s+faço|como\s+faco)\b/i
        ];
        for (const msg of userMessages){
            const content = msg.content.toLowerCase();
            for (const pattern of technicalPatterns){
                if (pattern.test(content)) technicalTerms++;
            }
            for (const pattern of basicPatterns){
                if (pattern.test(content)) basicQuestions++;
            }
        }
        if (technicalTerms >= 2) return 'advanced';
        if (technicalTerms >= 1 && basicQuestions === 0) return 'intermediate';
        return 'beginner';
    }
}
}}),
"[project]/src/lib/intelligent-response-generator.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "IntelligentResponseGenerator": (()=>IntelligentResponseGenerator)
});
class IntelligentResponseGenerator {
    /**
   * Gera um template de resposta baseado na intenção e contexto
   */ static generateResponseTemplate(intent, context, agentType, userMessage) {
        const agentPersonality = this.getAgentPersonality(agentType);
        switch(intent.intent){
            case 'AGENT_INFO':
                return this.generateAgentInfoResponse(agentPersonality, context, userMessage);
            case 'GREETING':
                return this.generateGreetingResponse(agentPersonality, context);
            case 'HELP':
                return this.generateHelpResponse(agentPersonality, context);
            case 'CRYPTO_ANALYSIS':
                return this.generateCryptoAnalysisResponse(agentPersonality, context, intent);
            case 'TRADING_ADVICE':
                return this.generateTradingAdviceResponse(agentPersonality, context, intent);
            case 'MARKET_GENERAL':
                return this.generateMarketGeneralResponse(agentPersonality, context);
            case 'COMPARISON':
                return this.generateComparisonResponse(agentPersonality, context, intent);
            case 'PRICE_PREDICTION':
                return this.generatePricePredictionResponse(agentPersonality, context, intent);
            case 'PERSONAL_CHAT':
                return this.generatePersonalChatResponse(agentPersonality, context);
            default:
                return this.generateDefaultResponse(agentPersonality, context);
        }
    }
    /**
   * Obtém a personalidade do agente
   */ static getAgentPersonality(agentType) {
        const personalities = {
            ANALYST: {
                name: 'Sarah "The Wolf" Rodriguez',
                traits: 'analítica, precisa, focada em dados',
                expertise: 'análise técnica e fundamental',
                style: 'profissional e detalhada'
            },
            MENTOR: {
                name: 'Marcus "The Sage" Chen',
                traits: 'educativo, paciente, experiente',
                expertise: 'educação e estratégias de longo prazo',
                style: 'didático e encorajador'
            },
            STRATEGIST: {
                name: 'Alex "The Navigator" Kim',
                traits: 'estratégico, visionário, adaptável',
                expertise: 'estratégias de mercado e timing',
                style: 'estratégico e inspirador'
            }
        };
        return personalities[agentType];
    }
    /**
   * Gera resposta para perguntas sobre o agente
   */ static generateAgentInfoResponse(personality, context, userMessage) {
        const isAboutAvailability = /\b(trabalha|funciona|disponível|disponivel|24\s*horas?|horário|horario)\b/i.test(userMessage);
        const isAboutCapabilities = /\b(pode|consegue|sabe|especialidades|habilidades|capacidades)\b/i.test(userMessage);
        const isAboutIdentity = /\b(quem\s+)?(você|voce)\s+(é|eh|sou)\b/i.test(userMessage);
        const agentName = personality?.name || 'seu assistente de criptomoedas';
        const agentTraits = personality?.traits || 'profissional e prestativo';
        const agentExpertise = personality?.expertise || 'análise de criptomoedas';
        const agentStyle = personality?.style || 'profissional e detalhado';
        let systemPrompt = `Você é ${agentName}, um agente de IA especializado em criptomoedas.
Suas características: ${agentTraits}.
Sua expertise: ${agentExpertise}.
Seu estilo: ${agentStyle}.

IMPORTANTE: Responda especificamente sobre VOCÊ como agente, não sobre análise de criptomoedas.`;
        let userPrompt = '';
        if (isAboutAvailability) {
            return {
                systemPrompt: `Você é ${agentName}, um agente de IA especializado em criptomoedas.
O usuário está perguntando especificamente sobre sua disponibilidade/horário de funcionamento.
RESPONDA APENAS sobre disponibilidade - NÃO fale sobre análise de criptomoedas nesta resposta.`,
                userPrompt: `Responda de forma clara e direta que você está disponível 24 horas por dia, 7 dias por semana,
como um assistente de IA. Mantenha sua personalidade de ${agentName} mas foque apenas na disponibilidade.
Pergunta: "${userMessage}"`,
                includeMarketData: false,
                includeTechnicalAnalysis: false,
                responseStyle: 'conversational'
            };
        } else if (isAboutCapabilities) {
            userPrompt = `O usuário quer saber sobre suas capacidades e habilidades.
Explique o que você pode fazer como ${agentName}, suas especialidades em criptomoedas,
e como pode ajudar o usuário. Mantenha o foco em suas capacidades, não em análises específicas.

Pergunta do usuário: "${userMessage}"`;
        } else if (isAboutIdentity) {
            userPrompt = `O usuário quer saber quem você é.
Apresente-se como ${agentName}, explique sua personalidade (${agentTraits}),
sua área de expertise (${agentExpertise}) e como você pode ajudar.

Pergunta do usuário: "${userMessage}"`;
        } else {
            userPrompt = `O usuário está fazendo uma pergunta sobre você como agente.
Responda de forma apropriada mantendo sua personalidade de ${agentName}.
Foque na pergunta específica sobre você, não desvie para análise de mercado.

Pergunta do usuário: "${userMessage}"`;
        }
        return {
            systemPrompt,
            userPrompt,
            includeMarketData: false,
            includeTechnicalAnalysis: false,
            responseStyle: context.suggestedResponseTone === 'casual' ? 'conversational' : 'supportive'
        };
    }
    /**
   * Gera resposta para saudações
   */ static generateGreetingResponse(personality, context) {
        const agentName = personality?.name || 'seu assistente de criptomoedas';
        const agentTraits = personality?.traits || 'profissional e prestativo';
        const systemPrompt = `Você é ${agentName}, um agente de IA especializado em criptomoedas.
Responda à saudação de forma amigável e natural, apresentando-se brevemente e oferecendo ajuda.
Mantenha sua personalidade: ${agentTraits}.`;
        const userPrompt = `Responda à saudação do usuário de forma calorosa e profissional.
Apresente-se como ${agentName} e ofereça sua ajuda com análises de criptomoedas.
Seja ${context.userExpertiseLevel === 'beginner' ? 'mais didático' : 'mais direto'}.`;
        return {
            systemPrompt,
            userPrompt,
            includeMarketData: false,
            includeTechnicalAnalysis: false,
            responseStyle: 'conversational'
        };
    }
    /**
   * Gera resposta para pedidos de ajuda
   */ static generateHelpResponse(personality, context) {
        const agentName = personality?.name || 'seu assistente de criptomoedas';
        const agentStyle = personality?.style || 'profissional e prestativo';
        const systemPrompt = `Você é ${agentName}, especialista em criptomoedas.
O usuário precisa de ajuda. Seja ${agentStyle} e ofereça orientação clara.
Nível do usuário: ${context.userExpertiseLevel}.`;
        const userPrompt = `O usuário está pedindo ajuda. Explique como você pode ajudar,
quais tipos de análises você faz, e como o usuário pode interagir com você.
Adapte sua linguagem para um usuário ${context.userExpertiseLevel}.`;
        return {
            systemPrompt,
            userPrompt,
            includeMarketData: false,
            includeTechnicalAnalysis: false,
            responseStyle: 'educational'
        };
    }
    /**
   * Gera resposta para análise de criptomoedas
   */ static generateCryptoAnalysisResponse(personality, context, intent) {
        const agentName = personality?.name || 'seu assistente de criptomoedas';
        const agentExpertise = personality?.expertise || 'análise de criptomoedas';
        const agentStyle = personality?.style || 'profissional e detalhado';
        const systemPrompt = `Você é ${agentName}, especialista em ${agentExpertise}.
Analise as criptomoedas mencionadas: ${intent.entities.join(', ')}.
Seja ${agentStyle} e adapte para usuário ${context.userExpertiseLevel}.
${context.topicContinuity ? 'Continue a análise anterior se relevante.' : ''}`;
        const userPrompt = `Analise as criptomoedas: ${intent.entities.join(', ')}.
Forneça insights ${agentExpertise.includes('técnica') ? 'técnicos' : 'estratégicos'}.
Considere o contexto: ${context.previousContext}.
Usuário é ${context.userExpertiseLevel}.`;
        return {
            systemPrompt,
            userPrompt,
            includeMarketData: true,
            includeTechnicalAnalysis: true,
            responseStyle: 'analytical'
        };
    }
    /**
   * Gera resposta para conselhos de trading
   */ static generateTradingAdviceResponse(personality, context, intent) {
        const agentName = personality?.name || 'seu assistente de criptomoedas';
        const agentExpertise = personality?.expertise || 'trading e análise de mercado';
        const agentStyle = personality?.style || 'profissional e cauteloso';
        const systemPrompt = `Você é ${agentName}, especialista em ${agentExpertise}.
Forneça conselhos de trading responsáveis. SEMPRE inclua disclaimers sobre riscos.
Seja ${agentStyle} mas cauteloso. Usuário: ${context.userExpertiseLevel}.`;
        const userPrompt = `O usuário busca conselhos de trading sobre: ${intent.entities.join(', ')}.
Forneça orientações estratégicas, mas sempre com disclaimers de risco.
Adapte para usuário ${context.userExpertiseLevel}.
${context.userMood === 'urgent' ? 'Usuário parece ansioso - seja tranquilizador.' : ''}`;
        return {
            systemPrompt,
            userPrompt,
            includeMarketData: true,
            includeTechnicalAnalysis: true,
            responseStyle: context.userMood === 'urgent' ? 'supportive' : 'analytical'
        };
    }
    /**
   * Gera resposta para perguntas gerais sobre mercado
   */ static generateMarketGeneralResponse(personality, context) {
        const agentName = personality?.name || 'seu assistente de criptomoedas';
        const agentStyle = personality?.style || 'analítico e informativo';
        const systemPrompt = `Você é ${agentName}, especialista em mercados de criptomoedas.
Forneça uma visão geral do mercado atual. Seja ${agentStyle}.
Usuário: ${context.userExpertiseLevel}.`;
        const userPrompt = `Forneça uma análise geral do mercado de criptomoedas atual.
Inclua sentimento, tendências principais e fatores relevantes.
Adapte para usuário ${context.userExpertiseLevel}.`;
        return {
            systemPrompt,
            userPrompt,
            includeMarketData: true,
            includeTechnicalAnalysis: false,
            responseStyle: 'analytical'
        };
    }
    /**
   * Gera resposta para comparações
   */ static generateComparisonResponse(personality, context, intent) {
        const agentName = personality?.name || 'seu assistente de criptomoedas';
        const agentStyle = personality?.style || 'objetivo e imparcial';
        const systemPrompt = `Você é ${agentName}. Compare as criptomoedas mencionadas: ${intent.entities.join(', ')}.
Seja ${agentStyle} e objetivo. Usuário: ${context.userExpertiseLevel}.`;
        const userPrompt = `Compare as criptomoedas: ${intent.entities.join(', ')}.
Analise prós, contras, casos de uso e potencial.
Seja imparcial e educativo para usuário ${context.userExpertiseLevel}.`;
        return {
            systemPrompt,
            userPrompt,
            includeMarketData: true,
            includeTechnicalAnalysis: true,
            responseStyle: 'educational'
        };
    }
    /**
   * Gera resposta para previsões de preço
   */ static generatePricePredictionResponse(personality, context, intent) {
        const agentName = personality?.name || 'seu assistente de criptomoedas';
        const agentStyle = personality?.style || 'responsável e educativo';
        const systemPrompt = `Você é ${agentName}. CUIDADO: Nunca faça previsões definitivas de preço.
Discuta fatores que podem influenciar preços, mas sempre com disclaimers.
Seja ${agentStyle} mas responsável.`;
        const userPrompt = `O usuário pergunta sobre previsões de preço para: ${intent.entities.join(', ')}.
Discuta fatores técnicos e fundamentais, mas NÃO faça previsões específicas.
Eduque sobre a imprevisibilidade do mercado.`;
        return {
            systemPrompt,
            userPrompt,
            includeMarketData: true,
            includeTechnicalAnalysis: true,
            responseStyle: 'educational'
        };
    }
    /**
   * Gera resposta para conversa pessoal
   */ static generatePersonalChatResponse(personality, context) {
        const agentName = personality?.name || 'seu assistente de criptomoedas';
        const systemPrompt = `Você é ${agentName}. O usuário quer conversar de forma mais pessoal.
Seja amigável mas mantenha o foco profissional. Redirecione gentilmente para criptomoedas.`;
        const userPrompt = `Responda de forma amigável mas profissional.
Mantenha sua personalidade de ${agentName} e redirecione para como pode ajudar com criptomoedas.`;
        return {
            systemPrompt,
            userPrompt,
            includeMarketData: false,
            includeTechnicalAnalysis: false,
            responseStyle: 'conversational'
        };
    }
    /**
   * Gera resposta padrão para casos não identificados
   */ static generateDefaultResponse(personality, context) {
        const agentName = personality?.name || 'seu assistente de criptomoedas';
        const systemPrompt = `Você é ${agentName}. A intenção do usuário não foi clara.
Peça esclarecimentos de forma educada e ofereça opções de como pode ajudar.`;
        const userPrompt = `A pergunta não foi clara. Peça esclarecimentos educadamente e
liste as principais formas como você pode ajudar com criptomoedas.`;
        return {
            systemPrompt,
            userPrompt,
            includeMarketData: false,
            includeTechnicalAnalysis: false,
            responseStyle: 'supportive'
        };
    }
}
}}),
"[project]/src/lib/ai-service.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AIService": (()=>AIService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/agents.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$crypto$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/crypto-api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$technical$2d$analysis$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/technical-analysis.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$intent$2d$classifier$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/intent-classifier.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$analyzer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/context-analyzer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$intelligent$2d$response$2d$generator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/intelligent-response-generator.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
class AIService {
    static context = {
        messages: [],
        activeAgent: 'ANALYST',
        marketSentiment: 'NEUTRAL'
    };
    /**
   * Processa uma mensagem do usuário e retorna resposta do agente ativo
   */ static async processMessage(userMessage, agentType) {
        // Atualiza agente ativo se especificado
        if (agentType) {
            this.context.activeAgent = agentType;
        }
        // Adiciona mensagem do usuário ao contexto
        const userMsg = {
            id: this.generateId(),
            content: userMessage,
            role: 'user',
            timestamp: new Date()
        };
        this.context.messages.push(userMsg);
        // NOVA INTELIGÊNCIA: Classifica a intenção da mensagem
        const intentClassification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$intent$2d$classifier$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["IntentClassifier"].classifyIntent(userMessage, this.context.messages.slice(0, -1) // Histórico sem a mensagem atual
        );
        // NOVA INTELIGÊNCIA: Analisa o contexto da conversa
        const contextAnalysis = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$analyzer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ContextAnalyzer"].analyzeContext(userMessage, this.context.messages.slice(0, -1), intentClassification, this.context.activeAgent);
        // Busca dados de crypto se necessário
        let cryptoData;
        let technicalAnalysis;
        let tradingSignal;
        // Só busca dados de crypto se a intenção requer
        if (this.shouldFetchCryptoData(intentClassification)) {
            const cryptoEntities = intentClassification.entities;
            if (cryptoEntities.length > 0) {
                this.context.currentCrypto = cryptoEntities[0]; // Usa a primeira crypto mencionada
                // Busca dados reais da criptomoeda
                const cryptoResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$crypto$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CryptoApiService"].getCryptoData(cryptoEntities[0]);
                if (cryptoResponse.success && cryptoResponse.data) {
                    cryptoData = cryptoResponse.data;
                    // Gera análise técnica
                    technicalAnalysis = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$technical$2d$analysis$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TechnicalAnalysisService"].generateTechnicalAnalysis(cryptoData);
                    // Gera sinal de trading
                    tradingSignal = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$technical$2d$analysis$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TechnicalAnalysisService"].generateTradingSignal(cryptoData, technicalAnalysis);
                }
            }
        }
        // NOVA INTELIGÊNCIA: Gera resposta inteligente baseada na intenção e contexto
        const agentResponse = await this.generateIntelligentResponse(userMessage, intentClassification, contextAnalysis, this.context.activeAgent, cryptoData, technicalAnalysis, tradingSignal);
        // Cria mensagem de resposta
        const assistantMsg = {
            id: this.generateId(),
            content: agentResponse,
            role: 'assistant',
            agentType: this.context.activeAgent,
            timestamp: new Date(),
            cryptoData,
            technicalAnalysis,
            tradingSignal
        };
        this.context.messages.push(assistantMsg);
        this.context.lastAnalysis = new Date();
        return assistantMsg;
    }
    /**
   * Determina se deve buscar dados de criptomoeda baseado na intenção
   */ static shouldFetchCryptoData(intent) {
        const cryptoIntents = [
            'CRYPTO_ANALYSIS',
            'TRADING_ADVICE',
            'COMPARISON',
            'PRICE_PREDICTION'
        ];
        return cryptoIntents.includes(intent.intent) && intent.entities.length > 0;
    }
    /**
   * NOVA INTELIGÊNCIA: Gera resposta inteligente baseada na intenção e contexto
   */ static async generateIntelligentResponse(userMessage, intent, context, agentType, cryptoData, technicalAnalysis, tradingSignal) {
        // Gera template de resposta baseado na intenção e contexto
        const responseTemplate = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$intelligent$2d$response$2d$generator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["IntelligentResponseGenerator"].generateResponseTemplate(intent, context, agentType, userMessage);
        // Para respostas que precisam de dados de mercado
        if (responseTemplate.includeMarketData && cryptoData && technicalAnalysis && tradingSignal) {
            return this.generateEnhancedCryptoResponse(responseTemplate, cryptoData, technicalAnalysis, tradingSignal, context);
        }
        // Para respostas simples (saudações, info do agente, etc.)
        if (intent.intent === 'AGENT_INFO') {
            return this.generateAgentInfoResponse(userMessage, agentType, context);
        }
        return this.generateSimpleResponse(responseTemplate, context, agentType);
    }
    /**
   * Gera resposta específica para perguntas sobre o agente
   */ static generateAgentInfoResponse(userMessage, agentType, context) {
        const agentNames = {
            'ANALYST': 'Sarah "The Wolf" Rodriguez',
            'MENTOR': 'Marcus "The Sage" Chen',
            'STRATEGIST': 'Alex "The Navigator" Kim'
        };
        const agentName = agentNames[agentType] || 'seu assistente de criptomoedas';
        // Detecta tipo específico de pergunta sobre o agente
        const isAboutAvailability = /\b(trabalha|funciona|disponível|disponivel|24\s*horas?|horário|horario)\b/i.test(userMessage);
        const isAboutCapabilities = /\b(pode|consegue|sabe|especialidades|habilidades|capacidades)\b/i.test(userMessage);
        const isAboutIdentity = /\b(quem\s+)?(você|voce)\s+(é|eh|sou)\b/i.test(userMessage);
        if (isAboutAvailability) {
            return `Olá! Sou ${agentName} 🤖

Sim, estou disponível **24 horas por dia, 7 dias por semana**! Como um assistente de IA, não preciso dormir ou descansar.

Estou sempre aqui para ajudá-lo com:
• Análises de criptomoedas em tempo real
• Estratégias de trading e investimento
• Educação sobre blockchain e DeFi
• Acompanhamento de mercado

Sobre qual criptomoeda gostaria de conversar agora? 🚀`;
        }
        if (isAboutCapabilities) {
            const capabilities = {
                'ANALYST': `Como Sarah "The Wolf" Rodriguez, minhas especialidades incluem:

🔍 **Análise Técnica Avançada**
• RSI, MACD, Bollinger Bands, Fibonacci
• Identificação de padrões de candlestick
• Análise de volume e momentum

⚡ **Gestão de Risco**
• Cálculo de stop-loss e take-profit
• Estratégias de money management
• Análise de confluências

📊 **Sinais de Trading**
• Identificação de pontos de entrada/saída
• Avaliação de força de tendências
• Timing de mercado`,
                'MENTOR': `Como Marcus "The Sage" Chen, posso educá-lo sobre:

📚 **Fundamentos Blockchain**
• Como funcionam diferentes protocolos
• Casos de uso e utilidade real
• Análise fundamentalista de projetos

🌱 **Estratégias de Investimento**
• Construção de portfólio diversificado
• DCA e estratégias de longo prazo
• Avaliação de risco/retorno

🧠 **Educação Financeira**
• Psicologia do investidor
• Planejamento financeiro
• Gestão emocional no trading`,
                'STRATEGIST': `Como Alex "The Navigator" Kim, ofereço:

🧭 **Visão Estratégica**
• Análise de tendências macro
• Identificação de narrativas emergentes
• Timing de ciclos de mercado

⚡ **Adaptação Tática**
• Rebalanceamento de portfólio
• Aproveitamento de volatilidade
• Estratégias multi-timeframe

🎯 **Planejamento**
• Cenários de mercado
• Correlações entre ativos
• Oportunidades de arbitragem`
            };
            return capabilities[agentType] || `Como ${agentName}, posso ajudá-lo com análises completas de criptomoedas!`;
        }
        if (isAboutIdentity) {
            const identities = {
                'ANALYST': `Sou Sarah "The Wolf" Rodriguez 🐺

**Minha História:**
15 anos navegando pelos mercados financeiros, especializada em análise técnica e gestão de risco. Ganhei o apelido "The Wolf" pela minha capacidade de farejar oportunidades no mercado cripto.

**Minha Filosofia:**
"Dados não mentem, mas o mercado pode surpreender." Sempre uso análise baseada em evidências, mas nunca esqueço que o mercado é imprevisível.

**Como Posso Ajudar:**
Transformo dados complexos em insights acionáveis para suas decisões de trading.`,
                'MENTOR': `Sou Marcus "The Sage" Chen 👨‍💼

**Minha Missão:**
Democratizar o conhecimento sobre criptomoedas e blockchain. Acredito que educação é o melhor investimento.

**Minha Abordagem:**
Ensino de forma didática e paciente, adaptando a linguagem ao nível de cada pessoa. Do iniciante ao avançado.

**Minha Paixão:**
Ver pessoas tomarem decisões financeiras mais inteligentes através do conhecimento sólido.`,
                'STRATEGIST': `Sou Alex "The Navigator" Kim 🧭

**Minha Expertise:**
Navegação estratégica em mercados voláteis. Especialista em identificar oportunidades onde outros veem apenas caos.

**Minha Metodologia:**
Combino análise macro com táticas adaptáveis. O mercado muda, nossa estratégia também deve mudar.

**Meu Diferencial:**
Visão de longo prazo com execução de curto prazo. Estratégia é sobre timing e adaptação.`
            };
            return identities[agentType] || `Sou ${agentName}, seu assistente especializado em criptomoedas!`;
        }
        // Resposta geral sobre o agente
        return `Sou ${agentName}, seu assistente de IA especializado em criptomoedas!

Estou aqui 24/7 para ajudá-lo com análises, estratégias e educação sobre o mercado cripto.

O que gostaria de saber? 🚀`;
    }
    /**
   * Gera resposta simples baseada no template
   */ static generateSimpleResponse(template, context, agentType) {
        const agentInfo = {
            type: agentType
        };
        // Respostas específicas baseadas no template
        switch(template.responseStyle){
            case 'conversational':
                return this.generateConversationalResponse(agentInfo, template, context);
            case 'educational':
                return this.generateEducationalResponse(agentInfo, template, context);
            case 'supportive':
                return this.generateSupportiveResponse(agentInfo, template, context);
            default:
                return this.generateDefaultAgentResponse(agentInfo, template, context);
        }
    }
    /**
   * Gera resposta conversacional
   */ static generateConversationalResponse(agent, template, context) {
        const greetings = {
            'ANALYST': [
                `Olá! Sou Sarah "The Wolf" Rodriguez 🐺. Como posso ajudá-lo com análises de criptomoedas hoje?`,
                `E aí! Sarah aqui, pronta para mergulhar nos dados do mercado cripto. Em que posso ajudar?`
            ],
            'MENTOR': [
                `Olá! Sou Marcus "The Sage" Chen 👨‍💼. Vamos explorar o mundo das criptomoedas juntos?`,
                `Bom dia! Prof. Marcus aqui, pronto para compartilhar conhecimento sobre o mercado cripto.`
            ],
            'STRATEGIST': [
                `Opa! Alex "The Navigator" Kim aqui 🧭. Pronto para navegar pelas oportunidades do mercado?`,
                `Olá! Alex aqui, vamos traçar estratégias inteligentes para o mercado cripto?`
            ]
        };
        const agentGreetings = greetings[agent.type] || greetings['ANALYST'];
        return agentGreetings[Math.floor(Math.random() * agentGreetings.length)];
    }
    /**
   * Gera resposta educacional
   */ static generateEducationalResponse(agent, template, context) {
        const helpResponses = {
            'ANALYST': `Como Sarah "The Wolf" Rodriguez, posso ajudá-lo com:

📊 **Análises Técnicas Detalhadas**
• Indicadores como RSI, MACD, Bollinger Bands
• Identificação de padrões de preço
• Sinais de entrada e saída

🎯 **Estratégias de Trading**
• Gestão de risco e money management
• Definição de stop-loss e take-profit
• Análise de confluências

💡 **Dicas Práticas**
• Como interpretar gráficos
• Timing de mercado
• Psicologia do trading

Sobre qual criptomoeda gostaria de uma análise?`,
            'MENTOR': `Como Marcus "The Sage" Chen, estou aqui para educá-lo sobre:

📚 **Fundamentos das Criptomoedas**
• Como funcionam as diferentes blockchains
• Casos de uso e utilidade dos tokens
• Análise fundamentalista

🌱 **Estratégias de Longo Prazo**
• Investimento vs Trading
• Diversificação de portfólio
• DCA (Dollar Cost Averaging)

🧠 **Educação Financeira**
• Gestão de risco
• Planejamento de investimentos
• Psicologia do investidor

Qual tópico gostaria de explorar primeiro?`,
            'STRATEGIST': `Como Alex "The Navigator" Kim, posso orientá-lo em:

🧭 **Estratégias de Mercado**
• Análise de tendências macro
• Timing de ciclos de mercado
• Oportunidades emergentes

⚡ **Adaptação Rápida**
• Como reagir a mudanças do mercado
• Rebalanceamento de portfólio
• Aproveitamento de volatilidade

🎯 **Visão Estratégica**
• Identificação de narrativas
• Análise de correlações
• Planejamento de cenários

Que estratégia podemos desenvolver juntos?`
        };
        return helpResponses[agent.type] || helpResponses['ANALYST'];
    }
    /**
   * Gera resposta de suporte
   */ static generateSupportiveResponse(agent, template, context) {
        const agentNames = {
            'ANALYST': 'Sarah "The Wolf" Rodriguez',
            'MENTOR': 'Marcus "The Sage" Chen',
            'STRATEGIST': 'Alex "The Navigator" Kim'
        };
        const agentName = agentNames[agent.type] || agent.name || 'seu assistente de criptomoedas';
        if (context.userMood === 'negative' || context.userMood === 'urgent') {
            return `Entendo sua preocupação. Como ${agentName}, estou aqui para ajudá-lo de forma clara e objetiva.

${context.userMood === 'urgent' ? '⚡ Vamos direto ao ponto:' : '🤝 Vamos resolver isso juntos:'}

Sobre qual criptomoeda específica você gostaria de uma análise? Ou prefere uma visão geral do mercado atual?

*Lembre-se: No mercado cripto, paciência e análise são fundamentais.* 💎`;
        }
        return `Como ${agentName}, estou aqui para ajudá-lo da melhor forma possível.

Posso fornecer análises detalhadas, explicar conceitos ou ajudar com estratégias. O que seria mais útil para você agora?`;
    }
    /**
   * Gera resposta padrão do agente
   */ static generateDefaultAgentResponse(agent, template, context) {
        const agentNames = {
            'ANALYST': 'Sarah "The Wolf" Rodriguez',
            'MENTOR': 'Marcus "The Sage" Chen',
            'STRATEGIST': 'Alex "The Navigator" Kim'
        };
        const agentName = agentNames[agent.type] || 'seu assistente de criptomoedas';
        return `Como ${agentName}, especialista em análise de criptomoedas, estou aqui para ajudá-lo.

Sobre qual criptomoeda específica gostaria de conversar?`;
    }
    /**
   * Gera resposta aprimorada com dados de crypto
   */ static generateEnhancedCryptoResponse(template, cryptoData, technicalAnalysis, tradingSignal, context) {
        // Usa a resposta existente como base, mas pode ser melhorada
        return this.generateCryptoAnalysisResponse(context.suggestedResponseTone === 'professional' ? 'ANALYST' : context.suggestedResponseTone === 'educational' ? 'MENTOR' : 'STRATEGIST', cryptoData, technicalAnalysis, tradingSignal, template.userPrompt);
    }
    /**
   * Detecta menção a criptomoedas na mensagem
   */ static detectCryptoMention(message) {
        const cryptoPatterns = [
            // Nomes completos
            /bitcoin/i,
            /ethereum/i,
            /cardano/i,
            /solana/i,
            /polkadot/i,
            /chainlink/i,
            /litecoin/i,
            /dogecoin/i,
            /shiba/i,
            /avalanche/i,
            // Símbolos
            /\bbtc\b/i,
            /\beth\b/i,
            /\bada\b/i,
            /\bsol\b/i,
            /\bdot\b/i,
            /\blink\b/i,
            /\bltc\b/i,
            /\bdoge\b/i,
            /\bshib\b/i,
            /\bavax\b/i,
            /\bmatic\b/i,
            /\bbnb\b/i,
            /\bxrp\b/i,
            /\batom\b/i,
            /\balgo\b/i
        ];
        const cryptoMap = {
            'bitcoin': 'bitcoin',
            'btc': 'bitcoin',
            'ethereum': 'ethereum',
            'eth': 'ethereum',
            'cardano': 'cardano',
            'ada': 'cardano',
            'solana': 'solana',
            'sol': 'solana',
            'polkadot': 'polkadot',
            'dot': 'polkadot',
            'chainlink': 'chainlink',
            'link': 'chainlink',
            'litecoin': 'litecoin',
            'ltc': 'litecoin',
            'dogecoin': 'dogecoin',
            'doge': 'dogecoin',
            'shiba': 'shiba-inu',
            'shib': 'shiba-inu',
            'avalanche': 'avalanche-2',
            'avax': 'avalanche-2',
            'matic': 'matic-network',
            'polygon': 'matic-network',
            'bnb': 'binancecoin',
            'binance': 'binancecoin',
            'xrp': 'ripple',
            'ripple': 'ripple',
            'atom': 'cosmos',
            'cosmos': 'cosmos',
            'algo': 'algorand',
            'algorand': 'algorand'
        };
        for (const pattern of cryptoPatterns){
            const match = message.match(pattern);
            if (match) {
                const found = match[0].toLowerCase();
                return cryptoMap[found] || found;
            }
        }
        return null;
    }
    /**
   * Verifica se é uma saudação
   */ static isGreeting(message) {
        const greetingPatterns = [
            /^(oi|olá|ola|hey|hi|hello|bom dia|boa tarde|boa noite)/i,
            /^(e aí|eai|como vai|tudo bem|beleza)/i
        ];
        return greetingPatterns.some((pattern)=>pattern.test(message.trim()));
    }
    /**
   * Verifica se é pergunta geral sobre mercado
   */ static isGeneralMarketQuestion(message) {
        const marketPatterns = [
            /mercado/i,
            /cripto/i,
            /crypto/i,
            /trading/i,
            /investir/i,
            /comprar/i,
            /vender/i,
            /análise/i,
            /tendência/i,
            /previsão/i
        ];
        return marketPatterns.some((pattern)=>pattern.test(message));
    }
    /**
   * Gera resposta de saudação
   */ static generateGreetingResponse(agentName, personality) {
        const greetings = {
            'Dr. Marcus Chen': [
                "Olá! Sou o Dr. Marcus Chen 👨‍💼. Como especialista em análise técnica, estou aqui para ajudá-lo com análises precisas do mercado cripto. Sobre qual criptomoeda gostaria de conversar?",
                "Bom dia! Dr. Marcus Chen aqui 📊. Pronto para mergulhar nos dados e indicadores técnicos? Qual análise posso fazer para você hoje?"
            ],
            'Sarah "The Wolf" Rodriguez': [
                "E aí, parceiro! 🐺 Sarah Rodriguez na área. 15 anos batalhando nos mercados e pronta para compartilhar a real sobre trading. Em que posso ajudar?",
                "Opa! The Wolf aqui 💎. Mercado tá pegando fogo hoje, né? Conta aí, qual crypto tá na sua mira?"
            ],
            'Prof. Elena Nakamura': [
                "Olá! Sou a Prof. Elena Nakamura 👩‍🏫. Vamos explorar juntos o fascinante mundo das criptomoedas? Posso ajudá-lo com análises fundamentais e contexto de mercado.",
                "Bom dia! Prof. Elena aqui 📚. Sempre é um prazer discutir as tendências e o futuro do mercado cripto. O que gostaria de aprender hoje?"
            ]
        };
        const agentGreetings = greetings[agentName] || [
            "Olá! Como posso ajudá-lo com análises de criptomoedas hoje?"
        ];
        return agentGreetings[Math.floor(Math.random() * agentGreetings.length)];
    }
    /**
   * Gera resposta com análise de criptomoeda
   */ static generateCryptoAnalysisResponse(agentType, cryptoData, technicalAnalysis, tradingSignal, userMessage) {
        const agent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAgent"])(agentType);
        const price = cryptoData.current_price.toLocaleString('pt-BR', {
            style: 'currency',
            currency: 'USD'
        });
        const change24h = cryptoData.price_change_percentage_24h.toFixed(2);
        const changeEmoji = cryptoData.price_change_percentage_24h >= 0 ? '📈' : '📉';
        switch(agentType){
            case 'ANALYST':
            case 'technical_analyst':
                return `📊 **Sarah "The Wolf" Rodriguez - Análise Técnica**
**${cryptoData.name} (${cryptoData.symbol.toUpperCase()})**

**Preço Atual:** ${price} ${changeEmoji} ${change24h}%

**Indicadores Técnicos:**
• **RSI (14):** ${technicalAnalysis.rsi.toFixed(1)} ${technicalAnalysis.rsi < 30 ? '(Sobrevenda 🟢)' : technicalAnalysis.rsi > 70 ? '(Sobrecompra 🔴)' : '(Neutro 🟡)'}
• **MACD:** ${technicalAnalysis.macd.macd > technicalAnalysis.macd.signal ? 'Bullish 📈' : 'Bearish 📉'}
• **SMA20:** ${technicalAnalysis.sma.sma20.toLocaleString('pt-BR', {
                    style: 'currency',
                    currency: 'USD'
                })}
• **Bollinger:** Preço ${cryptoData.current_price < technicalAnalysis.bollinger.lower ? 'abaixo da banda inferior 🟢' : cryptoData.current_price > technicalAnalysis.bollinger.upper ? 'acima da banda superior 🔴' : 'dentro das bandas 🟡'}

**Sinal de Trading:** ${tradingSignal.type} ${tradingSignal.strength} ⚡
**Confiança:** ${tradingSignal.confidence}%
**Target:** ${tradingSignal.price_target.toLocaleString('pt-BR', {
                    style: 'currency',
                    currency: 'USD'
                })}
**Stop-Loss:** ${tradingSignal.stop_loss.toLocaleString('pt-BR', {
                    style: 'currency',
                    currency: 'USD'
                })}

**Confluência de Sinais:** ${tradingSignal.reasoning}

*Como sempre digo: dados não mentem, mas o mercado pode surpreender. Use stop-loss!* 🐺`;
            case 'MENTOR':
            case 'market_consultant':
                return `👨‍💼 **Marcus "The Sage" Chen - Análise Educativa**
**${cryptoData.name} (${cryptoData.symbol.toUpperCase()})**

**Contexto Educativo:**
${cryptoData.name} está cotado a ${price}, com variação de ${change24h}% em 24h.

**Posição no Mercado:**
• **Ranking:** #${cryptoData.market_cap_rank} por market cap
• **Market Cap:** $${(cryptoData.market_cap / 1000000000).toFixed(2)}B
• **Volume 24h:** $${(cryptoData.total_volume / 1000000).toFixed(0)}M
• **Supply:** ${(cryptoData.circulating_supply / 1000000).toFixed(0)}M ${cryptoData.symbol.toUpperCase()}

**Análise Fundamentalista:**
${this.getFundamentalAnalysis(cryptoData)}

**Lição de Hoje:**
${tradingSignal.type === 'BUY' ? '🌱 Os fundamentos sugerem potencial, mas lembre-se: investimento é maratona, não sprint' : tradingSignal.type === 'SELL' ? '⚠️ Alguns indicadores preocupam - hora de revisar sua tese de investimento' : '📊 Fundamentos mistos - paciência é virtude no mercado cripto'}

**Fatores Educativos a Observar:**
• Utilidade real do projeto
• Equipe e parcerias
• Roadmap e desenvolvimento
• Adoção e comunidade

*Lembre-se: conhecimento é o melhor investimento. Estude antes de investir.* 📚`;
            case 'STRATEGIST':
            case 'experienced_trader':
                return `🧭 **Alex "The Navigator" Kim - Visão Estratégica**
**${cryptoData.name} (${cryptoData.symbol.toUpperCase()})**

**Situação Atual:** ${price} ${changeEmoji} ${change24h}%

**Leitura Estratégica:**
${tradingSignal.type === 'BUY' ? '🚀 Vejo oportunidade tática aqui - momento pode ser favorável' : tradingSignal.type === 'SELL' ? '⚠️ Sinais sugerem cautela - hora de reavaliar posição' : '🤔 Cenário misto - aguardar melhor definição'}

**Setup Estratégico:**
• **Ação:** ${tradingSignal.type === 'BUY' ? 'Acumulação gradual' : tradingSignal.type === 'SELL' ? 'Redução de exposição' : 'Observação ativa'}
• **Confiança:** ${tradingSignal.confidence}%
• **Target:** ${tradingSignal.price_target.toLocaleString('pt-BR', {
                    style: 'currency',
                    currency: 'USD'
                })}
• **Proteção:** ${tradingSignal.stop_loss.toLocaleString('pt-BR', {
                    style: 'currency',
                    currency: 'USD'
                })}

**Volume:** ${(cryptoData.total_volume / 1000000).toFixed(0)}M - ${cryptoData.total_volume > cryptoData.market_cap * 0.1 ? 'Liquidez forte 💪' : 'Liquidez limitada 😴'}

**Navegação Tática:** ${technicalAnalysis.rsi < 30 ? 'RSI oversold - oportunidade para posicionamento' : technicalAnalysis.rsi > 70 ? 'RSI overbought - cautela recomendada' : 'RSI neutro - aguardar confirmação direcional'}

*Estratégia é adaptação constante. Mantenha-se flexível e disciplinado.* ⚡`;
            default:
                return "Análise não disponível para este agente.";
        }
    }
    /**
   * Gera análise fundamentalista básica
   */ static getFundamentalAnalysis(cryptoData) {
        const analyses = [];
        if (cryptoData.market_cap_rank <= 10) {
            analyses.push("Projeto consolidado entre os top 10");
        }
        if (cryptoData.price_change_percentage_24h > 10) {
            analyses.push("Alta volatilidade recente pode indicar interesse especulativo");
        }
        if (cryptoData.total_volume > cryptoData.market_cap * 0.1) {
            analyses.push("Alto volume de negociação indica liquidez saudável");
        }
        return analyses.join('. ') || "Projeto em desenvolvimento com métricas estáveis";
    }
    /**
   * Gera resposta para perguntas gerais sobre mercado
   */ static generateGeneralMarketResponse(agentType, userMessage) {
        const responses = {
            'ANALYST': "Como Sarah 'The Wolf' Rodriguez, sempre baseio minhas análises em dados concretos. Sobre qual criptomoeda específica gostaria de uma análise técnica detalhada? 📊🐺",
            'technical_analyst': "Como Sarah 'The Wolf' Rodriguez, sempre baseio minhas análises em dados concretos. Sobre qual criptomoeda específica gostaria de uma análise técnica detalhada? 📊🐺",
            'MENTOR': "Como Marcus 'The Sage' Chen, acredito que educação é fundamental. Sobre qual projeto cripto gostaria de aprender mais? Posso explicar os fundamentos! 👨‍💼📚",
            'market_consultant': "Como Marcus 'The Sage' Chen, acredito que educação é fundamental. Sobre qual projeto cripto gostaria de aprender mais? Posso explicar os fundamentos! 👨‍💼📚",
            'STRATEGIST': "Como Alex 'The Navigator' Kim, vejo o mercado cripto em constante evolução. Qual oportunidade estratégica podemos explorar juntos? 🧭⚡",
            'experienced_trader': "Como Alex 'The Navigator' Kim, vejo o mercado cripto em constante evolução. Qual oportunidade estratégica podemos explorar juntos? 🧭⚡"
        };
        return responses[agentType] || "Como posso ajudá-lo com análises de criptomoedas hoje?";
    }
    /**
   * Gera resposta contextual
   */ static generateContextualResponse(agentType, userMessage) {
        const responses = {
            'ANALYST': `Como Sarah "The Wolf" Rodriguez 🐺, especialista em análise técnica e gestão de risco, posso ajudá-lo com análises precisas baseadas em dados. Sobre qual criptomoeda específica gostaria de conversar?`,
            'technical_analyst': `Como Sarah "The Wolf" Rodriguez 🐺, especialista em análise técnica e gestão de risco, posso ajudá-lo com análises precisas baseadas em dados. Sobre qual criptomoeda específica gostaria de conversar?`,
            'MENTOR': `Como Marcus "The Sage" Chen 👨‍💼, especialista em educação e estratégias de longo prazo, posso ajudá-lo a entender melhor o mercado cripto. Qual tópico gostaria de explorar?`,
            'market_consultant': `Como Marcus "The Sage" Chen 👨‍💼, especialista em educação e estratégias de longo prazo, posso ajudá-lo a entender melhor o mercado cripto. Qual tópico gostaria de explorar?`,
            'STRATEGIST': `Como Alex "The Navigator" Kim 🧭, especialista em estratégias de mercado e timing, posso ajudá-lo a navegar pelas oportunidades do mercado cripto. Que estratégia podemos desenvolver?`,
            'experienced_trader': `Como Alex "The Navigator" Kim 🧭, especialista em estratégias de mercado e timing, posso ajudá-lo a navegar pelas oportunidades do mercado cripto. Que estratégia podemos desenvolver?`
        };
        return responses[agentType] || "Como posso ajudá-lo com análises de criptomoedas hoje?";
    }
    /**
   * Gera ID único
   */ static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    /**
   * Obtém contexto atual
   */ static getContext() {
        return {
            ...this.context
        };
    }
    /**
   * Limpa contexto
   */ static clearContext() {
        this.context = {
            messages: [],
            activeAgent: 'ANALYST',
            marketSentiment: 'NEUTRAL'
        };
    }
    /**
   * Troca agente ativo
   */ static switchAgent(agentType) {
        this.context.activeAgent = agentType;
    }
}
}}),
"[project]/src/lib/utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn),
    "formatCurrency": (()=>formatCurrency),
    "formatDate": (()=>formatDate),
    "formatNumber": (()=>formatNumber),
    "formatPercentage": (()=>formatPercentage),
    "getTimeAgo": (()=>getTimeAgo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-ssr] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatCurrency(value) {
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 6
    }).format(value);
}
function formatPercentage(value) {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
}
function formatNumber(value) {
    if (value >= 1e9) {
        return `${(value / 1e9).toFixed(2)}B`;
    }
    if (value >= 1e6) {
        return `${(value / 1e6).toFixed(2)}M`;
    }
    if (value >= 1e3) {
        return `${(value / 1e3).toFixed(2)}K`;
    }
    return value.toFixed(2);
}
function formatDate(date) {
    return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(date);
}
function getTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    if (diffInSeconds < 60) {
        return 'agora mesmo';
    }
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
        return `${diffInMinutes}min atrás`;
    }
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
        return `${diffInHours}h atrás`;
    }
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d atrás`;
}
}}),
"[project]/src/components/chat/AgentSelector.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AgentSelector": (()=>AgentSelector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-ssr] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chart-column.js [app-ssr] (ecmascript) <export default as BarChart3>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-ssr] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$graduation$2d$cap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__GraduationCap$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/graduation-cap.js [app-ssr] (ecmascript) <export default as GraduationCap>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function AgentSelector({ activeAgent, onAgentChange }) {
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const agents = [
        {
            id: 'technical_analyst',
            name: 'Dr. Marcus Chen',
            title: 'Analista Técnico',
            description: 'Especialista em indicadores e análise técnica',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"],
            emoji: '👨‍💼',
            color: 'from-blue-500 to-cyan-500'
        },
        {
            id: 'experienced_trader',
            name: 'Sarah "The Wolf" Rodriguez',
            title: 'Trader Experiente',
            description: '15 anos de experiência em trading',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"],
            emoji: '🐺',
            color: 'from-green-500 to-emerald-500'
        },
        {
            id: 'market_consultant',
            name: 'Prof. Elena Nakamura',
            title: 'Consultora de Mercado',
            description: 'Análise fundamentalista e tendências macro',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$graduation$2d$cap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__GraduationCap$3e$__["GraduationCap"],
            emoji: '👩‍🏫',
            color: 'from-purple-500 to-pink-500'
        }
    ];
    const currentAgent = agents.find((agent)=>agent.id === activeAgent);
    const handleAgentSelect = (agentId)=>{
        onAgentChange(agentId);
        setIsOpen(false);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                onClick: ()=>setIsOpen(!isOpen),
                className: "flex items-center space-x-3 bg-white/10 hover:bg-white/20 border border-purple-500/30 rounded-lg px-4 py-2 transition-all duration-200",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("w-8 h-8 rounded-full flex items-center justify-center bg-gradient-to-r", currentAgent?.color),
                        children: currentAgent && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(currentAgent.icon, {
                            className: "w-4 h-4 text-white"
                        }, void 0, false, {
                            fileName: "[project]/src/components/chat/AgentSelector.tsx",
                            lineNumber: 64,
                            columnNumber: 28
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/chat/AgentSelector.tsx",
                        lineNumber: 60,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-left",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm font-medium text-white",
                                children: currentAgent?.name
                            }, void 0, false, {
                                fileName: "[project]/src/components/chat/AgentSelector.tsx",
                                lineNumber: 68,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs text-purple-300",
                                children: currentAgent?.title
                            }, void 0, false, {
                                fileName: "[project]/src/components/chat/AgentSelector.tsx",
                                lineNumber: 71,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chat/AgentSelector.tsx",
                        lineNumber: 67,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("w-4 h-4 text-purple-300 transition-transform duration-200", isOpen && "rotate-180")
                    }, void 0, false, {
                        fileName: "[project]/src/components/chat/AgentSelector.tsx",
                        lineNumber: 76,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/chat/AgentSelector.tsx",
                lineNumber: 56,
                columnNumber: 7
            }, this),
            isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "fixed inset-0 z-10",
                        onClick: ()=>setIsOpen(false)
                    }, void 0, false, {
                        fileName: "[project]/src/components/chat/AgentSelector.tsx",
                        lineNumber: 86,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute right-0 top-full mt-2 w-80 bg-slate-800/95 backdrop-blur-sm border border-purple-500/30 rounded-lg shadow-xl z-20",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-2",
                                children: agents.map((agent)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>handleAgentSelect(agent.id),
                                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 text-left", activeAgent === agent.id ? "bg-purple-500/20 border border-purple-500/50" : "hover:bg-white/10"),
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("w-10 h-10 rounded-full flex items-center justify-center bg-gradient-to-r flex-shrink-0", agent.color),
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(agent.icon, {
                                                    className: "w-5 h-5 text-white"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/chat/AgentSelector.tsx",
                                                    lineNumber: 109,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/chat/AgentSelector.tsx",
                                                lineNumber: 105,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex-1 min-w-0",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center space-x-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-sm font-medium text-white truncate",
                                                                children: agent.name
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/chat/AgentSelector.tsx",
                                                                lineNumber: 114,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-lg",
                                                                children: agent.emoji
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/chat/AgentSelector.tsx",
                                                                lineNumber: 117,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/chat/AgentSelector.tsx",
                                                        lineNumber: 113,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-xs text-purple-300 mb-1",
                                                        children: agent.title
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/chat/AgentSelector.tsx",
                                                        lineNumber: 119,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-xs text-gray-400",
                                                        children: agent.description
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/chat/AgentSelector.tsx",
                                                        lineNumber: 122,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/chat/AgentSelector.tsx",
                                                lineNumber: 112,
                                                columnNumber: 19
                                            }, this),
                                            activeAgent === agent.id && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-2 h-2 bg-purple-500 rounded-full flex-shrink-0"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/chat/AgentSelector.tsx",
                                                lineNumber: 128,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, agent.id, true, {
                                        fileName: "[project]/src/components/chat/AgentSelector.tsx",
                                        lineNumber: 95,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/chat/AgentSelector.tsx",
                                lineNumber: 93,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "border-t border-purple-500/20 p-3",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-xs text-gray-400 text-center",
                                    children: "Cada agente tem especialidades únicas para melhor atendê-lo"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/chat/AgentSelector.tsx",
                                    lineNumber: 135,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/chat/AgentSelector.tsx",
                                lineNumber: 134,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chat/AgentSelector.tsx",
                        lineNumber: 92,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/chat/AgentSelector.tsx",
        lineNumber: 54,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/chat/CryptoDataCard.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CryptoDataCard": (()=>CryptoDataCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-ssr] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-down.js [app-ssr] (ecmascript) <export default as TrendingDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/activity.js [app-ssr] (ecmascript) <export default as Activity>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/target.js [app-ssr] (ecmascript) <export default as Target>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/shield.js [app-ssr] (ecmascript) <export default as Shield>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
function CryptoDataCard({ cryptoData, technicalAnalysis, tradingSignal }) {
    const isPositive = cryptoData.price_change_percentage_24h >= 0;
    const getSignalColor = (signal)=>{
        switch(signal.type){
            case 'BUY':
                return 'from-green-500 to-emerald-500';
            case 'SELL':
                return 'from-red-500 to-rose-500';
            default:
                return 'from-yellow-500 to-orange-500';
        }
    };
    const getSignalIcon = (signal)=>{
        switch(signal.type){
            case 'BUY':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"];
            case 'SELL':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingDown$3e$__["TrendingDown"];
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__["Activity"];
        }
    };
    const getStrengthColor = (strength)=>{
        switch(strength){
            case 'STRONG':
                return 'text-green-400';
            case 'MODERATE':
                return 'text-yellow-400';
            default:
                return 'text-gray-400';
        }
    };
    const SignalIcon = getSignalIcon(tradingSignal);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-lg p-4 space-y-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-xs font-bold text-white",
                                    children: cryptoData.symbol.toUpperCase()
                                }, void 0, false, {
                                    fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                    lineNumber: 62,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 61,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "font-semibold text-white",
                                        children: cryptoData.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 67,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-gray-400",
                                        children: [
                                            "#",
                                            cryptoData.market_cap_rank
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 68,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 66,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                        lineNumber: 60,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-right",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-lg font-bold text-white",
                                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatCurrency"])(cryptoData.current_price)
                            }, void 0, false, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 73,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-sm font-medium flex items-center", isPositive ? "text-green-400" : "text-red-400"),
                                children: [
                                    isPositive ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                        className: "w-3 h-3 mr-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 80,
                                        columnNumber: 27
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingDown$3e$__["TrendingDown"], {
                                        className: "w-3 h-3 mr-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 80,
                                        columnNumber: 69
                                    }, this),
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatPercentage"])(cryptoData.price_change_percentage_24h)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 76,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                        lineNumber: 72,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                lineNumber: 59,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-2 gap-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs text-gray-400",
                                        children: "Market Cap"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 90,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs text-white",
                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatNumber"])(cryptoData.market_cap)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 91,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 89,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs text-gray-400",
                                        children: "Volume 24h"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 94,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs text-white",
                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatNumber"])(cryptoData.total_volume)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 95,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 93,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                        lineNumber: 88,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs text-gray-400",
                                        children: "High 24h"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 101,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs text-white",
                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatCurrency"])(cryptoData.high_24h)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 102,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 100,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs text-gray-400",
                                        children: "Low 24h"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 105,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs text-white",
                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatCurrency"])(cryptoData.low_24h)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 106,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 104,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                        lineNumber: 99,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                lineNumber: 87,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-t border-slate-700/50 pt-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                        className: "text-sm font-medium text-white mb-2",
                        children: "Indicadores Técnicos"
                    }, void 0, false, {
                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                        lineNumber: 113,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-3 gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs text-gray-400",
                                        children: "RSI"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 116,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-sm font-medium", technicalAnalysis.rsi < 30 ? "text-green-400" : technicalAnalysis.rsi > 70 ? "text-red-400" : "text-yellow-400"),
                                        children: technicalAnalysis.rsi.toFixed(1)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 117,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 115,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs text-gray-400",
                                        children: "MACD"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 127,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-sm font-medium", technicalAnalysis.macd.macd > technicalAnalysis.macd.signal ? "text-green-400" : "text-red-400"),
                                        children: technicalAnalysis.macd.macd > technicalAnalysis.macd.signal ? "Bullish" : "Bearish"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 128,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 126,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs text-gray-400",
                                        children: "SMA20"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 137,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-sm font-medium", cryptoData.current_price > technicalAnalysis.sma.sma20 ? "text-green-400" : "text-red-400"),
                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatCurrency"])(technicalAnalysis.sma.sma20)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 138,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 136,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                        lineNumber: 114,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                lineNumber: 112,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-t border-slate-700/50 pt-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between mb-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "text-sm font-medium text-white",
                                children: "Sinal de Trading"
                            }, void 0, false, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 151,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                        className: "w-3 h-3 text-gray-400"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 153,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs text-gray-400",
                                        children: tradingSignal.timeframe
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 154,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 152,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                        lineNumber: 150,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("bg-gradient-to-r rounded-lg p-3", getSignalColor(tradingSignal)),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between mb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center space-x-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SignalIcon, {
                                                className: "w-5 h-5 text-white"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                                lineNumber: 164,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold text-white",
                                                children: tradingSignal.type
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                                lineNumber: 165,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-sm", getStrengthColor(tradingSignal.strength)),
                                                children: tradingSignal.strength
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                                lineNumber: 166,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 163,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-white font-bold",
                                        children: [
                                            tradingSignal.confidence,
                                            "%"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 170,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 162,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-2 gap-3 text-white",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center space-x-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__["Target"], {
                                                className: "w-3 h-3"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                                lineNumber: 177,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-xs",
                                                children: "Target:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                                lineNumber: 178,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-xs font-medium",
                                                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatCurrency"])(tradingSignal.price_target)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                                lineNumber: 179,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 176,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center space-x-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__["Shield"], {
                                                className: "w-3 h-3"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                                lineNumber: 185,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-xs",
                                                children: "Stop:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                                lineNumber: 186,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-xs font-medium",
                                                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatCurrency"])(tradingSignal.stop_loss)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                                lineNumber: 187,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 184,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 175,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                        lineNumber: 158,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                lineNumber: 149,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-t border-slate-700/50 pt-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                        className: "text-sm font-medium text-white mb-2",
                        children: "Suporte & Resistência"
                    }, void 0, false, {
                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                        lineNumber: 197,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-2 gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs text-gray-400 mb-1",
                                        children: "Suporte"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 200,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-1",
                                        children: technicalAnalysis.support_resistance.support.slice(0, 2).map((level, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-xs text-green-400",
                                                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatCurrency"])(level)
                                            }, index, false, {
                                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                                lineNumber: 203,
                                                columnNumber: 17
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 201,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 199,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs text-gray-400 mb-1",
                                        children: "Resistência"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 211,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-1",
                                        children: technicalAnalysis.support_resistance.resistance.slice(0, 2).map((level, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-xs text-red-400",
                                                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatCurrency"])(level)
                                            }, index, false, {
                                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                                lineNumber: 214,
                                                columnNumber: 17
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                        lineNumber: 212,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                                lineNumber: 210,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                        lineNumber: 198,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
                lineNumber: 196,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/chat/CryptoDataCard.tsx",
        lineNumber: 57,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/chat/MessageBubble.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MessageBubble": (()=>MessageBubble)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-ssr] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chart-column.js [app-ssr] (ecmascript) <export default as BarChart3>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-ssr] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$graduation$2d$cap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__GraduationCap$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/graduation-cap.js [app-ssr] (ecmascript) <export default as GraduationCap>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/copy.js [app-ssr] (ecmascript) <export default as Copy>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$CryptoDataCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/chat/CryptoDataCard.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function MessageBubble({ message }) {
    const isUser = message.role === 'user';
    const getAgentIcon = (agent)=>{
        if (!agent) return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"];
        const icons = {
            'technical_analyst': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"],
            'experienced_trader': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"],
            'market_consultant': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$graduation$2d$cap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__GraduationCap$3e$__["GraduationCap"]
        };
        return icons[agent] || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"];
    };
    const getAgentColor = (agent)=>{
        if (!agent) return 'from-gray-500 to-gray-600';
        const colors = {
            'technical_analyst': 'from-blue-500 to-cyan-500',
            'experienced_trader': 'from-green-500 to-emerald-500',
            'market_consultant': 'from-purple-500 to-pink-500'
        };
        return colors[agent] || 'from-gray-500 to-gray-600';
    };
    const getAgentName = (agent)=>{
        if (!agent) return 'Usuário';
        const names = {
            'technical_analyst': 'Dr. Marcus Chen',
            'experienced_trader': 'Sarah "The Wolf" Rodriguez',
            'market_consultant': 'Prof. Elena Nakamura'
        };
        return names[agent] || 'Assistente';
    };
    const copyToClipboard = async (text)=>{
        try {
            await navigator.clipboard.writeText(text);
        // Aqui você poderia adicionar um toast de sucesso
        } catch (err) {
            console.error('Erro ao copiar texto:', err);
        }
    };
    const formatMessageContent = (content)=>{
        // Converte markdown básico para HTML
        return content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>').replace(/\*(.*?)\*/g, '<em>$1</em>').replace(/`(.*?)`/g, '<code class="bg-black/20 px-1 py-0.5 rounded text-sm">$1</code>').replace(/\n/g, '<br />');
    };
    const IconComponent = getAgentIcon(message.agent);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex items-start space-x-3", isUser ? "flex-row-reverse space-x-reverse" : "flex-row"),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 bg-gradient-to-r", isUser ? "from-slate-600 to-slate-700" : getAgentColor(message.agent)),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(IconComponent, {
                    className: "w-5 h-5 text-white"
                }, void 0, false, {
                    fileName: "[project]/src/components/chat/MessageBubble.tsx",
                    lineNumber: 82,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/chat/MessageBubble.tsx",
                lineNumber: 78,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex-1 max-w-3xl", isUser ? "flex flex-col items-end" : "flex flex-col items-start"),
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex items-center space-x-2 mb-1", isUser ? "flex-row-reverse space-x-reverse" : "flex-row"),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm font-medium text-white",
                                children: isUser ? 'Você' : getAgentName(message.agent)
                            }, void 0, false, {
                                fileName: "[project]/src/components/chat/MessageBubble.tsx",
                                lineNumber: 95,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-xs text-gray-400",
                                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTimeAgo"])(message.timestamp)
                            }, void 0, false, {
                                fileName: "[project]/src/components/chat/MessageBubble.tsx",
                                lineNumber: 98,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chat/MessageBubble.tsx",
                        lineNumber: 91,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("rounded-lg px-4 py-3 max-w-full", isUser ? "bg-gradient-to-r from-purple-600 to-pink-600 text-white" : "bg-slate-800/80 text-gray-100 border border-slate-700/50"),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "prose prose-sm max-w-none prose-invert",
                                dangerouslySetInnerHTML: {
                                    __html: formatMessageContent(message.content)
                                }
                            }, void 0, false, {
                                fileName: "[project]/src/components/chat/MessageBubble.tsx",
                                lineNumber: 110,
                                columnNumber: 11
                            }, this),
                            !isUser && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-end space-x-2 mt-3 pt-2 border-t border-slate-700/50",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>copyToClipboard(message.content),
                                    className: "p-1 text-gray-400 hover:text-white transition-colors duration-200",
                                    title: "Copiar mensagem",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__["Copy"], {
                                        className: "w-4 h-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/MessageBubble.tsx",
                                        lineNumber: 125,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/chat/MessageBubble.tsx",
                                    lineNumber: 120,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/chat/MessageBubble.tsx",
                                lineNumber: 119,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chat/MessageBubble.tsx",
                        lineNumber: 104,
                        columnNumber: 9
                    }, this),
                    message.cryptoData && message.technicalAnalysis && message.tradingSignal && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-3 w-full",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$CryptoDataCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CryptoDataCard"], {
                            cryptoData: message.cryptoData,
                            technicalAnalysis: message.technicalAnalysis,
                            tradingSignal: message.tradingSignal
                        }, void 0, false, {
                            fileName: "[project]/src/components/chat/MessageBubble.tsx",
                            lineNumber: 134,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/chat/MessageBubble.tsx",
                        lineNumber: 133,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/chat/MessageBubble.tsx",
                lineNumber: 86,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/chat/MessageBubble.tsx",
        lineNumber: 73,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/chat/TypingIndicator.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TypingIndicator": (()=>TypingIndicator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chart-column.js [app-ssr] (ecmascript) <export default as BarChart3>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-ssr] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$graduation$2d$cap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__GraduationCap$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/graduation-cap.js [app-ssr] (ecmascript) <export default as GraduationCap>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
function TypingIndicator({ agent }) {
    const getAgentIcon = (agentType)=>{
        const icons = {
            'technical_analyst': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"],
            'experienced_trader': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"],
            'market_consultant': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$graduation$2d$cap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__GraduationCap$3e$__["GraduationCap"]
        };
        return icons[agentType];
    };
    const getAgentColor = (agentType)=>{
        const colors = {
            'technical_analyst': 'from-blue-500 to-cyan-500',
            'experienced_trader': 'from-green-500 to-emerald-500',
            'market_consultant': 'from-purple-500 to-pink-500'
        };
        return colors[agentType];
    };
    const getAgentName = (agentType)=>{
        const names = {
            'technical_analyst': 'Dr. Marcus Chen',
            'experienced_trader': 'Sarah "The Wolf" Rodriguez',
            'market_consultant': 'Prof. Elena Nakamura'
        };
        return names[agentType];
    };
    const getTypingMessage = (agentType)=>{
        const messages = {
            'technical_analyst': 'Analisando indicadores técnicos...',
            'experienced_trader': 'Checando o mercado...',
            'market_consultant': 'Consultando dados fundamentais...'
        };
        return messages[agentType];
    };
    const IconComponent = getAgentIcon(agent);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-start space-x-3",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 bg-gradient-to-r", getAgentColor(agent)),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(IconComponent, {
                    className: "w-5 h-5 text-white"
                }, void 0, false, {
                    fileName: "[project]/src/components/chat/TypingIndicator.tsx",
                    lineNumber: 58,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/chat/TypingIndicator.tsx",
                lineNumber: 54,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 max-w-3xl",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-2 mb-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm font-medium text-white",
                                children: getAgentName(agent)
                            }, void 0, false, {
                                fileName: "[project]/src/components/chat/TypingIndicator.tsx",
                                lineNumber: 65,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-xs text-gray-400",
                                children: "agora"
                            }, void 0, false, {
                                fileName: "[project]/src/components/chat/TypingIndicator.tsx",
                                lineNumber: 68,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chat/TypingIndicator.tsx",
                        lineNumber: 64,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-slate-800/80 border border-slate-700/50 rounded-lg px-4 py-3",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex space-x-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-2 h-2 bg-purple-500 rounded-full animate-bounce",
                                            style: {
                                                animationDelay: '0ms'
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/chat/TypingIndicator.tsx",
                                            lineNumber: 78,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-2 h-2 bg-purple-500 rounded-full animate-bounce",
                                            style: {
                                                animationDelay: '150ms'
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/chat/TypingIndicator.tsx",
                                            lineNumber: 79,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-2 h-2 bg-purple-500 rounded-full animate-bounce",
                                            style: {
                                                animationDelay: '300ms'
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/chat/TypingIndicator.tsx",
                                            lineNumber: 80,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/chat/TypingIndicator.tsx",
                                    lineNumber: 77,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm text-gray-300",
                                    children: getTypingMessage(agent)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/chat/TypingIndicator.tsx",
                                    lineNumber: 83,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/chat/TypingIndicator.tsx",
                            lineNumber: 75,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/chat/TypingIndicator.tsx",
                        lineNumber: 74,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/chat/TypingIndicator.tsx",
                lineNumber: 62,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/chat/TypingIndicator.tsx",
        lineNumber: 52,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/chat/ChatInterface.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChatInterface": (()=>ChatInterface)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$send$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Send$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/send.js [app-ssr] (ecmascript) <export default as Send>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bot.js [app-ssr] (ecmascript) <export default as Bot>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-service.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ChatContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/ChatContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$AgentSelector$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/chat/AgentSelector.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$MessageBubble$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/chat/MessageBubble.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$TypingIndicator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/chat/TypingIndicator.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
function ChatInterface() {
    const { state, addMessage, setActiveAgent, setLoading, setCurrentCrypto, setLastAnalysis } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ChatContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useChatContext"])();
    const [inputValue, setInputValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const messagesEndRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const inputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const scrollToBottom = ()=>{
        messagesEndRef.current?.scrollIntoView({
            behavior: 'smooth'
        });
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        scrollToBottom();
    }, [
        state.messages
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Mensagem de boas-vindas apenas se não há mensagens
        if (state.messages.length === 0) {
            const welcomeMessage = {
                id: 'welcome',
                content: `Olá! 👋 Bem-vindo ao **CryptoAgent AI**!

Sou seu assistente especializado em criptomoedas com 3 agentes especializados:

🔹 **Dr. Marcus Chen** 👨‍💼 - Analista Técnico
🔹 **Sarah "The Wolf" Rodriguez** 🐺 - Trader Experiente
🔹 **Prof. Elena Nakamura** 👩‍🏫 - Consultora de Mercado

Posso ajudá-lo com:
• Análises técnicas em tempo real
• Sinais de compra/venda
• Contexto de mercado e tendências
• Estratégias de trading

**Para começar, digite o nome de uma criptomoeda** (ex: Bitcoin, ETH, Solana) ou faça uma pergunta sobre o mercado! 🚀`,
                role: 'assistant',
                agent: state.activeAgent,
                timestamp: new Date()
            };
            addMessage(welcomeMessage);
        }
    }, [
        state.messages.length,
        state.activeAgent,
        addMessage
    ]);
    const handleSendMessage = async ()=>{
        if (!inputValue.trim() || state.isLoading) return;
        const userMessage = inputValue.trim();
        setInputValue('');
        setLoading(true);
        try {
            // Adiciona mensagem do usuário
            const userMsg = {
                id: Date.now().toString(),
                content: userMessage,
                role: 'user',
                timestamp: new Date()
            };
            addMessage(userMsg);
            // Processa mensagem com IA
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AIService"].processMessage(userMessage, state.activeAgent);
            addMessage(response);
            // Atualiza contexto se necessário
            if (response.cryptoData) {
                setCurrentCrypto(response.cryptoData.id);
            }
            if (response.technicalAnalysis) {
                setLastAnalysis(new Date());
            }
        } catch (error) {
            console.error('Erro ao processar mensagem:', error);
            const errorMsg = {
                id: Date.now().toString(),
                content: 'Desculpe, ocorreu um erro ao processar sua mensagem. Tente novamente.',
                role: 'assistant',
                agent: state.activeAgent,
                timestamp: new Date()
            };
            addMessage(errorMsg);
        } finally{
            setLoading(false);
            inputRef.current?.focus();
        }
    };
    const handleKeyPress = (e)=>{
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };
    const handleAgentChange = (newAgent)=>{
        setActiveAgent(newAgent);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AIService"].switchAgent(newAgent);
        // Adiciona mensagem informando sobre a troca
        const switchMessage = {
            id: Date.now().toString(),
            content: `Agente alterado para **${getAgentName(newAgent)}** ${getAgentEmoji(newAgent)}`,
            role: 'assistant',
            agent: newAgent,
            timestamp: new Date()
        };
        addMessage(switchMessage);
    };
    const getAgentName = (agent)=>{
        const names = {
            'technical_analyst': 'Dr. Marcus Chen',
            'experienced_trader': 'Sarah "The Wolf" Rodriguez',
            'market_consultant': 'Prof. Elena Nakamura'
        };
        return names[agent];
    };
    const getAgentEmoji = (agent)=>{
        const emojis = {
            'technical_analyst': '👨‍💼',
            'experienced_trader': '🐺',
            'market_consultant': '👩‍🏫'
        };
        return emojis[agent];
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-4xl mx-auto flex items-center justify-between",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__["Bot"], {
                                        className: "w-6 h-6 text-white"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/ChatInterface.tsx",
                                        lineNumber: 160,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/chat/ChatInterface.tsx",
                                    lineNumber: 159,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                            className: "text-xl font-bold text-white",
                                            children: "CryptoAgent AI"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/chat/ChatInterface.tsx",
                                            lineNumber: 163,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm text-purple-300",
                                            children: [
                                                "Agente ativo: ",
                                                getAgentName(state.activeAgent),
                                                " ",
                                                getAgentEmoji(state.activeAgent)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/chat/ChatInterface.tsx",
                                            lineNumber: 164,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/chat/ChatInterface.tsx",
                                    lineNumber: 162,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/chat/ChatInterface.tsx",
                            lineNumber: 158,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$AgentSelector$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AgentSelector"], {
                            activeAgent: state.activeAgent,
                            onAgentChange: handleAgentChange
                        }, void 0, false, {
                            fileName: "[project]/src/components/chat/ChatInterface.tsx",
                            lineNumber: 170,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/chat/ChatInterface.tsx",
                    lineNumber: 157,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/chat/ChatInterface.tsx",
                lineNumber: 156,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 overflow-y-auto p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-4xl mx-auto space-y-4",
                    children: [
                        state.messages.map((message)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$MessageBubble$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MessageBubble"], {
                                message: message
                            }, message.id, false, {
                                fileName: "[project]/src/components/chat/ChatInterface.tsx",
                                lineNumber: 181,
                                columnNumber: 13
                            }, this)),
                        state.isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$TypingIndicator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TypingIndicator"], {
                            agent: state.activeAgent
                        }, void 0, false, {
                            fileName: "[project]/src/components/chat/ChatInterface.tsx",
                            lineNumber: 184,
                            columnNumber: 31
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            ref: messagesEndRef
                        }, void 0, false, {
                            fileName: "[project]/src/components/chat/ChatInterface.tsx",
                            lineNumber: 186,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/chat/ChatInterface.tsx",
                    lineNumber: 179,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/chat/ChatInterface.tsx",
                lineNumber: 178,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-black/20 backdrop-blur-sm border-t border-purple-500/20 p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-4xl mx-auto",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-1 relative",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                        ref: inputRef,
                                        type: "text",
                                        value: inputValue,
                                        onChange: (e)=>setInputValue(e.target.value),
                                        onKeyPress: handleKeyPress,
                                        placeholder: "Digite o nome de uma criptomoeda ou faça uma pergunta...",
                                        className: "w-full bg-white/10 border border-purple-500/30 rounded-lg px-4 py-3 text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",
                                        disabled: state.isLoading
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/ChatInterface.tsx",
                                        lineNumber: 195,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/chat/ChatInterface.tsx",
                                    lineNumber: 194,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: handleSendMessage,
                                    disabled: !inputValue.trim() || state.isLoading,
                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("p-3 rounded-lg transition-all duration-200", inputValue.trim() && !state.isLoading ? "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white shadow-lg" : "bg-gray-600 text-gray-400 cursor-not-allowed"),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$send$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Send$3e$__["Send"], {
                                        className: "w-5 h-5"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/chat/ChatInterface.tsx",
                                        lineNumber: 217,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/chat/ChatInterface.tsx",
                                    lineNumber: 207,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/chat/ChatInterface.tsx",
                            lineNumber: 193,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-2 text-xs text-purple-300 text-center",
                            children: "Pressione Enter para enviar • Shift+Enter para nova linha"
                        }, void 0, false, {
                            fileName: "[project]/src/components/chat/ChatInterface.tsx",
                            lineNumber: 221,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/chat/ChatInterface.tsx",
                    lineNumber: 192,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/chat/ChatInterface.tsx",
                lineNumber: 191,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/chat/ChatInterface.tsx",
        lineNumber: 154,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__50ae24d2._.js.map