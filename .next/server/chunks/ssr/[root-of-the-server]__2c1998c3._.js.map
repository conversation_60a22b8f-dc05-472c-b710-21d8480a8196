{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/lib/agents.ts"], "sourcesContent": ["import { Agent, AgentType } from '@/types';\n\nexport const agents: Record<AgentType, Agent> = {\n  technical_analyst: {\n    id: 'technical_analyst',\n    name: 'Dr. <PERSON>',\n    description: 'Especialista em Análise Técnica com PhD em Matemática Financeira',\n    personality: '<PERSON><PERSON><PERSON><PERSON><PERSON>, preciso e metodológico. Fala de forma técnica mas acessível, sempre baseando suas análises em dados concretos e indicadores matemáticos.',\n    specialties: [\n      'Análise de indicadores técnicos (RSI, MACD, Bollinger)',\n      'Padrões de candlestick e formações gráficas',\n      'Níveis de suporte e resistência',\n      'Análise de volume e momentum',\n      'Fibonacci e ondas de Elliott'\n    ],\n    avatar: '👨‍💼',\n    systemPrompt: `Você é Dr. <PERSON>, um analista técnico experiente com PhD em Matemática Financeira. \n\nPERSONALIDADE:\n- Analítico e metodológico\n- Sempre baseia análises em dados concretos\n- Explica conceitos técnicos de forma acessível\n- Usa linguagem profissional mas amigável\n- Gosta de números e estatísticas\n\nESPECIALIDADES:\n- Indicadores técnicos (RSI, MACD, SMA, EMA, Bollinger Bands)\n- Padrões de candlestick e formações gráficas\n- Análise de volume e momentum\n- Níveis de suporte e resistência\n- Fibonacci e análise de ondas\n\nESTILO DE COMUNICAÇÃO:\n- Sempre cumprimente de forma educada\n- Use dados reais e atuais do mercado\n- Explique o \"porquê\" por trás de cada análise\n- Forneça níveis específicos de entrada, stop-loss e take-profit\n- Mencione a confluência de indicadores\n- Use emojis técnicos: 📊 📈 📉 ⚡ 🎯\n\nIMPORTANTE: Sempre busque dados reais e atuais das criptomoedas antes de fazer qualquer análise. Nunca invente números ou dados.`\n  },\n\n  experienced_trader: {\n    id: 'experienced_trader',\n    name: 'Sarah \"The Wolf\" Rodriguez',\n    description: 'Trader veterana com 15 anos de experiência em mercados financeiros',\n    personality: 'Direta, confiante e pragmática. Fala como alguém que já viu de tudo no mercado, com histórias e experiências práticas.',\n    specialties: [\n      'Gestão de risco e money management',\n      'Psicologia do trading',\n      'Estratégias de entrada e saída',\n      'Análise de mercado em tempo real',\n      'Trading de alta frequência'\n    ],\n    avatar: '🐺',\n    systemPrompt: `Você é Sarah \"The Wolf\" Rodriguez, uma trader veterana com 15 anos batalhando nos mercados.\n\nPERSONALIDADE:\n- Direta e sem rodeios\n- Confiante baseada em experiência\n- Pragmática e focada em resultados\n- Conta histórias e experiências do mercado\n- Tem um toque de humor sarcástico\n\nESPECIALIDADES:\n- Gestão de risco e money management\n- Psicologia do trading e controle emocional\n- Estratégias de entrada e saída\n- Leitura de mercado em tempo real\n- Experiência com diferentes ciclos de mercado\n\nESTILO DE COMUNICAÇÃO:\n- Use gírias do trading (\"pump\", \"dump\", \"HODL\", \"diamond hands\")\n- Conte experiências pessoais (fictícias mas realistas)\n- Seja direta sobre riscos e oportunidades\n- Foque em aspectos práticos do trading\n- Use emojis de trader: 🐺 💎 🚀 🔥 💰 ⚡ 📈 📉\n\nFRASES TÍPICAS:\n- \"Já vi essa história antes...\"\n- \"No mercado, quem não tem disciplina, não tem dinheiro\"\n- \"Risk management é tudo, parceiro\"\n- \"O mercado não perdoa amadores\"\n\nIMPORTANTE: Sempre busque dados reais e atuais. Baseie conselhos em experiência prática de trading.`\n  },\n\n  market_consultant: {\n    id: 'market_consultant',\n    name: 'Prof. Elena Nakamura',\n    description: 'Consultora de mercado e professora de Economia Digital',\n    personality: 'Educativa, paciente e abrangente. Gosta de explicar o contexto macro e as tendências de longo prazo.',\n    specialties: [\n      'Análise fundamentalista',\n      'Tendências macroeconômicas',\n      'Regulamentações e políticas',\n      'Adoção institucional',\n      'Tecnologia blockchain'\n    ],\n    avatar: '👩‍🏫',\n    systemPrompt: `Você é Prof. Elena Nakamura, consultora de mercado e professora de Economia Digital.\n\nPERSONALIDADE:\n- Educativa e didática\n- Paciente para explicar conceitos complexos\n- Visão macro e de longo prazo\n- Gosta de contextualizar historicamente\n- Sempre atualizada com notícias e regulamentações\n\nESPECIALIDADES:\n- Análise fundamentalista de criptomoedas\n- Impacto de notícias e regulamentações\n- Tendências macroeconômicas globais\n- Adoção institucional e empresarial\n- Tecnologia blockchain e casos de uso\n\nESTILO DE COMUNICAÇÃO:\n- Explique conceitos de forma didática\n- Contextualize com eventos históricos\n- Mencione notícias recentes relevantes\n- Foque no \"big picture\" do mercado\n- Use emojis educativos: 👩‍🏫 📚 🌍 💡 🔍 📰 🏛️\n\nABORDAGEM:\n- Sempre contextualize a pergunta no cenário macro\n- Explique causas e consequências\n- Mencione fatores fundamentais\n- Discuta tendências de longo prazo\n- Relacione com adoção e regulamentação\n\nIMPORTANTE: Mantenha-se atualizada com notícias recentes do mercado cripto e economia global.`\n  }\n};\n\nexport function getAgent(agentType: AgentType): Agent {\n  return agents[agentType];\n}\n\nexport function getAllAgents(): Agent[] {\n  return Object.values(agents);\n}\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,SAAmC;IAC9C,mBAAmB;QACjB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,aAAa;YACX;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;QACR,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;gIAwB6G,CAAC;IAC/H;IAEA,oBAAoB;QAClB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,aAAa;YACX;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;QACR,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mGA6BgF,CAAC;IAClG;IAEA,mBAAmB;QACjB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,aAAa;YACX;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;QACR,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6FA8B0E,CAAC;IAC5F;AACF;AAEO,SAAS,SAAS,SAAoB;IAC3C,OAAO,MAAM,CAAC,UAAU;AAC1B;AAEO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/lib/crypto-api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { CryptoData, MarketData, TechnicalIndicators, ApiResponse } from '@/types';\n\nconst COINGECKO_BASE_URL = 'https://api.coingecko.com/api/v3';\nconst FEAR_GREED_API = 'https://api.alternative.me/fng/';\n\n// Cache simples para evitar muitas requisições\nconst cache = new Map<string, { data: any; timestamp: number }>();\nconst CACHE_DURATION = 60000; // 1 minuto\n\nfunction getCachedData<T>(key: string): T | null {\n  const cached = cache.get(key);\n  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n    return cached.data as T;\n  }\n  return null;\n}\n\nfunction setCachedData(key: string, data: any): void {\n  cache.set(key, { data, timestamp: Date.now() });\n}\n\nexport class CryptoApiService {\n  \n  /**\n   * Busca dados de uma criptomoeda específica\n   */\n  static async getCryptoData(coinId: string): Promise<ApiResponse<CryptoData>> {\n    try {\n      const cacheKey = `crypto-${coinId}`;\n      const cached = getCachedData<CryptoData>(cacheKey);\n      \n      if (cached) {\n        return {\n          success: true,\n          data: cached,\n          timestamp: new Date()\n        };\n      }\n\n      const response = await axios.get(\n        `${COINGECKO_BASE_URL}/coins/${coinId}`,\n        {\n          params: {\n            localization: false,\n            tickers: false,\n            market_data: true,\n            community_data: false,\n            developer_data: false,\n            sparkline: false\n          }\n        }\n      );\n\n      const data: CryptoData = {\n        id: response.data.id,\n        symbol: response.data.symbol,\n        name: response.data.name,\n        current_price: response.data.market_data.current_price.usd,\n        market_cap: response.data.market_data.market_cap.usd,\n        market_cap_rank: response.data.market_cap_rank,\n        fully_diluted_valuation: response.data.market_data.fully_diluted_valuation?.usd || 0,\n        total_volume: response.data.market_data.total_volume.usd,\n        high_24h: response.data.market_data.high_24h.usd,\n        low_24h: response.data.market_data.low_24h.usd,\n        price_change_24h: response.data.market_data.price_change_24h,\n        price_change_percentage_24h: response.data.market_data.price_change_percentage_24h,\n        market_cap_change_24h: response.data.market_data.market_cap_change_24h,\n        market_cap_change_percentage_24h: response.data.market_data.market_cap_change_percentage_24h,\n        circulating_supply: response.data.market_data.circulating_supply,\n        total_supply: response.data.market_data.total_supply,\n        max_supply: response.data.market_data.max_supply,\n        ath: response.data.market_data.ath.usd,\n        ath_change_percentage: response.data.market_data.ath_change_percentage.usd,\n        ath_date: response.data.market_data.ath_date.usd,\n        atl: response.data.market_data.atl.usd,\n        atl_change_percentage: response.data.market_data.atl_change_percentage.usd,\n        atl_date: response.data.market_data.atl_date.usd,\n        last_updated: response.data.last_updated\n      };\n\n      setCachedData(cacheKey, data);\n\n      return {\n        success: true,\n        data,\n        timestamp: new Date()\n      };\n\n    } catch (error) {\n      console.error('Erro ao buscar dados da crypto:', error);\n      return {\n        success: false,\n        error: 'Erro ao buscar dados da criptomoeda',\n        timestamp: new Date()\n      };\n    }\n  }\n\n  /**\n   * Busca dados de múltiplas criptomoedas\n   */\n  static async getMultipleCryptoData(coinIds: string[]): Promise<ApiResponse<CryptoData[]>> {\n    try {\n      const cacheKey = `multiple-${coinIds.join(',')}`;\n      const cached = getCachedData<CryptoData[]>(cacheKey);\n      \n      if (cached) {\n        return {\n          success: true,\n          data: cached,\n          timestamp: new Date()\n        };\n      }\n\n      const response = await axios.get(\n        `${COINGECKO_BASE_URL}/coins/markets`,\n        {\n          params: {\n            vs_currency: 'usd',\n            ids: coinIds.join(','),\n            order: 'market_cap_desc',\n            per_page: coinIds.length,\n            page: 1,\n            sparkline: false,\n            price_change_percentage: '24h'\n          }\n        }\n      );\n\n      const data: CryptoData[] = response.data.map((coin: any) => ({\n        id: coin.id,\n        symbol: coin.symbol,\n        name: coin.name,\n        current_price: coin.current_price,\n        market_cap: coin.market_cap,\n        market_cap_rank: coin.market_cap_rank,\n        fully_diluted_valuation: coin.fully_diluted_valuation || 0,\n        total_volume: coin.total_volume,\n        high_24h: coin.high_24h,\n        low_24h: coin.low_24h,\n        price_change_24h: coin.price_change_24h,\n        price_change_percentage_24h: coin.price_change_percentage_24h,\n        market_cap_change_24h: coin.market_cap_change_24h,\n        market_cap_change_percentage_24h: coin.market_cap_change_percentage_24h,\n        circulating_supply: coin.circulating_supply,\n        total_supply: coin.total_supply,\n        max_supply: coin.max_supply,\n        ath: coin.ath,\n        ath_change_percentage: coin.ath_change_percentage,\n        ath_date: coin.ath_date,\n        atl: coin.atl,\n        atl_change_percentage: coin.atl_change_percentage,\n        atl_date: coin.atl_date,\n        last_updated: coin.last_updated\n      }));\n\n      setCachedData(cacheKey, data);\n\n      return {\n        success: true,\n        data,\n        timestamp: new Date()\n      };\n\n    } catch (error) {\n      console.error('Erro ao buscar dados de múltiplas cryptos:', error);\n      return {\n        success: false,\n        error: 'Erro ao buscar dados das criptomoedas',\n        timestamp: new Date()\n      };\n    }\n  }\n\n  /**\n   * Busca dados globais do mercado\n   */\n  static async getMarketData(): Promise<ApiResponse<MarketData>> {\n    try {\n      const cacheKey = 'market-data';\n      const cached = getCachedData<MarketData>(cacheKey);\n      \n      if (cached) {\n        return {\n          success: true,\n          data: cached,\n          timestamp: new Date()\n        };\n      }\n\n      const [globalResponse, fearGreedResponse, trendingResponse] = await Promise.all([\n        axios.get(`${COINGECKO_BASE_URL}/global`),\n        axios.get(FEAR_GREED_API),\n        axios.get(`${COINGECKO_BASE_URL}/search/trending`)\n      ]);\n\n      const data: MarketData = {\n        global: globalResponse.data.data,\n        fear_greed_index: fearGreedResponse.data.data[0].value,\n        trending_coins: trendingResponse.data.coins.map((coin: any) => coin.item.id)\n      };\n\n      setCachedData(cacheKey, data);\n\n      return {\n        success: true,\n        data,\n        timestamp: new Date()\n      };\n\n    } catch (error) {\n      console.error('Erro ao buscar dados do mercado:', error);\n      return {\n        success: false,\n        error: 'Erro ao buscar dados do mercado',\n        timestamp: new Date()\n      };\n    }\n  }\n\n  /**\n   * Busca lista de criptomoedas populares\n   */\n  static async getPopularCoins(limit: number = 10): Promise<ApiResponse<CryptoData[]>> {\n    try {\n      const response = await axios.get(\n        `${COINGECKO_BASE_URL}/coins/markets`,\n        {\n          params: {\n            vs_currency: 'usd',\n            order: 'market_cap_desc',\n            per_page: limit,\n            page: 1,\n            sparkline: false,\n            price_change_percentage: '24h'\n          }\n        }\n      );\n\n      const data: CryptoData[] = response.data.map((coin: any) => ({\n        id: coin.id,\n        symbol: coin.symbol,\n        name: coin.name,\n        current_price: coin.current_price,\n        market_cap: coin.market_cap,\n        market_cap_rank: coin.market_cap_rank,\n        fully_diluted_valuation: coin.fully_diluted_valuation || 0,\n        total_volume: coin.total_volume,\n        high_24h: coin.high_24h,\n        low_24h: coin.low_24h,\n        price_change_24h: coin.price_change_24h,\n        price_change_percentage_24h: coin.price_change_percentage_24h,\n        market_cap_change_24h: coin.market_cap_change_24h,\n        market_cap_change_percentage_24h: coin.market_cap_change_percentage_24h,\n        circulating_supply: coin.circulating_supply,\n        total_supply: coin.total_supply,\n        max_supply: coin.max_supply,\n        ath: coin.ath,\n        ath_change_percentage: coin.ath_change_percentage,\n        ath_date: coin.ath_date,\n        atl: coin.atl,\n        atl_change_percentage: coin.atl_change_percentage,\n        atl_date: coin.atl_date,\n        last_updated: coin.last_updated\n      }));\n\n      return {\n        success: true,\n        data,\n        timestamp: new Date()\n      };\n\n    } catch (error) {\n      console.error('Erro ao buscar moedas populares:', error);\n      return {\n        success: false,\n        error: 'Erro ao buscar moedas populares',\n        timestamp: new Date()\n      };\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGA,MAAM,qBAAqB;AAC3B,MAAM,iBAAiB;AAEvB,+CAA+C;AAC/C,MAAM,QAAQ,IAAI;AAClB,MAAM,iBAAiB,OAAO,WAAW;AAEzC,SAAS,cAAiB,GAAW;IACnC,MAAM,SAAS,MAAM,GAAG,CAAC;IACzB,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,gBAAgB;QAC5D,OAAO,OAAO,IAAI;IACpB;IACA,OAAO;AACT;AAEA,SAAS,cAAc,GAAW,EAAE,IAAS;IAC3C,MAAM,GAAG,CAAC,KAAK;QAAE;QAAM,WAAW,KAAK,GAAG;IAAG;AAC/C;AAEO,MAAM;IAEX;;GAEC,GACD,aAAa,cAAc,MAAc,EAAoC;QAC3E,IAAI;YACF,MAAM,WAAW,CAAC,OAAO,EAAE,QAAQ;YACnC,MAAM,SAAS,cAA0B;YAEzC,IAAI,QAAQ;gBACV,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,WAAW,IAAI;gBACjB;YACF;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,GAAG,mBAAmB,OAAO,EAAE,QAAQ,EACvC;gBACE,QAAQ;oBACN,cAAc;oBACd,SAAS;oBACT,aAAa;oBACb,gBAAgB;oBAChB,gBAAgB;oBAChB,WAAW;gBACb;YACF;YAGF,MAAM,OAAmB;gBACvB,IAAI,SAAS,IAAI,CAAC,EAAE;gBACpB,QAAQ,SAAS,IAAI,CAAC,MAAM;gBAC5B,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,eAAe,SAAS,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,GAAG;gBAC1D,YAAY,SAAS,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG;gBACpD,iBAAiB,SAAS,IAAI,CAAC,eAAe;gBAC9C,yBAAyB,SAAS,IAAI,CAAC,WAAW,CAAC,uBAAuB,EAAE,OAAO;gBACnF,cAAc,SAAS,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG;gBACxD,UAAU,SAAS,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG;gBAChD,SAAS,SAAS,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG;gBAC9C,kBAAkB,SAAS,IAAI,CAAC,WAAW,CAAC,gBAAgB;gBAC5D,6BAA6B,SAAS,IAAI,CAAC,WAAW,CAAC,2BAA2B;gBAClF,uBAAuB,SAAS,IAAI,CAAC,WAAW,CAAC,qBAAqB;gBACtE,kCAAkC,SAAS,IAAI,CAAC,WAAW,CAAC,gCAAgC;gBAC5F,oBAAoB,SAAS,IAAI,CAAC,WAAW,CAAC,kBAAkB;gBAChE,cAAc,SAAS,IAAI,CAAC,WAAW,CAAC,YAAY;gBACpD,YAAY,SAAS,IAAI,CAAC,WAAW,CAAC,UAAU;gBAChD,KAAK,SAAS,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG;gBACtC,uBAAuB,SAAS,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,GAAG;gBAC1E,UAAU,SAAS,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG;gBAChD,KAAK,SAAS,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG;gBACtC,uBAAuB,SAAS,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,GAAG;gBAC1E,UAAU,SAAS,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG;gBAChD,cAAc,SAAS,IAAI,CAAC,YAAY;YAC1C;YAEA,cAAc,UAAU;YAExB,OAAO;gBACL,SAAS;gBACT;gBACA,WAAW,IAAI;YACjB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,WAAW,IAAI;YACjB;QACF;IACF;IAEA;;GAEC,GACD,aAAa,sBAAsB,OAAiB,EAAsC;QACxF,IAAI;YACF,MAAM,WAAW,CAAC,SAAS,EAAE,QAAQ,IAAI,CAAC,MAAM;YAChD,MAAM,SAAS,cAA4B;YAE3C,IAAI,QAAQ;gBACV,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,WAAW,IAAI;gBACjB;YACF;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,GAAG,mBAAmB,cAAc,CAAC,EACrC;gBACE,QAAQ;oBACN,aAAa;oBACb,KAAK,QAAQ,IAAI,CAAC;oBAClB,OAAO;oBACP,UAAU,QAAQ,MAAM;oBACxB,MAAM;oBACN,WAAW;oBACX,yBAAyB;gBAC3B;YACF;YAGF,MAAM,OAAqB,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC3D,IAAI,KAAK,EAAE;oBACX,QAAQ,KAAK,MAAM;oBACnB,MAAM,KAAK,IAAI;oBACf,eAAe,KAAK,aAAa;oBACjC,YAAY,KAAK,UAAU;oBAC3B,iBAAiB,KAAK,eAAe;oBACrC,yBAAyB,KAAK,uBAAuB,IAAI;oBACzD,cAAc,KAAK,YAAY;oBAC/B,UAAU,KAAK,QAAQ;oBACvB,SAAS,KAAK,OAAO;oBACrB,kBAAkB,KAAK,gBAAgB;oBACvC,6BAA6B,KAAK,2BAA2B;oBAC7D,uBAAuB,KAAK,qBAAqB;oBACjD,kCAAkC,KAAK,gCAAgC;oBACvE,oBAAoB,KAAK,kBAAkB;oBAC3C,cAAc,KAAK,YAAY;oBAC/B,YAAY,KAAK,UAAU;oBAC3B,KAAK,KAAK,GAAG;oBACb,uBAAuB,KAAK,qBAAqB;oBACjD,UAAU,KAAK,QAAQ;oBACvB,KAAK,KAAK,GAAG;oBACb,uBAAuB,KAAK,qBAAqB;oBACjD,UAAU,KAAK,QAAQ;oBACvB,cAAc,KAAK,YAAY;gBACjC,CAAC;YAED,cAAc,UAAU;YAExB,OAAO;gBACL,SAAS;gBACT;gBACA,WAAW,IAAI;YACjB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,WAAW,IAAI;YACjB;QACF;IACF;IAEA;;GAEC,GACD,aAAa,gBAAkD;QAC7D,IAAI;YACF,MAAM,WAAW;YACjB,MAAM,SAAS,cAA0B;YAEzC,IAAI,QAAQ;gBACV,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,WAAW,IAAI;gBACjB;YACF;YAEA,MAAM,CAAC,gBAAgB,mBAAmB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC9E,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,mBAAmB,OAAO,CAAC;gBACxC,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC;gBACV,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,mBAAmB,gBAAgB,CAAC;aAClD;YAED,MAAM,OAAmB;gBACvB,QAAQ,eAAe,IAAI,CAAC,IAAI;gBAChC,kBAAkB,kBAAkB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK;gBACtD,gBAAgB,iBAAiB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAc,KAAK,IAAI,CAAC,EAAE;YAC7E;YAEA,cAAc,UAAU;YAExB,OAAO;gBACL,SAAS;gBACT;gBACA,WAAW,IAAI;YACjB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,WAAW,IAAI;YACjB;QACF;IACF;IAEA;;GAEC,GACD,aAAa,gBAAgB,QAAgB,EAAE,EAAsC;QACnF,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,GAAG,mBAAmB,cAAc,CAAC,EACrC;gBACE,QAAQ;oBACN,aAAa;oBACb,OAAO;oBACP,UAAU;oBACV,MAAM;oBACN,WAAW;oBACX,yBAAyB;gBAC3B;YACF;YAGF,MAAM,OAAqB,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC3D,IAAI,KAAK,EAAE;oBACX,QAAQ,KAAK,MAAM;oBACnB,MAAM,KAAK,IAAI;oBACf,eAAe,KAAK,aAAa;oBACjC,YAAY,KAAK,UAAU;oBAC3B,iBAAiB,KAAK,eAAe;oBACrC,yBAAyB,KAAK,uBAAuB,IAAI;oBACzD,cAAc,KAAK,YAAY;oBAC/B,UAAU,KAAK,QAAQ;oBACvB,SAAS,KAAK,OAAO;oBACrB,kBAAkB,KAAK,gBAAgB;oBACvC,6BAA6B,KAAK,2BAA2B;oBAC7D,uBAAuB,KAAK,qBAAqB;oBACjD,kCAAkC,KAAK,gCAAgC;oBACvE,oBAAoB,KAAK,kBAAkB;oBAC3C,cAAc,KAAK,YAAY;oBAC/B,YAAY,KAAK,UAAU;oBAC3B,KAAK,KAAK,GAAG;oBACb,uBAAuB,KAAK,qBAAqB;oBACjD,UAAU,KAAK,QAAQ;oBACvB,KAAK,KAAK,GAAG;oBACb,uBAAuB,KAAK,qBAAqB;oBACjD,UAAU,KAAK,QAAQ;oBACvB,cAAc,KAAK,YAAY;gBACjC,CAAC;YAED,OAAO;gBACL,SAAS;gBACT;gBACA,WAAW,IAAI;YACjB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,WAAW,IAAI;YACjB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/lib/technical-analysis.ts"], "sourcesContent": ["import { CryptoData, TechnicalIndicators, TradingSignal } from '@/types';\n\nexport class TechnicalAnalysisService {\n  \n  /**\n   * Calcula RSI (Relative Strength Index)\n   */\n  static calculateRSI(prices: number[], period: number = 14): number {\n    if (prices.length < period + 1) return 50; // Valor neutro se não há dados suficientes\n    \n    let gains = 0;\n    let losses = 0;\n    \n    // Calcula ganhos e perdas iniciais\n    for (let i = 1; i <= period; i++) {\n      const change = prices[i] - prices[i - 1];\n      if (change > 0) {\n        gains += change;\n      } else {\n        losses += Math.abs(change);\n      }\n    }\n    \n    let avgGain = gains / period;\n    let avgLoss = losses / period;\n    \n    // Calcula RSI para o resto dos dados\n    for (let i = period + 1; i < prices.length; i++) {\n      const change = prices[i] - prices[i - 1];\n      const gain = change > 0 ? change : 0;\n      const loss = change < 0 ? Math.abs(change) : 0;\n      \n      avgGain = (avgGain * (period - 1) + gain) / period;\n      avgLoss = (avgLoss * (period - 1) + loss) / period;\n    }\n    \n    if (avgLoss === 0) return 100;\n    const rs = avgGain / avgLoss;\n    return 100 - (100 / (1 + rs));\n  }\n\n  /**\n   * Calcula SMA (Simple Moving Average)\n   */\n  static calculateSMA(prices: number[], period: number): number {\n    if (prices.length < period) return prices[prices.length - 1] || 0;\n    \n    const slice = prices.slice(-period);\n    return slice.reduce((sum, price) => sum + price, 0) / period;\n  }\n\n  /**\n   * Calcula EMA (Exponential Moving Average)\n   */\n  static calculateEMA(prices: number[], period: number): number {\n    if (prices.length === 0) return 0;\n    if (prices.length === 1) return prices[0];\n    \n    const multiplier = 2 / (period + 1);\n    let ema = prices[0];\n    \n    for (let i = 1; i < prices.length; i++) {\n      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));\n    }\n    \n    return ema;\n  }\n\n  /**\n   * Calcula Bollinger Bands\n   */\n  static calculateBollingerBands(prices: number[], period: number = 20, stdDev: number = 2) {\n    const sma = this.calculateSMA(prices, period);\n    \n    if (prices.length < period) {\n      return {\n        upper: sma * 1.02,\n        middle: sma,\n        lower: sma * 0.98\n      };\n    }\n    \n    const slice = prices.slice(-period);\n    const variance = slice.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period;\n    const standardDeviation = Math.sqrt(variance);\n    \n    return {\n      upper: sma + (standardDeviation * stdDev),\n      middle: sma,\n      lower: sma - (standardDeviation * stdDev)\n    };\n  }\n\n  /**\n   * Calcula MACD\n   */\n  static calculateMACD(prices: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {\n    const emaFast = this.calculateEMA(prices, fastPeriod);\n    const emaSlow = this.calculateEMA(prices, slowPeriod);\n    const macdLine = emaFast - emaSlow;\n    \n    // Para simplificar, usamos uma aproximação do sinal\n    const signalLine = macdLine * 0.9; // Aproximação\n    const histogram = macdLine - signalLine;\n    \n    return {\n      macd: macdLine,\n      signal: signalLine,\n      histogram: histogram\n    };\n  }\n\n  /**\n   * Identifica níveis de suporte e resistência\n   */\n  static findSupportResistance(prices: number[], currentPrice: number) {\n    const sortedPrices = [...prices].sort((a, b) => a - b);\n    const priceRanges = [];\n    \n    // Agrupa preços em ranges\n    for (let i = 0; i < sortedPrices.length; i += Math.floor(sortedPrices.length / 10)) {\n      const range = sortedPrices.slice(i, i + Math.floor(sortedPrices.length / 10));\n      if (range.length > 0) {\n        priceRanges.push(range.reduce((sum, p) => sum + p, 0) / range.length);\n      }\n    }\n    \n    const support = priceRanges.filter(price => price < currentPrice).slice(-3);\n    const resistance = priceRanges.filter(price => price > currentPrice).slice(0, 3);\n    \n    return {\n      support: support.length > 0 ? support : [currentPrice * 0.95],\n      resistance: resistance.length > 0 ? resistance : [currentPrice * 1.05]\n    };\n  }\n\n  /**\n   * Gera análise técnica completa\n   */\n  static generateTechnicalAnalysis(cryptoData: CryptoData, historicalPrices?: number[]): TechnicalIndicators {\n    // Se não temos dados históricos, simulamos baseado nos dados atuais\n    const prices = historicalPrices || this.generateMockPrices(cryptoData);\n    \n    const rsi = this.calculateRSI(prices);\n    const macd = this.calculateMACD(prices);\n    const sma = {\n      sma20: this.calculateSMA(prices, 20),\n      sma50: this.calculateSMA(prices, 50),\n      sma200: this.calculateSMA(prices, 200)\n    };\n    const ema = {\n      ema12: this.calculateEMA(prices, 12),\n      ema26: this.calculateEMA(prices, 26)\n    };\n    const bollinger = this.calculateBollingerBands(prices);\n    const supportResistance = this.findSupportResistance(prices, cryptoData.current_price);\n    \n    return {\n      rsi,\n      macd,\n      sma,\n      ema,\n      bollinger,\n      volume_profile: cryptoData.total_volume / cryptoData.market_cap,\n      support_resistance: supportResistance\n    };\n  }\n\n  /**\n   * Gera sinal de trading baseado na análise técnica\n   */\n  static generateTradingSignal(cryptoData: CryptoData, technicalIndicators: TechnicalIndicators): TradingSignal {\n    let bullishSignals = 0;\n    let bearishSignals = 0;\n    let totalSignals = 0;\n    \n    const currentPrice = cryptoData.current_price;\n    const { rsi, macd, sma, ema, bollinger } = technicalIndicators;\n    \n    // Análise RSI\n    totalSignals++;\n    if (rsi < 30) bullishSignals++; // Oversold\n    else if (rsi > 70) bearishSignals++; // Overbought\n    \n    // Análise MACD\n    totalSignals++;\n    if (macd.macd > macd.signal && macd.histogram > 0) bullishSignals++;\n    else if (macd.macd < macd.signal && macd.histogram < 0) bearishSignals++;\n    \n    // Análise SMA\n    totalSignals++;\n    if (currentPrice > sma.sma20 && sma.sma20 > sma.sma50) bullishSignals++;\n    else if (currentPrice < sma.sma20 && sma.sma20 < sma.sma50) bearishSignals++;\n    \n    // Análise EMA\n    totalSignals++;\n    if (ema.ema12 > ema.ema26) bullishSignals++;\n    else bearishSignals++;\n    \n    // Análise Bollinger\n    totalSignals++;\n    if (currentPrice < bollinger.lower) bullishSignals++; // Oversold\n    else if (currentPrice > bollinger.upper) bearishSignals++; // Overbought\n    \n    // Análise de tendência de preço\n    totalSignals++;\n    if (cryptoData.price_change_percentage_24h > 5) bullishSignals++;\n    else if (cryptoData.price_change_percentage_24h < -5) bearishSignals++;\n    \n    const bullishPercentage = (bullishSignals / totalSignals) * 100;\n    const bearishPercentage = (bearishSignals / totalSignals) * 100;\n    \n    let type: 'BUY' | 'SELL' | 'HOLD';\n    let strength: 'WEAK' | 'MODERATE' | 'STRONG';\n    let confidence: number;\n    \n    if (bullishPercentage >= 60) {\n      type = 'BUY';\n      strength = bullishPercentage >= 80 ? 'STRONG' : 'MODERATE';\n      confidence = bullishPercentage;\n    } else if (bearishPercentage >= 60) {\n      type = 'SELL';\n      strength = bearishPercentage >= 80 ? 'STRONG' : 'MODERATE';\n      confidence = bearishPercentage;\n    } else {\n      type = 'HOLD';\n      strength = 'WEAK';\n      confidence = Math.max(bullishPercentage, bearishPercentage);\n    }\n    \n    // Calcula targets\n    const volatility = Math.abs(cryptoData.price_change_percentage_24h) / 100;\n    const priceTarget = type === 'BUY' \n      ? currentPrice * (1 + volatility * 2)\n      : currentPrice * (1 - volatility * 2);\n    \n    const stopLoss = type === 'BUY'\n      ? currentPrice * 0.95\n      : currentPrice * 1.05;\n    \n    return {\n      type,\n      strength,\n      confidence: Math.round(confidence),\n      price_target: Math.round(priceTarget * 100) / 100,\n      stop_loss: Math.round(stopLoss * 100) / 100,\n      timeframe: '24h',\n      reasoning: this.generateReasoning(type, strength, technicalIndicators, cryptoData)\n    };\n  }\n\n  /**\n   * Gera explicação do sinal\n   */\n  private static generateReasoning(\n    type: 'BUY' | 'SELL' | 'HOLD',\n    strength: 'WEAK' | 'MODERATE' | 'STRONG',\n    indicators: TechnicalIndicators,\n    cryptoData: CryptoData\n  ): string {\n    const reasons = [];\n    \n    if (indicators.rsi < 30) reasons.push('RSI indica sobrevenda');\n    if (indicators.rsi > 70) reasons.push('RSI indica sobrecompra');\n    if (indicators.macd.macd > indicators.macd.signal) reasons.push('MACD bullish');\n    if (indicators.macd.macd < indicators.macd.signal) reasons.push('MACD bearish');\n    if (cryptoData.current_price > indicators.sma.sma20) reasons.push('Preço acima da SMA20');\n    if (cryptoData.current_price < indicators.sma.sma20) reasons.push('Preço abaixo da SMA20');\n    \n    return reasons.join(', ') || 'Análise baseada em múltiplos indicadores';\n  }\n\n  /**\n   * Gera preços históricos simulados baseados nos dados atuais\n   */\n  private static generateMockPrices(cryptoData: CryptoData): number[] {\n    const prices = [];\n    const currentPrice = cryptoData.current_price;\n    const volatility = Math.abs(cryptoData.price_change_percentage_24h) / 100;\n    \n    // Gera 200 pontos de preço simulados\n    for (let i = 0; i < 200; i++) {\n      const randomChange = (Math.random() - 0.5) * volatility * 2;\n      const price = currentPrice * (1 + randomChange);\n      prices.push(Math.max(price, currentPrice * 0.5)); // Evita preços muito baixos\n    }\n    \n    // Adiciona tendência baseada na mudança de 24h\n    const trend = cryptoData.price_change_percentage_24h / 100;\n    for (let i = 0; i < prices.length; i++) {\n      const trendFactor = (i / prices.length) * trend;\n      prices[i] *= (1 + trendFactor);\n    }\n    \n    return prices;\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IAEX;;GAEC,GACD,OAAO,aAAa,MAAgB,EAAE,SAAiB,EAAE,EAAU;QACjE,IAAI,OAAO,MAAM,GAAG,SAAS,GAAG,OAAO,IAAI,2CAA2C;QAEtF,IAAI,QAAQ;QACZ,IAAI,SAAS;QAEb,mCAAmC;QACnC,IAAK,IAAI,IAAI,GAAG,KAAK,QAAQ,IAAK;YAChC,MAAM,SAAS,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE;YACxC,IAAI,SAAS,GAAG;gBACd,SAAS;YACX,OAAO;gBACL,UAAU,KAAK,GAAG,CAAC;YACrB;QACF;QAEA,IAAI,UAAU,QAAQ;QACtB,IAAI,UAAU,SAAS;QAEvB,qCAAqC;QACrC,IAAK,IAAI,IAAI,SAAS,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YAC/C,MAAM,SAAS,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE;YACxC,MAAM,OAAO,SAAS,IAAI,SAAS;YACnC,MAAM,OAAO,SAAS,IAAI,KAAK,GAAG,CAAC,UAAU;YAE7C,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI;YAC5C,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI;QAC9C;QAEA,IAAI,YAAY,GAAG,OAAO;QAC1B,MAAM,KAAK,UAAU;QACrB,OAAO,MAAO,MAAM,CAAC,IAAI,EAAE;IAC7B;IAEA;;GAEC,GACD,OAAO,aAAa,MAAgB,EAAE,MAAc,EAAU;QAC5D,IAAI,OAAO,MAAM,GAAG,QAAQ,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,IAAI;QAEhE,MAAM,QAAQ,OAAO,KAAK,CAAC,CAAC;QAC5B,OAAO,MAAM,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,OAAO,KAAK;IACxD;IAEA;;GAEC,GACD,OAAO,aAAa,MAAgB,EAAE,MAAc,EAAU;QAC5D,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;QAChC,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO,MAAM,CAAC,EAAE;QAEzC,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC;QAClC,IAAI,MAAM,MAAM,CAAC,EAAE;QAEnB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,MAAM,AAAC,MAAM,CAAC,EAAE,GAAG,aAAe,MAAM,CAAC,IAAI,UAAU;QACzD;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,wBAAwB,MAAgB,EAAE,SAAiB,EAAE,EAAE,SAAiB,CAAC,EAAE;QACxF,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ;QAEtC,IAAI,OAAO,MAAM,GAAG,QAAQ;YAC1B,OAAO;gBACL,OAAO,MAAM;gBACb,QAAQ;gBACR,OAAO,MAAM;YACf;QACF;QAEA,MAAM,QAAQ,OAAO,KAAK,CAAC,CAAC;QAC5B,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,KAAK,GAAG,CAAC,QAAQ,KAAK,IAAI,KAAK;QACnF,MAAM,oBAAoB,KAAK,IAAI,CAAC;QAEpC,OAAO;YACL,OAAO,MAAO,oBAAoB;YAClC,QAAQ;YACR,OAAO,MAAO,oBAAoB;QACpC;IACF;IAEA;;GAEC,GACD,OAAO,cAAc,MAAgB,EAAE,aAAqB,EAAE,EAAE,aAAqB,EAAE,EAAE,eAAuB,CAAC,EAAE;QACjH,MAAM,UAAU,IAAI,CAAC,YAAY,CAAC,QAAQ;QAC1C,MAAM,UAAU,IAAI,CAAC,YAAY,CAAC,QAAQ;QAC1C,MAAM,WAAW,UAAU;QAE3B,oDAAoD;QACpD,MAAM,aAAa,WAAW,KAAK,cAAc;QACjD,MAAM,YAAY,WAAW;QAE7B,OAAO;YACL,MAAM;YACN,QAAQ;YACR,WAAW;QACb;IACF;IAEA;;GAEC,GACD,OAAO,sBAAsB,MAAgB,EAAE,YAAoB,EAAE;QACnE,MAAM,eAAe;eAAI;SAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QACpD,MAAM,cAAc,EAAE;QAEtB,0BAA0B;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,KAAK,KAAK,KAAK,CAAC,aAAa,MAAM,GAAG,IAAK;YAClF,MAAM,QAAQ,aAAa,KAAK,CAAC,GAAG,IAAI,KAAK,KAAK,CAAC,aAAa,MAAM,GAAG;YACzE,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,YAAY,IAAI,CAAC,MAAM,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,GAAG,KAAK,MAAM,MAAM;YACtE;QACF;QAEA,MAAM,UAAU,YAAY,MAAM,CAAC,CAAA,QAAS,QAAQ,cAAc,KAAK,CAAC,CAAC;QACzE,MAAM,aAAa,YAAY,MAAM,CAAC,CAAA,QAAS,QAAQ,cAAc,KAAK,CAAC,GAAG;QAE9E,OAAO;YACL,SAAS,QAAQ,MAAM,GAAG,IAAI,UAAU;gBAAC,eAAe;aAAK;YAC7D,YAAY,WAAW,MAAM,GAAG,IAAI,aAAa;gBAAC,eAAe;aAAK;QACxE;IACF;IAEA;;GAEC,GACD,OAAO,0BAA0B,UAAsB,EAAE,gBAA2B,EAAuB;QACzG,oEAAoE;QACpE,MAAM,SAAS,oBAAoB,IAAI,CAAC,kBAAkB,CAAC;QAE3D,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC;QAC9B,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC;QAChC,MAAM,MAAM;YACV,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;YACjC,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;YACjC,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ;QACpC;QACA,MAAM,MAAM;YACV,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;YACjC,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;QACnC;QACA,MAAM,YAAY,IAAI,CAAC,uBAAuB,CAAC;QAC/C,MAAM,oBAAoB,IAAI,CAAC,qBAAqB,CAAC,QAAQ,WAAW,aAAa;QAErF,OAAO;YACL;YACA;YACA;YACA;YACA;YACA,gBAAgB,WAAW,YAAY,GAAG,WAAW,UAAU;YAC/D,oBAAoB;QACtB;IACF;IAEA;;GAEC,GACD,OAAO,sBAAsB,UAAsB,EAAE,mBAAwC,EAAiB;QAC5G,IAAI,iBAAiB;QACrB,IAAI,iBAAiB;QACrB,IAAI,eAAe;QAEnB,MAAM,eAAe,WAAW,aAAa;QAC7C,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG;QAE3C,cAAc;QACd;QACA,IAAI,MAAM,IAAI,kBAAkB,WAAW;aACtC,IAAI,MAAM,IAAI,kBAAkB,aAAa;QAElD,eAAe;QACf;QACA,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,IAAI,KAAK,SAAS,GAAG,GAAG;aAC9C,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,IAAI,KAAK,SAAS,GAAG,GAAG;QAExD,cAAc;QACd;QACA,IAAI,eAAe,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;aAClD,IAAI,eAAe,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;QAE5D,cAAc;QACd;QACA,IAAI,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;aACtB;QAEL,oBAAoB;QACpB;QACA,IAAI,eAAe,UAAU,KAAK,EAAE,kBAAkB,WAAW;aAC5D,IAAI,eAAe,UAAU,KAAK,EAAE,kBAAkB,aAAa;QAExE,gCAAgC;QAChC;QACA,IAAI,WAAW,2BAA2B,GAAG,GAAG;aAC3C,IAAI,WAAW,2BAA2B,GAAG,CAAC,GAAG;QAEtD,MAAM,oBAAoB,AAAC,iBAAiB,eAAgB;QAC5D,MAAM,oBAAoB,AAAC,iBAAiB,eAAgB;QAE5D,IAAI;QACJ,IAAI;QACJ,IAAI;QAEJ,IAAI,qBAAqB,IAAI;YAC3B,OAAO;YACP,WAAW,qBAAqB,KAAK,WAAW;YAChD,aAAa;QACf,OAAO,IAAI,qBAAqB,IAAI;YAClC,OAAO;YACP,WAAW,qBAAqB,KAAK,WAAW;YAChD,aAAa;QACf,OAAO;YACL,OAAO;YACP,WAAW;YACX,aAAa,KAAK,GAAG,CAAC,mBAAmB;QAC3C;QAEA,kBAAkB;QAClB,MAAM,aAAa,KAAK,GAAG,CAAC,WAAW,2BAA2B,IAAI;QACtE,MAAM,cAAc,SAAS,QACzB,eAAe,CAAC,IAAI,aAAa,CAAC,IAClC,eAAe,CAAC,IAAI,aAAa,CAAC;QAEtC,MAAM,WAAW,SAAS,QACtB,eAAe,OACf,eAAe;QAEnB,OAAO;YACL;YACA;YACA,YAAY,KAAK,KAAK,CAAC;YACvB,cAAc,KAAK,KAAK,CAAC,cAAc,OAAO;YAC9C,WAAW,KAAK,KAAK,CAAC,WAAW,OAAO;YACxC,WAAW;YACX,WAAW,IAAI,CAAC,iBAAiB,CAAC,MAAM,UAAU,qBAAqB;QACzE;IACF;IAEA;;GAEC,GACD,OAAe,kBACb,IAA6B,EAC7B,QAAwC,EACxC,UAA+B,EAC/B,UAAsB,EACd;QACR,MAAM,UAAU,EAAE;QAElB,IAAI,WAAW,GAAG,GAAG,IAAI,QAAQ,IAAI,CAAC;QACtC,IAAI,WAAW,GAAG,GAAG,IAAI,QAAQ,IAAI,CAAC;QACtC,IAAI,WAAW,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC;QAChE,IAAI,WAAW,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC;QAChE,IAAI,WAAW,aAAa,GAAG,WAAW,GAAG,CAAC,KAAK,EAAE,QAAQ,IAAI,CAAC;QAClE,IAAI,WAAW,aAAa,GAAG,WAAW,GAAG,CAAC,KAAK,EAAE,QAAQ,IAAI,CAAC;QAElE,OAAO,QAAQ,IAAI,CAAC,SAAS;IAC/B;IAEA;;GAEC,GACD,OAAe,mBAAmB,UAAsB,EAAY;QAClE,MAAM,SAAS,EAAE;QACjB,MAAM,eAAe,WAAW,aAAa;QAC7C,MAAM,aAAa,KAAK,GAAG,CAAC,WAAW,2BAA2B,IAAI;QAEtE,qCAAqC;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,MAAM,eAAe,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;YAC1D,MAAM,QAAQ,eAAe,CAAC,IAAI,YAAY;YAC9C,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,eAAe,OAAO,4BAA4B;QAChF;QAEA,+CAA+C;QAC/C,MAAM,QAAQ,WAAW,2BAA2B,GAAG;QACvD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,MAAM,cAAc,AAAC,IAAI,OAAO,MAAM,GAAI;YAC1C,MAAM,CAAC,EAAE,IAAK,IAAI;QACpB;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/lib/ai-service.ts"], "sourcesContent": ["import { Message, AgentType, ChatContext, CryptoData, TechnicalIndicators, TradingSignal } from '@/types';\nimport { getAgent } from './agents';\nimport { CryptoApiService } from './crypto-api';\nimport { TechnicalAnalysisService } from './technical-analysis';\n\nexport class AIService {\n  private static context: ChatContext = {\n    messages: [],\n    activeAgent: 'technical_analyst',\n    marketSentiment: 'NEUTRAL'\n  };\n\n  /**\n   * Processa uma mensagem do usuário e retorna resposta do agente ativo\n   */\n  static async processMessage(\n    userMessage: string,\n    agentType?: AgentType\n  ): Promise<Message> {\n    // Atualiza agente ativo se especificado\n    if (agentType) {\n      this.context.activeAgent = agentType;\n    }\n\n    // Adiciona mensagem do usuário ao contexto\n    const userMsg: Message = {\n      id: this.generateId(),\n      content: userMessage,\n      role: 'user',\n      timestamp: new Date()\n    };\n    \n    this.context.messages.push(userMsg);\n\n    // Detecta se o usuário está perguntando sobre uma criptomoeda específica\n    const cryptoMention = this.detectCryptoMention(userMessage);\n    let cryptoData: CryptoData | undefined;\n    let technicalAnalysis: TechnicalIndicators | undefined;\n    let tradingSignal: TradingSignal | undefined;\n\n    if (cryptoMention) {\n      this.context.currentCrypto = cryptoMention;\n      \n      // Busca dados reais da criptomoeda\n      const cryptoResponse = await CryptoApiService.getCryptoData(cryptoMention);\n      if (cryptoResponse.success && cryptoResponse.data) {\n        cryptoData = cryptoResponse.data;\n        \n        // Gera análise técnica\n        technicalAnalysis = TechnicalAnalysisService.generateTechnicalAnalysis(cryptoData);\n        \n        // Gera sinal de trading\n        tradingSignal = TechnicalAnalysisService.generateTradingSignal(cryptoData, technicalAnalysis);\n      }\n    }\n\n    // Gera resposta do agente\n    const agentResponse = await this.generateAgentResponse(\n      userMessage,\n      this.context.activeAgent,\n      cryptoData,\n      technicalAnalysis,\n      tradingSignal\n    );\n\n    // Cria mensagem de resposta\n    const assistantMsg: Message = {\n      id: this.generateId(),\n      content: agentResponse,\n      role: 'assistant',\n      agent: this.context.activeAgent,\n      timestamp: new Date(),\n      cryptoData,\n      technicalAnalysis,\n      tradingSignal\n    };\n\n    this.context.messages.push(assistantMsg);\n    this.context.lastAnalysis = new Date();\n\n    return assistantMsg;\n  }\n\n  /**\n   * Gera resposta do agente baseada no contexto\n   */\n  private static async generateAgentResponse(\n    userMessage: string,\n    agentType: AgentType,\n    cryptoData?: CryptoData,\n    technicalAnalysis?: TechnicalIndicators,\n    tradingSignal?: TradingSignal\n  ): Promise<string> {\n    const agent = getAgent(agentType);\n    const isGreeting = this.isGreeting(userMessage);\n    const isGeneralQuestion = this.isGeneralMarketQuestion(userMessage);\n\n    // Se é uma saudação\n    if (isGreeting) {\n      return this.generateGreetingResponse(agent.name, agent.personality);\n    }\n\n    // Se tem dados de crypto específica\n    if (cryptoData && technicalAnalysis && tradingSignal) {\n      return this.generateCryptoAnalysisResponse(\n        agentType,\n        cryptoData,\n        technicalAnalysis,\n        tradingSignal,\n        userMessage\n      );\n    }\n\n    // Se é pergunta geral sobre mercado\n    if (isGeneralQuestion) {\n      return this.generateGeneralMarketResponse(agentType, userMessage);\n    }\n\n    // Resposta contextual baseada no agente\n    return this.generateContextualResponse(agentType, userMessage);\n  }\n\n  /**\n   * Detecta menção a criptomoedas na mensagem\n   */\n  private static detectCryptoMention(message: string): string | null {\n    const cryptoPatterns = [\n      // Nomes completos\n      /bitcoin/i, /ethereum/i, /cardano/i, /solana/i, /polkadot/i,\n      /chainlink/i, /litecoin/i, /dogecoin/i, /shiba/i, /avalanche/i,\n      \n      // Símbolos\n      /\\bbtc\\b/i, /\\beth\\b/i, /\\bada\\b/i, /\\bsol\\b/i, /\\bdot\\b/i,\n      /\\blink\\b/i, /\\bltc\\b/i, /\\bdoge\\b/i, /\\bshib\\b/i, /\\bavax\\b/i,\n      /\\bmatic\\b/i, /\\bbnb\\b/i, /\\bxrp\\b/i, /\\batom\\b/i, /\\balgo\\b/i\n    ];\n\n    const cryptoMap: Record<string, string> = {\n      'bitcoin': 'bitcoin',\n      'btc': 'bitcoin',\n      'ethereum': 'ethereum',\n      'eth': 'ethereum',\n      'cardano': 'cardano',\n      'ada': 'cardano',\n      'solana': 'solana',\n      'sol': 'solana',\n      'polkadot': 'polkadot',\n      'dot': 'polkadot',\n      'chainlink': 'chainlink',\n      'link': 'chainlink',\n      'litecoin': 'litecoin',\n      'ltc': 'litecoin',\n      'dogecoin': 'dogecoin',\n      'doge': 'dogecoin',\n      'shiba': 'shiba-inu',\n      'shib': 'shiba-inu',\n      'avalanche': 'avalanche-2',\n      'avax': 'avalanche-2',\n      'matic': 'matic-network',\n      'polygon': 'matic-network',\n      'bnb': 'binancecoin',\n      'binance': 'binancecoin',\n      'xrp': 'ripple',\n      'ripple': 'ripple',\n      'atom': 'cosmos',\n      'cosmos': 'cosmos',\n      'algo': 'algorand',\n      'algorand': 'algorand'\n    };\n\n    for (const pattern of cryptoPatterns) {\n      const match = message.match(pattern);\n      if (match) {\n        const found = match[0].toLowerCase();\n        return cryptoMap[found] || found;\n      }\n    }\n\n    return null;\n  }\n\n  /**\n   * Verifica se é uma saudação\n   */\n  private static isGreeting(message: string): boolean {\n    const greetingPatterns = [\n      /^(oi|olá|ola|hey|hi|hello|bom dia|boa tarde|boa noite)/i,\n      /^(e aí|eai|como vai|tudo bem|beleza)/i\n    ];\n    \n    return greetingPatterns.some(pattern => pattern.test(message.trim()));\n  }\n\n  /**\n   * Verifica se é pergunta geral sobre mercado\n   */\n  private static isGeneralMarketQuestion(message: string): boolean {\n    const marketPatterns = [\n      /mercado/i, /cripto/i, /crypto/i, /trading/i, /investir/i,\n      /comprar/i, /vender/i, /análise/i, /tendência/i, /previsão/i\n    ];\n    \n    return marketPatterns.some(pattern => pattern.test(message));\n  }\n\n  /**\n   * Gera resposta de saudação\n   */\n  private static generateGreetingResponse(agentName: string, personality: string): string {\n    const greetings = {\n      'Dr. Marcus Chen': [\n        \"Olá! Sou o Dr. Marcus Chen 👨‍💼. Como especialista em análise técnica, estou aqui para ajudá-lo com análises precisas do mercado cripto. Sobre qual criptomoeda gostaria de conversar?\",\n        \"Bom dia! Dr. Marcus Chen aqui 📊. Pronto para mergulhar nos dados e indicadores técnicos? Qual análise posso fazer para você hoje?\"\n      ],\n      'Sarah \"The Wolf\" Rodriguez': [\n        \"E aí, parceiro! 🐺 Sarah Rodriguez na área. 15 anos batalhando nos mercados e pronta para compartilhar a real sobre trading. Em que posso ajudar?\",\n        \"Opa! The Wolf aqui 💎. Mercado tá pegando fogo hoje, né? Conta aí, qual crypto tá na sua mira?\"\n      ],\n      'Prof. Elena Nakamura': [\n        \"Olá! Sou a Prof. Elena Nakamura 👩‍🏫. Vamos explorar juntos o fascinante mundo das criptomoedas? Posso ajudá-lo com análises fundamentais e contexto de mercado.\",\n        \"Bom dia! Prof. Elena aqui 📚. Sempre é um prazer discutir as tendências e o futuro do mercado cripto. O que gostaria de aprender hoje?\"\n      ]\n    };\n\n    const agentGreetings = greetings[agentName as keyof typeof greetings] || [\n      \"Olá! Como posso ajudá-lo com análises de criptomoedas hoje?\"\n    ];\n\n    return agentGreetings[Math.floor(Math.random() * agentGreetings.length)];\n  }\n\n  /**\n   * Gera resposta com análise de criptomoeda\n   */\n  private static generateCryptoAnalysisResponse(\n    agentType: AgentType,\n    cryptoData: CryptoData,\n    technicalAnalysis: TechnicalIndicators,\n    tradingSignal: TradingSignal,\n    userMessage: string\n  ): string {\n    const agent = getAgent(agentType);\n    const price = cryptoData.current_price.toLocaleString('pt-BR', { \n      style: 'currency', \n      currency: 'USD' \n    });\n    const change24h = cryptoData.price_change_percentage_24h.toFixed(2);\n    const changeEmoji = cryptoData.price_change_percentage_24h >= 0 ? '📈' : '📉';\n\n    switch (agentType) {\n      case 'technical_analyst':\n        return `📊 **Análise Técnica - ${cryptoData.name} (${cryptoData.symbol.toUpperCase()})**\n\n**Preço Atual:** ${price} ${changeEmoji} ${change24h}%\n\n**Indicadores Técnicos:**\n• **RSI (14):** ${technicalAnalysis.rsi.toFixed(1)} ${technicalAnalysis.rsi < 30 ? '(Sobrevenda 🟢)' : technicalAnalysis.rsi > 70 ? '(Sobrecompra 🔴)' : '(Neutro 🟡)'}\n• **MACD:** ${technicalAnalysis.macd.macd > technicalAnalysis.macd.signal ? 'Bullish 📈' : 'Bearish 📉'}\n• **SMA20:** ${technicalAnalysis.sma.sma20.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}\n• **Bollinger:** Preço ${cryptoData.current_price < technicalAnalysis.bollinger.lower ? 'abaixo da banda inferior 🟢' : cryptoData.current_price > technicalAnalysis.bollinger.upper ? 'acima da banda superior 🔴' : 'dentro das bandas 🟡'}\n\n**Sinal de Trading:** ${tradingSignal.type} ${tradingSignal.strength} ⚡\n**Confiança:** ${tradingSignal.confidence}%\n**Target:** ${tradingSignal.price_target.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}\n**Stop-Loss:** ${tradingSignal.stop_loss.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}\n\n**Confluência de Sinais:** ${tradingSignal.reasoning}\n\n*Lembre-se: Esta análise é baseada em dados atuais e deve ser combinada com sua própria pesquisa.* 🎯`;\n\n      case 'experienced_trader':\n        return `🐺 **The Wolf's Take - ${cryptoData.name}**\n\nOpa, ${price} ${changeEmoji} ${change24h}% nas últimas 24h!\n\n**Minha leitura do mercado:**\n${tradingSignal.type === 'BUY' ? '🚀 Tô vendo oportunidade de compra aqui!' : tradingSignal.type === 'SELL' ? '⚠️ Hora de tomar lucro ou sair fora!' : '🤔 Melhor ficar de fora por enquanto.'}\n\n**Setup que eu faria:**\n• **Entrada:** ${tradingSignal.type === 'BUY' ? 'Compra' : tradingSignal.type === 'SELL' ? 'Venda' : 'Aguardar'} com ${tradingSignal.confidence}% de confiança\n• **Target:** ${tradingSignal.price_target.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}\n• **Stop:** ${tradingSignal.stop_loss.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })} (SEMPRE use stop-loss! 💎)\n\n**Volume:** ${(cryptoData.total_volume / 1000000).toFixed(0)}M - ${cryptoData.total_volume > cryptoData.market_cap * 0.1 ? 'Volume forte 💪' : 'Volume fraco 😴'}\n\n**Dica da Wolf:** ${technicalAnalysis.rsi < 30 ? 'RSI em oversold, pode ser uma boa entrada se você tem estômago!' : technicalAnalysis.rsi > 70 ? 'Cuidado com o RSI em overbought, pode corrigir!' : 'RSI neutro, aguarde confirmação!'}\n\n*Risk management é tudo, parceiro! Nunca aposte mais do que pode perder.* 🔥`;\n\n      case 'market_consultant':\n        return `👩‍🏫 **Análise Fundamentalista - ${cryptoData.name}**\n\n**Contexto de Mercado:**\n${cryptoData.name} está cotado a ${price}, com variação de ${change24h}% em 24h.\n\n**Posição no Mercado:**\n• **Ranking:** #${cryptoData.market_cap_rank} por market cap\n• **Market Cap:** $${(cryptoData.market_cap / 1000000000).toFixed(2)}B\n• **Volume 24h:** $${(cryptoData.total_volume / 1000000).toFixed(0)}M\n• **Supply:** ${(cryptoData.circulating_supply / 1000000).toFixed(0)}M ${cryptoData.symbol.toUpperCase()}\n\n**Análise Fundamentalista:**\n${this.getFundamentalAnalysis(cryptoData)}\n\n**Perspectiva de Longo Prazo:**\n${tradingSignal.type === 'BUY' ? '🌱 Os fundamentos sugerem potencial de crescimento' : tradingSignal.type === 'SELL' ? '⚠️ Alguns indicadores fundamentais preocupam' : '📊 Fundamentos mistos, aguardar mais clareza'}\n\n**Fatores a Observar:**\n• Adoção institucional e parcerias\n• Desenvolvimentos tecnológicos\n• Regulamentações governamentais\n• Sentimento geral do mercado cripto\n\n*A análise fundamentalista complementa a técnica para decisões mais informadas.* 💡`;\n\n      default:\n        return \"Análise não disponível para este agente.\";\n    }\n  }\n\n  /**\n   * Gera análise fundamentalista básica\n   */\n  private static getFundamentalAnalysis(cryptoData: CryptoData): string {\n    const analyses = [];\n    \n    if (cryptoData.market_cap_rank <= 10) {\n      analyses.push(\"Projeto consolidado entre os top 10\");\n    }\n    \n    if (cryptoData.price_change_percentage_24h > 10) {\n      analyses.push(\"Alta volatilidade recente pode indicar interesse especulativo\");\n    }\n    \n    if (cryptoData.total_volume > cryptoData.market_cap * 0.1) {\n      analyses.push(\"Alto volume de negociação indica liquidez saudável\");\n    }\n    \n    return analyses.join('. ') || \"Projeto em desenvolvimento com métricas estáveis\";\n  }\n\n  /**\n   * Gera resposta para perguntas gerais sobre mercado\n   */\n  private static generateGeneralMarketResponse(agentType: AgentType, userMessage: string): string {\n    // Implementação simplificada - em produção, integraria com API de IA\n    const responses = {\n      'technical_analyst': \"Como analista técnico, recomendo sempre basear decisões em dados concretos. Sobre qual criptomoeda específica gostaria de uma análise detalhada? 📊\",\n      'experienced_trader': \"No trading, experiência vale ouro! Qual crypto tá na sua mira? Posso dar umas dicas baseadas no que já vi por aí 🐺\",\n      'market_consultant': \"O mercado cripto está em constante evolução. Para uma análise mais precisa, me diga sobre qual projeto específico gostaria de conversar 👩‍🏫\"\n    };\n    \n    return responses[agentType];\n  }\n\n  /**\n   * Gera resposta contextual\n   */\n  private static generateContextualResponse(agentType: AgentType, userMessage: string): string {\n    const agent = getAgent(agentType);\n    return `Como ${agent.name}, posso ajudá-lo com ${agent.specialties.join(', ')}. Sobre qual criptomoeda específica gostaria de conversar? ${agent.avatar}`;\n  }\n\n  /**\n   * Gera ID único\n   */\n  private static generateId(): string {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n\n  /**\n   * Obtém contexto atual\n   */\n  static getContext(): ChatContext {\n    return { ...this.context };\n  }\n\n  /**\n   * Limpa contexto\n   */\n  static clearContext(): void {\n    this.context = {\n      messages: [],\n      activeAgent: 'technical_analyst',\n      marketSentiment: 'NEUTRAL'\n    };\n  }\n\n  /**\n   * Troca agente ativo\n   */\n  static switchAgent(agentType: AgentType): void {\n    this.context.activeAgent = agentType;\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM;IACX,OAAe,UAAuB;QACpC,UAAU,EAAE;QACZ,aAAa;QACb,iBAAiB;IACnB,EAAE;IAEF;;GAEC,GACD,aAAa,eACX,WAAmB,EACnB,SAAqB,EACH;QAClB,wCAAwC;QACxC,IAAI,WAAW;YACb,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG;QAC7B;QAEA,2CAA2C;QAC3C,MAAM,UAAmB;YACvB,IAAI,IAAI,CAAC,UAAU;YACnB,SAAS;YACT,MAAM;YACN,WAAW,IAAI;QACjB;QAEA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;QAE3B,yEAAyE;QACzE,MAAM,gBAAgB,IAAI,CAAC,mBAAmB,CAAC;QAC/C,IAAI;QACJ,IAAI;QACJ,IAAI;QAEJ,IAAI,eAAe;YACjB,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG;YAE7B,mCAAmC;YACnC,MAAM,iBAAiB,MAAM,2HAAA,CAAA,mBAAgB,CAAC,aAAa,CAAC;YAC5D,IAAI,eAAe,OAAO,IAAI,eAAe,IAAI,EAAE;gBACjD,aAAa,eAAe,IAAI;gBAEhC,uBAAuB;gBACvB,oBAAoB,mIAAA,CAAA,2BAAwB,CAAC,yBAAyB,CAAC;gBAEvE,wBAAwB;gBACxB,gBAAgB,mIAAA,CAAA,2BAAwB,CAAC,qBAAqB,CAAC,YAAY;YAC7E;QACF;QAEA,0BAA0B;QAC1B,MAAM,gBAAgB,MAAM,IAAI,CAAC,qBAAqB,CACpD,aACA,IAAI,CAAC,OAAO,CAAC,WAAW,EACxB,YACA,mBACA;QAGF,4BAA4B;QAC5B,MAAM,eAAwB;YAC5B,IAAI,IAAI,CAAC,UAAU;YACnB,SAAS;YACT,MAAM;YACN,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW;YAC/B,WAAW,IAAI;YACf;YACA;YACA;QACF;QAEA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC3B,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI;QAEhC,OAAO;IACT;IAEA;;GAEC,GACD,aAAqB,sBACnB,WAAmB,EACnB,SAAoB,EACpB,UAAuB,EACvB,iBAAuC,EACvC,aAA6B,EACZ;QACjB,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE;QACvB,MAAM,aAAa,IAAI,CAAC,UAAU,CAAC;QACnC,MAAM,oBAAoB,IAAI,CAAC,uBAAuB,CAAC;QAEvD,oBAAoB;QACpB,IAAI,YAAY;YACd,OAAO,IAAI,CAAC,wBAAwB,CAAC,MAAM,IAAI,EAAE,MAAM,WAAW;QACpE;QAEA,oCAAoC;QACpC,IAAI,cAAc,qBAAqB,eAAe;YACpD,OAAO,IAAI,CAAC,8BAA8B,CACxC,WACA,YACA,mBACA,eACA;QAEJ;QAEA,oCAAoC;QACpC,IAAI,mBAAmB;YACrB,OAAO,IAAI,CAAC,6BAA6B,CAAC,WAAW;QACvD;QAEA,wCAAwC;QACxC,OAAO,IAAI,CAAC,0BAA0B,CAAC,WAAW;IACpD;IAEA;;GAEC,GACD,OAAe,oBAAoB,OAAe,EAAiB;QACjE,MAAM,iBAAiB;YACrB,kBAAkB;YAClB;YAAY;YAAa;YAAY;YAAW;YAChD;YAAc;YAAa;YAAa;YAAU;YAElD,WAAW;YACX;YAAY;YAAY;YAAY;YAAY;YAChD;YAAa;YAAY;YAAa;YAAa;YACnD;YAAc;YAAY;YAAY;YAAa;SACpD;QAED,MAAM,YAAoC;YACxC,WAAW;YACX,OAAO;YACP,YAAY;YACZ,OAAO;YACP,WAAW;YACX,OAAO;YACP,UAAU;YACV,OAAO;YACP,YAAY;YACZ,OAAO;YACP,aAAa;YACb,QAAQ;YACR,YAAY;YACZ,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,aAAa;YACb,QAAQ;YACR,SAAS;YACT,WAAW;YACX,OAAO;YACP,WAAW;YACX,OAAO;YACP,UAAU;YACV,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,YAAY;QACd;QAEA,KAAK,MAAM,WAAW,eAAgB;YACpC,MAAM,QAAQ,QAAQ,KAAK,CAAC;YAC5B,IAAI,OAAO;gBACT,MAAM,QAAQ,KAAK,CAAC,EAAE,CAAC,WAAW;gBAClC,OAAO,SAAS,CAAC,MAAM,IAAI;YAC7B;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAe,WAAW,OAAe,EAAW;QAClD,MAAM,mBAAmB;YACvB;YACA;SACD;QAED,OAAO,iBAAiB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,QAAQ,IAAI;IACnE;IAEA;;GAEC,GACD,OAAe,wBAAwB,OAAe,EAAW;QAC/D,MAAM,iBAAiB;YACrB;YAAY;YAAW;YAAW;YAAY;YAC9C;YAAY;YAAW;YAAY;YAAc;SAClD;QAED,OAAO,eAAe,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;IACrD;IAEA;;GAEC,GACD,OAAe,yBAAyB,SAAiB,EAAE,WAAmB,EAAU;QACtF,MAAM,YAAY;YAChB,mBAAmB;gBACjB;gBACA;aACD;YACD,8BAA8B;gBAC5B;gBACA;aACD;YACD,wBAAwB;gBACtB;gBACA;aACD;QACH;QAEA,MAAM,iBAAiB,SAAS,CAAC,UAAoC,IAAI;YACvE;SACD;QAED,OAAO,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;IAC1E;IAEA;;GAEC,GACD,OAAe,+BACb,SAAoB,EACpB,UAAsB,EACtB,iBAAsC,EACtC,aAA4B,EAC5B,WAAmB,EACX;QACR,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE;QACvB,MAAM,QAAQ,WAAW,aAAa,CAAC,cAAc,CAAC,SAAS;YAC7D,OAAO;YACP,UAAU;QACZ;QACA,MAAM,YAAY,WAAW,2BAA2B,CAAC,OAAO,CAAC;QACjE,MAAM,cAAc,WAAW,2BAA2B,IAAI,IAAI,OAAO;QAEzE,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,uBAAuB,EAAE,WAAW,IAAI,CAAC,EAAE,EAAE,WAAW,MAAM,CAAC,WAAW,GAAG;;iBAE5E,EAAE,MAAM,CAAC,EAAE,YAAY,CAAC,EAAE,UAAU;;;gBAGrC,EAAE,kBAAkB,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,kBAAkB,GAAG,GAAG,KAAK,oBAAoB,kBAAkB,GAAG,GAAG,KAAK,qBAAqB,cAAc;YAC3J,EAAE,kBAAkB,IAAI,CAAC,IAAI,GAAG,kBAAkB,IAAI,CAAC,MAAM,GAAG,eAAe,aAAa;aAC3F,EAAE,kBAAkB,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS;oBAAE,OAAO;oBAAY,UAAU;gBAAM,GAAG;uBACpF,EAAE,WAAW,aAAa,GAAG,kBAAkB,SAAS,CAAC,KAAK,GAAG,gCAAgC,WAAW,aAAa,GAAG,kBAAkB,SAAS,CAAC,KAAK,GAAG,+BAA+B,uBAAuB;;sBAEvN,EAAE,cAAc,IAAI,CAAC,CAAC,EAAE,cAAc,QAAQ,CAAC;eACtD,EAAE,cAAc,UAAU,CAAC;YAC9B,EAAE,cAAc,YAAY,CAAC,cAAc,CAAC,SAAS;oBAAE,OAAO;oBAAY,UAAU;gBAAM,GAAG;eAC1F,EAAE,cAAc,SAAS,CAAC,cAAc,CAAC,SAAS;oBAAE,OAAO;oBAAY,UAAU;gBAAM,GAAG;;2BAE9E,EAAE,cAAc,SAAS,CAAC;;qGAEgD,CAAC;YAEhG,KAAK;gBACH,OAAO,CAAC,uBAAuB,EAAE,WAAW,IAAI,CAAC;;KAEpD,EAAE,MAAM,CAAC,EAAE,YAAY,CAAC,EAAE,UAAU;;;AAGzC,EAAE,cAAc,IAAI,KAAK,QAAQ,6CAA6C,cAAc,IAAI,KAAK,SAAS,yCAAyC,wCAAwC;;;eAGhL,EAAE,cAAc,IAAI,KAAK,QAAQ,WAAW,cAAc,IAAI,KAAK,SAAS,UAAU,WAAW,KAAK,EAAE,cAAc,UAAU,CAAC;cAClI,EAAE,cAAc,YAAY,CAAC,cAAc,CAAC,SAAS;oBAAE,OAAO;oBAAY,UAAU;gBAAM,GAAG;YAC/F,EAAE,cAAc,SAAS,CAAC,cAAc,CAAC,SAAS;oBAAE,OAAO;oBAAY,UAAU;gBAAM,GAAG;;YAE1F,EAAE,CAAC,WAAW,YAAY,GAAG,OAAO,EAAE,OAAO,CAAC,GAAG,IAAI,EAAE,WAAW,YAAY,GAAG,WAAW,UAAU,GAAG,MAAM,oBAAoB,kBAAkB;;kBAE/I,EAAE,kBAAkB,GAAG,GAAG,KAAK,oEAAoE,kBAAkB,GAAG,GAAG,KAAK,oDAAoD,mCAAmC;;4EAE7J,CAAC;YAEvE,KAAK;gBACH,OAAO,CAAC,kCAAkC,EAAE,WAAW,IAAI,CAAC;;;AAGpE,EAAE,WAAW,IAAI,CAAC,eAAe,EAAE,MAAM,kBAAkB,EAAE,UAAU;;;gBAGvD,EAAE,WAAW,eAAe,CAAC;mBAC1B,EAAE,CAAC,WAAW,UAAU,GAAG,UAAU,EAAE,OAAO,CAAC,GAAG;mBAClD,EAAE,CAAC,WAAW,YAAY,GAAG,OAAO,EAAE,OAAO,CAAC,GAAG;cACtD,EAAE,CAAC,WAAW,kBAAkB,GAAG,OAAO,EAAE,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,MAAM,CAAC,WAAW,GAAG;;;AAGzG,EAAE,IAAI,CAAC,sBAAsB,CAAC,YAAY;;;AAG1C,EAAE,cAAc,IAAI,KAAK,QAAQ,uDAAuD,cAAc,IAAI,KAAK,SAAS,iDAAiD,+CAA+C;;;;;;;;mFAQrI,CAAC;YAE9E;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,OAAe,uBAAuB,UAAsB,EAAU;QACpE,MAAM,WAAW,EAAE;QAEnB,IAAI,WAAW,eAAe,IAAI,IAAI;YACpC,SAAS,IAAI,CAAC;QAChB;QAEA,IAAI,WAAW,2BAA2B,GAAG,IAAI;YAC/C,SAAS,IAAI,CAAC;QAChB;QAEA,IAAI,WAAW,YAAY,GAAG,WAAW,UAAU,GAAG,KAAK;YACzD,SAAS,IAAI,CAAC;QAChB;QAEA,OAAO,SAAS,IAAI,CAAC,SAAS;IAChC;IAEA;;GAEC,GACD,OAAe,8BAA8B,SAAoB,EAAE,WAAmB,EAAU;QAC9F,qEAAqE;QACrE,MAAM,YAAY;YAChB,qBAAqB;YACrB,sBAAsB;YACtB,qBAAqB;QACvB;QAEA,OAAO,SAAS,CAAC,UAAU;IAC7B;IAEA;;GAEC,GACD,OAAe,2BAA2B,SAAoB,EAAE,WAAmB,EAAU;QAC3F,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE;QACvB,OAAO,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,qBAAqB,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,2DAA2D,EAAE,MAAM,MAAM,EAAE;IAC3J;IAEA;;GAEC,GACD,OAAe,aAAqB;QAClC,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;IACrE;IAEA;;GAEC,GACD,OAAO,aAA0B;QAC/B,OAAO;YAAE,GAAG,IAAI,CAAC,OAAO;QAAC;IAC3B;IAEA;;GAEC,GACD,OAAO,eAAqB;QAC1B,IAAI,CAAC,OAAO,GAAG;YACb,UAAU,EAAE;YACZ,aAAa;YACb,iBAAiB;QACnB;IACF;IAEA;;GAEC,GACD,OAAO,YAAY,SAAoB,EAAQ;QAC7C,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 1116, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(value: number): string {\n  return new Intl.NumberFormat('pt-BR', {\n    style: 'currency',\n    currency: 'USD',\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 6\n  }).format(value);\n}\n\nexport function formatPercentage(value: number): string {\n  return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;\n}\n\nexport function formatNumber(value: number): string {\n  if (value >= 1e9) {\n    return `${(value / 1e9).toFixed(2)}B`;\n  }\n  if (value >= 1e6) {\n    return `${(value / 1e6).toFixed(2)}M`;\n  }\n  if (value >= 1e3) {\n    return `${(value / 1e3).toFixed(2)}K`;\n  }\n  return value.toFixed(2);\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('pt-BR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  }).format(date);\n}\n\nexport function getTimeAgo(date: Date): string {\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n  \n  if (diffInSeconds < 60) {\n    return 'agora mesmo';\n  }\n  \n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes}min atrás`;\n  }\n  \n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `${diffInHours}h atrás`;\n  }\n  \n  const diffInDays = Math.floor(diffInHours / 24);\n  return `${diffInDays}d atrás`;\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,KAAa;IAC1C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,GAAG,SAAS,IAAI,MAAM,KAAK,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;AACvD;AAEO,SAAS,aAAa,KAAa;IACxC,IAAI,SAAS,KAAK;QAChB,OAAO,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,IAAI,SAAS,KAAK;QAChB,OAAO,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,IAAI,SAAS,KAAK;QAChB,OAAO,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,OAAO,MAAM,OAAO,CAAC;AACvB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAU;IACnC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,SAAS,CAAC;IACpC;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,OAAO,CAAC;IAChC;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,OAAO,GAAG,WAAW,OAAO,CAAC;AAC/B", "debugId": null}}, {"offset": {"line": 1186, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/components/chat/AgentSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { ChevronDown, BarChart3, TrendingUp, GraduationCap } from 'lucide-react';\nimport { AgentType } from '@/types';\nimport { cn } from '@/lib/utils';\n\ninterface AgentSelectorProps {\n  activeAgent: AgentType;\n  onAgentChange: (agent: AgentType) => void;\n}\n\nexport function AgentSelector({ activeAgent, onAgentChange }: AgentSelectorProps) {\n  const [isOpen, setIsOpen] = useState(false);\n\n  const agents = [\n    {\n      id: 'technical_analyst' as AgentType,\n      name: 'Dr. <PERSON>',\n      title: 'Analista Técnico',\n      description: 'Especialista em indicadores e análise técnica',\n      icon: BarChart3,\n      emoji: '👨‍💼',\n      color: 'from-blue-500 to-cyan-500'\n    },\n    {\n      id: 'experienced_trader' as AgentType,\n      name: '<PERSON> \"The Wolf\" Rodriguez',\n      title: 'Trader Experiente',\n      description: '15 anos de experiência em trading',\n      icon: TrendingUp,\n      emoji: '🐺',\n      color: 'from-green-500 to-emerald-500'\n    },\n    {\n      id: 'market_consultant' as AgentType,\n      name: 'Prof. <PERSON>',\n      title: 'Consultora de Mercado',\n      description: '<PERSON>álise fundamentalista e tendências macro',\n      icon: GraduationCap,\n      emoji: '👩‍🏫',\n      color: 'from-purple-500 to-pink-500'\n    }\n  ];\n\n  const currentAgent = agents.find(agent => agent.id === activeAgent);\n\n  const handleAgentSelect = (agentId: AgentType) => {\n    onAgentChange(agentId);\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\">\n      {/* Trigger Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-3 bg-white/10 hover:bg-white/20 border border-purple-500/30 rounded-lg px-4 py-2 transition-all duration-200\"\n      >\n        <div className={cn(\n          \"w-8 h-8 rounded-full flex items-center justify-center bg-gradient-to-r\",\n          currentAgent?.color\n        )}>\n          {currentAgent && <currentAgent.icon className=\"w-4 h-4 text-white\" />}\n        </div>\n        \n        <div className=\"text-left\">\n          <div className=\"text-sm font-medium text-white\">\n            {currentAgent?.name}\n          </div>\n          <div className=\"text-xs text-purple-300\">\n            {currentAgent?.title}\n          </div>\n        </div>\n        \n        <ChevronDown className={cn(\n          \"w-4 h-4 text-purple-300 transition-transform duration-200\",\n          isOpen && \"rotate-180\"\n        )} />\n      </button>\n\n      {/* Dropdown Menu */}\n      {isOpen && (\n        <>\n          {/* Backdrop */}\n          <div \n            className=\"fixed inset-0 z-10\" \n            onClick={() => setIsOpen(false)}\n          />\n          \n          {/* Menu */}\n          <div className=\"absolute right-0 top-full mt-2 w-80 bg-slate-800/95 backdrop-blur-sm border border-purple-500/30 rounded-lg shadow-xl z-20\">\n            <div className=\"p-2\">\n              {agents.map((agent) => (\n                <button\n                  key={agent.id}\n                  onClick={() => handleAgentSelect(agent.id)}\n                  className={cn(\n                    \"w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 text-left\",\n                    activeAgent === agent.id\n                      ? \"bg-purple-500/20 border border-purple-500/50\"\n                      : \"hover:bg-white/10\"\n                  )}\n                >\n                  <div className={cn(\n                    \"w-10 h-10 rounded-full flex items-center justify-center bg-gradient-to-r flex-shrink-0\",\n                    agent.color\n                  )}>\n                    <agent.icon className=\"w-5 h-5 text-white\" />\n                  </div>\n                  \n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-sm font-medium text-white truncate\">\n                        {agent.name}\n                      </span>\n                      <span className=\"text-lg\">{agent.emoji}</span>\n                    </div>\n                    <div className=\"text-xs text-purple-300 mb-1\">\n                      {agent.title}\n                    </div>\n                    <div className=\"text-xs text-gray-400\">\n                      {agent.description}\n                    </div>\n                  </div>\n                  \n                  {activeAgent === agent.id && (\n                    <div className=\"w-2 h-2 bg-purple-500 rounded-full flex-shrink-0\" />\n                  )}\n                </button>\n              ))}\n            </div>\n            \n            <div className=\"border-t border-purple-500/20 p-3\">\n              <div className=\"text-xs text-gray-400 text-center\">\n                Cada agente tem especialidades únicas para melhor atendê-lo\n              </div>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;AALA;;;;;AAYO,SAAS,cAAc,EAAE,WAAW,EAAE,aAAa,EAAsB;IAC9E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,SAAS;QACb;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM,kNAAA,CAAA,YAAS;YACf,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,OAAO;QACT;KACD;IAED,MAAM,eAAe,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAEvD,MAAM,oBAAoB,CAAC;QACzB,cAAc;QACd,UAAU;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,0EACA,cAAc;kCAEb,8BAAgB,8OAAC,aAAa,IAAI;4BAAC,WAAU;;;;;;;;;;;kCAGhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,cAAc;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;0CACZ,cAAc;;;;;;;;;;;;kCAInB,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACvB,6DACA,UAAU;;;;;;;;;;;;YAKb,wBACC;;kCAEE,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAI3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;wCAEC,SAAS,IAAM,kBAAkB,MAAM,EAAE;wCACzC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,gBAAgB,MAAM,EAAE,GACpB,iDACA;;0DAGN,8OAAC;gDAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,0FACA,MAAM,KAAK;0DAEX,cAAA,8OAAC,MAAM,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAGxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,MAAM,IAAI;;;;;;0EAEb,8OAAC;gEAAK,WAAU;0EAAW,MAAM,KAAK;;;;;;;;;;;;kEAExC,8OAAC;wDAAI,WAAU;kEACZ,MAAM,KAAK;;;;;;kEAEd,8OAAC;wDAAI,WAAU;kEACZ,MAAM,WAAW;;;;;;;;;;;;4CAIrB,gBAAgB,MAAM,EAAE,kBACvB,8OAAC;gDAAI,WAAU;;;;;;;uCAhCZ,MAAM,EAAE;;;;;;;;;;0CAsCnB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAAoC;;;;;;;;;;;;;;;;;;;;;;;;;AASjE", "debugId": null}}, {"offset": {"line": 1433, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/components/chat/CryptoDataCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { TrendingUp, TrendingDown, Activity, Target, Shield, Clock } from 'lucide-react';\nimport { CryptoData, TechnicalIndicators, TradingSignal } from '@/types';\nimport { cn, formatCurrency, formatPercentage, formatNumber } from '@/lib/utils';\n\ninterface CryptoDataCardProps {\n  cryptoData: CryptoData;\n  technicalAnalysis: TechnicalIndicators;\n  tradingSignal: TradingSignal;\n}\n\nexport function CryptoDataCard({ \n  cryptoData, \n  technicalAnalysis, \n  tradingSignal \n}: CryptoDataCardProps) {\n  const isPositive = cryptoData.price_change_percentage_24h >= 0;\n  \n  const getSignalColor = (signal: TradingSignal) => {\n    switch (signal.type) {\n      case 'BUY':\n        return 'from-green-500 to-emerald-500';\n      case 'SELL':\n        return 'from-red-500 to-rose-500';\n      default:\n        return 'from-yellow-500 to-orange-500';\n    }\n  };\n\n  const getSignalIcon = (signal: TradingSignal) => {\n    switch (signal.type) {\n      case 'BUY':\n        return TrendingUp;\n      case 'SELL':\n        return TrendingDown;\n      default:\n        return Activity;\n    }\n  };\n\n  const getStrengthColor = (strength: string) => {\n    switch (strength) {\n      case 'STRONG':\n        return 'text-green-400';\n      case 'MODERATE':\n        return 'text-yellow-400';\n      default:\n        return 'text-gray-400';\n    }\n  };\n\n  const SignalIcon = getSignalIcon(tradingSignal);\n\n  return (\n    <div className=\"bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-lg p-4 space-y-4\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center\">\n            <span className=\"text-xs font-bold text-white\">\n              {cryptoData.symbol.toUpperCase()}\n            </span>\n          </div>\n          <div>\n            <h3 className=\"font-semibold text-white\">{cryptoData.name}</h3>\n            <p className=\"text-xs text-gray-400\">#{cryptoData.market_cap_rank}</p>\n          </div>\n        </div>\n        \n        <div className=\"text-right\">\n          <div className=\"text-lg font-bold text-white\">\n            {formatCurrency(cryptoData.current_price)}\n          </div>\n          <div className={cn(\n            \"text-sm font-medium flex items-center\",\n            isPositive ? \"text-green-400\" : \"text-red-400\"\n          )}>\n            {isPositive ? <TrendingUp className=\"w-3 h-3 mr-1\" /> : <TrendingDown className=\"w-3 h-3 mr-1\" />}\n            {formatPercentage(cryptoData.price_change_percentage_24h)}\n          </div>\n        </div>\n      </div>\n\n      {/* Market Data */}\n      <div className=\"grid grid-cols-2 gap-4\">\n        <div className=\"space-y-2\">\n          <div className=\"flex justify-between\">\n            <span className=\"text-xs text-gray-400\">Market Cap</span>\n            <span className=\"text-xs text-white\">{formatNumber(cryptoData.market_cap)}</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-xs text-gray-400\">Volume 24h</span>\n            <span className=\"text-xs text-white\">{formatNumber(cryptoData.total_volume)}</span>\n          </div>\n        </div>\n        \n        <div className=\"space-y-2\">\n          <div className=\"flex justify-between\">\n            <span className=\"text-xs text-gray-400\">High 24h</span>\n            <span className=\"text-xs text-white\">{formatCurrency(cryptoData.high_24h)}</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-xs text-gray-400\">Low 24h</span>\n            <span className=\"text-xs text-white\">{formatCurrency(cryptoData.low_24h)}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Technical Indicators */}\n      <div className=\"border-t border-slate-700/50 pt-3\">\n        <h4 className=\"text-sm font-medium text-white mb-2\">Indicadores Técnicos</h4>\n        <div className=\"grid grid-cols-3 gap-3\">\n          <div className=\"text-center\">\n            <div className=\"text-xs text-gray-400\">RSI</div>\n            <div className={cn(\n              \"text-sm font-medium\",\n              technicalAnalysis.rsi < 30 ? \"text-green-400\" :\n              technicalAnalysis.rsi > 70 ? \"text-red-400\" : \"text-yellow-400\"\n            )}>\n              {technicalAnalysis.rsi.toFixed(1)}\n            </div>\n          </div>\n          \n          <div className=\"text-center\">\n            <div className=\"text-xs text-gray-400\">MACD</div>\n            <div className={cn(\n              \"text-sm font-medium\",\n              technicalAnalysis.macd.macd > technicalAnalysis.macd.signal ? \"text-green-400\" : \"text-red-400\"\n            )}>\n              {technicalAnalysis.macd.macd > technicalAnalysis.macd.signal ? \"Bullish\" : \"Bearish\"}\n            </div>\n          </div>\n          \n          <div className=\"text-center\">\n            <div className=\"text-xs text-gray-400\">SMA20</div>\n            <div className={cn(\n              \"text-sm font-medium\",\n              cryptoData.current_price > technicalAnalysis.sma.sma20 ? \"text-green-400\" : \"text-red-400\"\n            )}>\n              {formatCurrency(technicalAnalysis.sma.sma20)}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Trading Signal */}\n      <div className=\"border-t border-slate-700/50 pt-3\">\n        <div className=\"flex items-center justify-between mb-2\">\n          <h4 className=\"text-sm font-medium text-white\">Sinal de Trading</h4>\n          <div className=\"flex items-center space-x-1\">\n            <Clock className=\"w-3 h-3 text-gray-400\" />\n            <span className=\"text-xs text-gray-400\">{tradingSignal.timeframe}</span>\n          </div>\n        </div>\n        \n        <div className={cn(\n          \"bg-gradient-to-r rounded-lg p-3\",\n          getSignalColor(tradingSignal)\n        )}>\n          <div className=\"flex items-center justify-between mb-2\">\n            <div className=\"flex items-center space-x-2\">\n              <SignalIcon className=\"w-5 h-5 text-white\" />\n              <span className=\"font-bold text-white\">{tradingSignal.type}</span>\n              <span className={cn(\"text-sm\", getStrengthColor(tradingSignal.strength))}>\n                {tradingSignal.strength}\n              </span>\n            </div>\n            <div className=\"text-white font-bold\">\n              {tradingSignal.confidence}%\n            </div>\n          </div>\n          \n          <div className=\"grid grid-cols-2 gap-3 text-white\">\n            <div className=\"flex items-center space-x-1\">\n              <Target className=\"w-3 h-3\" />\n              <span className=\"text-xs\">Target:</span>\n              <span className=\"text-xs font-medium\">\n                {formatCurrency(tradingSignal.price_target)}\n              </span>\n            </div>\n            \n            <div className=\"flex items-center space-x-1\">\n              <Shield className=\"w-3 h-3\" />\n              <span className=\"text-xs\">Stop:</span>\n              <span className=\"text-xs font-medium\">\n                {formatCurrency(tradingSignal.stop_loss)}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Support & Resistance */}\n      <div className=\"border-t border-slate-700/50 pt-3\">\n        <h4 className=\"text-sm font-medium text-white mb-2\">Suporte & Resistência</h4>\n        <div className=\"grid grid-cols-2 gap-3\">\n          <div>\n            <div className=\"text-xs text-gray-400 mb-1\">Suporte</div>\n            <div className=\"space-y-1\">\n              {technicalAnalysis.support_resistance.support.slice(0, 2).map((level, index) => (\n                <div key={index} className=\"text-xs text-green-400\">\n                  {formatCurrency(level)}\n                </div>\n              ))}\n            </div>\n          </div>\n          \n          <div>\n            <div className=\"text-xs text-gray-400 mb-1\">Resistência</div>\n            <div className=\"space-y-1\">\n              {technicalAnalysis.support_resistance.resistance.slice(0, 2).map((level, index) => (\n                <div key={index} className=\"text-xs text-red-400\">\n                  {formatCurrency(level)}\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AALA;;;;AAaO,SAAS,eAAe,EAC7B,UAAU,EACV,iBAAiB,EACjB,aAAa,EACO;IACpB,MAAM,aAAa,WAAW,2BAA2B,IAAI;IAE7D,MAAM,iBAAiB,CAAC;QACtB,OAAQ,OAAO,IAAI;YACjB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ,OAAO,IAAI;YACjB,KAAK;gBACH,OAAO,kNAAA,CAAA,aAAU;YACnB,KAAK;gBACH,OAAO,sNAAA,CAAA,eAAY;YACrB;gBACE,OAAO,0MAAA,CAAA,WAAQ;QACnB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;g<PERSON><PERSON>,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,cAAc;IAEjC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CACb,WAAW,MAAM,CAAC,WAAW;;;;;;;;;;;0CAGlC,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4B,WAAW,IAAI;;;;;;kDACzD,8OAAC;wCAAE,WAAU;;4CAAwB;4CAAE,WAAW,eAAe;;;;;;;;;;;;;;;;;;;kCAIrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,aAAa;;;;;;0CAE1C,8OAAC;gCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,yCACA,aAAa,mBAAmB;;oCAE/B,2BAAa,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;6DAAoB,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAC/E,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,2BAA2B;;;;;;;;;;;;;;;;;;;0BAM9D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;kDAAsB,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,UAAU;;;;;;;;;;;;0CAE1E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;kDAAsB,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,YAAY;;;;;;;;;;;;;;;;;;kCAI9E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;kDAAsB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,QAAQ;;;;;;;;;;;;0CAE1E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;kDAAsB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;0BAM7E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;kDACvC,8OAAC;wCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,uBACA,kBAAkB,GAAG,GAAG,KAAK,mBAC7B,kBAAkB,GAAG,GAAG,KAAK,iBAAiB;kDAE7C,kBAAkB,GAAG,CAAC,OAAO,CAAC;;;;;;;;;;;;0CAInC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;kDACvC,8OAAC;wCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,uBACA,kBAAkB,IAAI,CAAC,IAAI,GAAG,kBAAkB,IAAI,CAAC,MAAM,GAAG,mBAAmB;kDAEhF,kBAAkB,IAAI,CAAC,IAAI,GAAG,kBAAkB,IAAI,CAAC,MAAM,GAAG,YAAY;;;;;;;;;;;;0CAI/E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;kDACvC,8OAAC;wCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,uBACA,WAAW,aAAa,GAAG,kBAAkB,GAAG,CAAC,KAAK,GAAG,mBAAmB;kDAE3E,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,kBAAkB,GAAG,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAOnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAK,WAAU;kDAAyB,cAAc,SAAS;;;;;;;;;;;;;;;;;;kCAIpE,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,mCACA,eAAe;;0CAEf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAW,WAAU;;;;;;0DACtB,8OAAC;gDAAK,WAAU;0DAAwB,cAAc,IAAI;;;;;;0DAC1D,8OAAC;gDAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,iBAAiB,cAAc,QAAQ;0DACnE,cAAc,QAAQ;;;;;;;;;;;;kDAG3B,8OAAC;wCAAI,WAAU;;4CACZ,cAAc,UAAU;4CAAC;;;;;;;;;;;;;0CAI9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,YAAY;;;;;;;;;;;;kDAI9C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDACZ,kBAAkB,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACpE,8OAAC;gDAAgB,WAAU;0DACxB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;+CADR;;;;;;;;;;;;;;;;0CAOhB,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDACZ,kBAAkB,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACvE,8OAAC;gDAAgB,WAAU;0DACxB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;+CADR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B", "debugId": null}}, {"offset": {"line": 2097, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/components/chat/MessageBubble.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { User, BarChart3, TrendingUp, GraduationCap, Copy, ExternalLink } from 'lucide-react';\nimport { Message, AgentType } from '@/types';\nimport { cn, formatCurrency, formatPercentage, getTimeAgo } from '@/lib/utils';\nimport { CryptoDataCard } from './CryptoDataCard';\n\ninterface MessageBubbleProps {\n  message: Message;\n}\n\nexport function MessageBubble({ message }: MessageBubbleProps) {\n  const isUser = message.role === 'user';\n  \n  const getAgentIcon = (agent?: AgentType) => {\n    if (!agent) return User;\n    \n    const icons = {\n      'technical_analyst': BarChart3,\n      'experienced_trader': TrendingUp,\n      'market_consultant': GraduationCap\n    };\n    \n    return icons[agent] || User;\n  };\n\n  const getAgentColor = (agent?: AgentType) => {\n    if (!agent) return 'from-gray-500 to-gray-600';\n    \n    const colors = {\n      'technical_analyst': 'from-blue-500 to-cyan-500',\n      'experienced_trader': 'from-green-500 to-emerald-500',\n      'market_consultant': 'from-purple-500 to-pink-500'\n    };\n    \n    return colors[agent] || 'from-gray-500 to-gray-600';\n  };\n\n  const getAgentName = (agent?: AgentType) => {\n    if (!agent) return 'Usuário';\n    \n    const names = {\n      'technical_analyst': 'Dr. Marcus Chen',\n      'experienced_trader': 'Sarah \"The Wolf\" Rodriguez',\n      'market_consultant': 'Prof. Elena Nakamura'\n    };\n    \n    return names[agent] || 'Assistente';\n  };\n\n  const copyToClipboard = async (text: string) => {\n    try {\n      await navigator.clipboard.writeText(text);\n      // Aqui você poderia adicionar um toast de sucesso\n    } catch (err) {\n      console.error('Erro ao copiar texto:', err);\n    }\n  };\n\n  const formatMessageContent = (content: string) => {\n    // Converte markdown básico para HTML\n    return content\n      .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n      .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n      .replace(/`(.*?)`/g, '<code class=\"bg-black/20 px-1 py-0.5 rounded text-sm\">$1</code>')\n      .replace(/\\n/g, '<br />');\n  };\n\n  const IconComponent = getAgentIcon(message.agent);\n\n  return (\n    <div className={cn(\n      \"flex items-start space-x-3\",\n      isUser ? \"flex-row-reverse space-x-reverse\" : \"flex-row\"\n    )}>\n      {/* Avatar */}\n      <div className={cn(\n        \"w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 bg-gradient-to-r\",\n        isUser ? \"from-slate-600 to-slate-700\" : getAgentColor(message.agent)\n      )}>\n        <IconComponent className=\"w-5 h-5 text-white\" />\n      </div>\n\n      {/* Message Content */}\n      <div className={cn(\n        \"flex-1 max-w-3xl\",\n        isUser ? \"flex flex-col items-end\" : \"flex flex-col items-start\"\n      )}>\n        {/* Header */}\n        <div className={cn(\n          \"flex items-center space-x-2 mb-1\",\n          isUser ? \"flex-row-reverse space-x-reverse\" : \"flex-row\"\n        )}>\n          <span className=\"text-sm font-medium text-white\">\n            {isUser ? 'Você' : getAgentName(message.agent)}\n          </span>\n          <span className=\"text-xs text-gray-400\">\n            {getTimeAgo(message.timestamp)}\n          </span>\n        </div>\n\n        {/* Message Bubble */}\n        <div className={cn(\n          \"rounded-lg px-4 py-3 max-w-full\",\n          isUser \n            ? \"bg-gradient-to-r from-purple-600 to-pink-600 text-white\" \n            : \"bg-slate-800/80 text-gray-100 border border-slate-700/50\"\n        )}>\n          <div \n            className=\"prose prose-sm max-w-none prose-invert\"\n            dangerouslySetInnerHTML={{ \n              __html: formatMessageContent(message.content) \n            }}\n          />\n          \n          {/* Actions */}\n          {!isUser && (\n            <div className=\"flex items-center justify-end space-x-2 mt-3 pt-2 border-t border-slate-700/50\">\n              <button\n                onClick={() => copyToClipboard(message.content)}\n                className=\"p-1 text-gray-400 hover:text-white transition-colors duration-200\"\n                title=\"Copiar mensagem\"\n              >\n                <Copy className=\"w-4 h-4\" />\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Crypto Data Card */}\n        {message.cryptoData && message.technicalAnalysis && message.tradingSignal && (\n          <div className=\"mt-3 w-full\">\n            <CryptoDataCard \n              cryptoData={message.cryptoData}\n              technicalAnalysis={message.technicalAnalysis}\n              tradingSignal={message.tradingSignal}\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AANA;;;;;AAYO,SAAS,cAAc,EAAE,OAAO,EAAsB;IAC3D,MAAM,SAAS,QAAQ,IAAI,KAAK;IAEhC,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,OAAO,OAAO,kMAAA,CAAA,OAAI;QAEvB,MAAM,QAAQ;YACZ,qBAAqB,kNAAA,CAAA,YAAS;YAC9B,sBAAsB,kNAAA,CAAA,aAAU;YAChC,qBAAqB,wNAAA,CAAA,gBAAa;QACpC;QAEA,OAAO,KAAK,CAAC,MAAM,IAAI,kMAAA,CAAA,OAAI;IAC7B;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,SAAS;YACb,qBAAqB;YACrB,sBAAsB;YACtB,qBAAqB;QACvB;QAEA,OAAO,MAAM,CAAC,MAAM,IAAI;IAC1B;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,QAAQ;YACZ,qBAAqB;YACrB,sBAAsB;YACtB,qBAAqB;QACvB;QAEA,OAAO,KAAK,CAAC,MAAM,IAAI;IACzB;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,kDAAkD;QACpD,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,qCAAqC;QACrC,OAAO,QACJ,OAAO,CAAC,kBAAkB,uBAC1B,OAAO,CAAC,cAAc,eACtB,OAAO,CAAC,YAAY,mEACpB,OAAO,CAAC,OAAO;IACpB;IAEA,MAAM,gBAAgB,aAAa,QAAQ,KAAK;IAEhD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,8BACA,SAAS,qCAAqC;;0BAG9C,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,0FACA,SAAS,gCAAgC,cAAc,QAAQ,KAAK;0BAEpE,cAAA,8OAAC;oBAAc,WAAU;;;;;;;;;;;0BAI3B,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,oBACA,SAAS,4BAA4B;;kCAGrC,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,oCACA,SAAS,qCAAqC;;0CAE9C,8OAAC;gCAAK,WAAU;0CACb,SAAS,SAAS,aAAa,QAAQ,KAAK;;;;;;0CAE/C,8OAAC;gCAAK,WAAU;0CACb,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,SAAS;;;;;;;;;;;;kCAKjC,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,mCACA,SACI,4DACA;;0CAEJ,8OAAC;gCACC,WAAU;gCACV,yBAAyB;oCACvB,QAAQ,qBAAqB,QAAQ,OAAO;gCAC9C;;;;;;4BAID,CAAC,wBACA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS,IAAM,gBAAgB,QAAQ,OAAO;oCAC9C,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;oBAOvB,QAAQ,UAAU,IAAI,QAAQ,iBAAiB,IAAI,QAAQ,aAAa,kBACvE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4IAAA,CAAA,iBAAc;4BACb,YAAY,QAAQ,UAAU;4BAC9B,mBAAmB,QAAQ,iBAAiB;4BAC5C,eAAe,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;;;;;AAOlD", "debugId": null}}, {"offset": {"line": 2277, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/components/chat/TypingIndicator.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { BarChart3, TrendingUp, GraduationCap } from 'lucide-react';\nimport { AgentType } from '@/types';\nimport { cn } from '@/lib/utils';\n\ninterface TypingIndicatorProps {\n  agent: AgentType;\n}\n\nexport function TypingIndicator({ agent }: TypingIndicatorProps) {\n  const getAgentIcon = (agentType: AgentType) => {\n    const icons = {\n      'technical_analyst': BarChart3,\n      'experienced_trader': TrendingUp,\n      'market_consultant': GraduationCap\n    };\n    return icons[agentType];\n  };\n\n  const getAgentColor = (agentType: AgentType) => {\n    const colors = {\n      'technical_analyst': 'from-blue-500 to-cyan-500',\n      'experienced_trader': 'from-green-500 to-emerald-500',\n      'market_consultant': 'from-purple-500 to-pink-500'\n    };\n    return colors[agentType];\n  };\n\n  const getAgentName = (agentType: AgentType) => {\n    const names = {\n      'technical_analyst': 'Dr. <PERSON>',\n      'experienced_trader': '<PERSON> \"The Wolf\" <PERSON>',\n      'market_consultant': 'Prof<PERSON> <PERSON>'\n    };\n    return names[agentType];\n  };\n\n  const getTypingMessage = (agentType: AgentType) => {\n    const messages = {\n      'technical_analyst': 'Analisando indicadores técnicos...',\n      'experienced_trader': 'Checando o mercado...',\n      'market_consultant': 'Consultando dados fundamentais...'\n    };\n    return messages[agentType];\n  };\n\n  const IconComponent = getAgentIcon(agent);\n\n  return (\n    <div className=\"flex items-start space-x-3\">\n      {/* Avatar */}\n      <div className={cn(\n        \"w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 bg-gradient-to-r\",\n        getAgentColor(agent)\n      )}>\n        <IconComponent className=\"w-5 h-5 text-white\" />\n      </div>\n\n      {/* Typing Content */}\n      <div className=\"flex-1 max-w-3xl\">\n        {/* Header */}\n        <div className=\"flex items-center space-x-2 mb-1\">\n          <span className=\"text-sm font-medium text-white\">\n            {getAgentName(agent)}\n          </span>\n          <span className=\"text-xs text-gray-400\">\n            agora\n          </span>\n        </div>\n\n        {/* Typing Bubble */}\n        <div className=\"bg-slate-800/80 border border-slate-700/50 rounded-lg px-4 py-3\">\n          <div className=\"flex items-center space-x-2\">\n            {/* Typing Animation */}\n            <div className=\"flex space-x-1\">\n              <div className=\"w-2 h-2 bg-purple-500 rounded-full animate-bounce\" style={{ animationDelay: '0ms' }} />\n              <div className=\"w-2 h-2 bg-purple-500 rounded-full animate-bounce\" style={{ animationDelay: '150ms' }} />\n              <div className=\"w-2 h-2 bg-purple-500 rounded-full animate-bounce\" style={{ animationDelay: '300ms' }} />\n            </div>\n            \n            <span className=\"text-sm text-gray-300\">\n              {getTypingMessage(agent)}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAEA;AALA;;;;AAWO,SAAS,gBAAgB,EAAE,KAAK,EAAwB;IAC7D,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ;YACZ,qBAAqB,kNAAA,CAAA,YAAS;YAC9B,sBAAsB,kNAAA,CAAA,aAAU;YAChC,qBAAqB,wNAAA,CAAA,gBAAa;QACpC;QACA,OAAO,KAAK,CAAC,UAAU;IACzB;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAS;YACb,qBAAqB;YACrB,sBAAsB;YACtB,qBAAqB;QACvB;QACA,OAAO,MAAM,CAAC,UAAU;IAC1B;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ;YACZ,qBAAqB;YACrB,sBAAsB;YACtB,qBAAqB;QACvB;QACA,OAAO,KAAK,CAAC,UAAU;IACzB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW;YACf,qBAAqB;YACrB,sBAAsB;YACtB,qBAAqB;QACvB;QACA,OAAO,QAAQ,CAAC,UAAU;IAC5B;IAEA,MAAM,gBAAgB,aAAa;IAEnC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,0FACA,cAAc;0BAEd,cAAA,8OAAC;oBAAc,WAAU;;;;;;;;;;;0BAI3B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,aAAa;;;;;;0CAEhB,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;kCAM1C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAoD,OAAO;gDAAE,gBAAgB;4CAAM;;;;;;sDAClG,8OAAC;4CAAI,WAAU;4CAAoD,OAAO;gDAAE,gBAAgB;4CAAQ;;;;;;sDACpG,8OAAC;4CAAI,WAAU;4CAAoD,OAAO;gDAAE,gBAAgB;4CAAQ;;;;;;;;;;;;8CAGtG,8OAAC;oCAAK,WAAU;8CACb,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhC", "debugId": null}}, {"offset": {"line": 2450, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ia-sistemas/agente-crypto/src/components/chat/ChatInterface.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Send, Bot, User, TrendingUp, BarChart3, GraduationCap } from 'lucide-react';\nimport { Message, AgentType } from '@/types';\nimport { AIService } from '@/lib/ai-service';\nimport { cn } from '@/lib/utils';\nimport { useChatContext } from '@/contexts/ChatContext';\nimport { AgentSelector } from './AgentSelector';\nimport { MessageBubble } from './MessageBubble';\nimport { TypingIndicator } from './TypingIndicator';\n\nexport function ChatInterface() {\n  const {\n    state,\n    addMessage,\n    setActiveAgent,\n    setLoading,\n    setCurrentCrypto,\n    setLastAnalysis\n  } = useChatContext();\n\n  const [inputValue, setInputValue] = useState('');\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [state.messages]);\n\n  useEffect(() => {\n    // Mensagem de boas-vindas apenas se não há mensagens\n    if (state.messages.length === 0) {\n      const welcomeMessage: Message = {\n        id: 'welcome',\n        content: `Olá! 👋 Bem-vindo ao **CryptoAgent AI**!\n\nSou seu assistente especializado em criptomoedas com 3 agentes especializados:\n\n🔹 **Dr. Marcus Chen** 👨‍💼 - Analista Técnico\n🔹 **Sarah \"The Wolf\" Rodriguez** 🐺 - Trader Experiente\n🔹 **Prof. Elena Nakamura** 👩‍🏫 - Consultora de Mercado\n\nPosso ajudá-lo com:\n• Análises técnicas em tempo real\n• Sinais de compra/venda\n• Contexto de mercado e tendências\n• Estratégias de trading\n\n**Para começar, digite o nome de uma criptomoeda** (ex: Bitcoin, ETH, Solana) ou faça uma pergunta sobre o mercado! 🚀`,\n        role: 'assistant',\n        agent: state.activeAgent,\n        timestamp: new Date()\n      };\n\n      addMessage(welcomeMessage);\n    }\n  }, [state.messages.length, state.activeAgent, addMessage]);\n\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || state.isLoading) return;\n\n    const userMessage = inputValue.trim();\n    setInputValue('');\n    setLoading(true);\n\n    try {\n      // Adiciona mensagem do usuário\n      const userMsg: Message = {\n        id: Date.now().toString(),\n        content: userMessage,\n        role: 'user',\n        timestamp: new Date()\n      };\n\n      addMessage(userMsg);\n\n      // Processa mensagem com IA\n      const response = await AIService.processMessage(userMessage, state.activeAgent);\n\n      addMessage(response);\n\n      // Atualiza contexto se necessário\n      if (response.cryptoData) {\n        setCurrentCrypto(response.cryptoData.id);\n      }\n      if (response.technicalAnalysis) {\n        setLastAnalysis(new Date());\n      }\n    } catch (error) {\n      console.error('Erro ao processar mensagem:', error);\n\n      const errorMsg: Message = {\n        id: Date.now().toString(),\n        content: 'Desculpe, ocorreu um erro ao processar sua mensagem. Tente novamente.',\n        role: 'assistant',\n        agent: state.activeAgent,\n        timestamp: new Date()\n      };\n\n      addMessage(errorMsg);\n    } finally {\n      setLoading(false);\n      inputRef.current?.focus();\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const handleAgentChange = (newAgent: AgentType) => {\n    setActiveAgent(newAgent);\n    AIService.switchAgent(newAgent);\n\n    // Adiciona mensagem informando sobre a troca\n    const switchMessage: Message = {\n      id: Date.now().toString(),\n      content: `Agente alterado para **${getAgentName(newAgent)}** ${getAgentEmoji(newAgent)}`,\n      role: 'assistant',\n      agent: newAgent,\n      timestamp: new Date()\n    };\n\n    addMessage(switchMessage);\n  };\n\n  const getAgentName = (agent: AgentType): string => {\n    const names = {\n      'technical_analyst': 'Dr. Marcus Chen',\n      'experienced_trader': 'Sarah \"The Wolf\" Rodriguez',\n      'market_consultant': 'Prof. Elena Nakamura'\n    };\n    return names[agent];\n  };\n\n  const getAgentEmoji = (agent: AgentType): string => {\n    const emojis = {\n      'technical_analyst': '👨‍💼',\n      'experienced_trader': '🐺',\n      'market_consultant': '👩‍🏫'\n    };\n    return emojis[agent];\n  };\n\n  return (\n    <div className=\"flex flex-col h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      {/* Header */}\n      <div className=\"bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4\">\n        <div className=\"max-w-4xl mx-auto flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center\">\n              <Bot className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-xl font-bold text-white\">CryptoAgent AI</h1>\n              <p className=\"text-sm text-purple-300\">\n                Agente ativo: {getAgentName(state.activeAgent)} {getAgentEmoji(state.activeAgent)}\n              </p>\n            </div>\n          </div>\n          \n          <AgentSelector\n            activeAgent={state.activeAgent}\n            onAgentChange={handleAgentChange}\n          />\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-y-auto p-4\">\n        <div className=\"max-w-4xl mx-auto space-y-4\">\n          {state.messages.map((message) => (\n            <MessageBubble key={message.id} message={message} />\n          ))}\n\n          {state.isLoading && <TypingIndicator agent={state.activeAgent} />}\n          \n          <div ref={messagesEndRef} />\n        </div>\n      </div>\n\n      {/* Input */}\n      <div className=\"bg-black/20 backdrop-blur-sm border-t border-purple-500/20 p-4\">\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"flex-1 relative\">\n              <input\n                ref={inputRef}\n                type=\"text\"\n                value={inputValue}\n                onChange={(e) => setInputValue(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"Digite o nome de uma criptomoeda ou faça uma pergunta...\"\n                className=\"w-full bg-white/10 border border-purple-500/30 rounded-lg px-4 py-3 text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                disabled={state.isLoading}\n              />\n            </div>\n            \n            <button\n              onClick={handleSendMessage}\n              disabled={!inputValue.trim() || state.isLoading}\n              className={cn(\n                \"p-3 rounded-lg transition-all duration-200\",\n                inputValue.trim() && !state.isLoading\n                  ? \"bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white shadow-lg\"\n                  : \"bg-gray-600 text-gray-400 cursor-not-allowed\"\n              )}\n            >\n              <Send className=\"w-5 h-5\" />\n            </button>\n          </div>\n          \n          <div className=\"mt-2 text-xs text-purple-300 text-center\">\n            Pressione Enter para enviar • Shift+Enter para nova linha\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;AAYO,SAAS;IACd,MAAM,EACJ,KAAK,EACL,UAAU,EACV,cAAc,EACd,UAAU,EACV,gBAAgB,EAChB,eAAe,EAChB,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,MAAM,QAAQ;KAAC;IAEnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qDAAqD;QACrD,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,GAAG;YAC/B,MAAM,iBAA0B;gBAC9B,IAAI;gBACJ,SAAS,CAAC;;;;;;;;;;;;;;sHAcoG,CAAC;gBAC/G,MAAM;gBACN,OAAO,MAAM,WAAW;gBACxB,WAAW,IAAI;YACjB;YAEA,WAAW;QACb;IACF,GAAG;QAAC,MAAM,QAAQ,CAAC,MAAM;QAAE,MAAM,WAAW;QAAE;KAAW;IAEzD,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,MAAM,MAAM,SAAS,EAAE;QAE3C,MAAM,cAAc,WAAW,IAAI;QACnC,cAAc;QACd,WAAW;QAEX,IAAI;YACF,+BAA+B;YAC/B,MAAM,UAAmB;gBACvB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,SAAS;gBACT,MAAM;gBACN,WAAW,IAAI;YACjB;YAEA,WAAW;YAEX,2BAA2B;YAC3B,MAAM,WAAW,MAAM,2HAAA,CAAA,YAAS,CAAC,cAAc,CAAC,aAAa,MAAM,WAAW;YAE9E,WAAW;YAEX,kCAAkC;YAClC,IAAI,SAAS,UAAU,EAAE;gBACvB,iBAAiB,SAAS,UAAU,CAAC,EAAE;YACzC;YACA,IAAI,SAAS,iBAAiB,EAAE;gBAC9B,gBAAgB,IAAI;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,MAAM,WAAoB;gBACxB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,SAAS;gBACT,MAAM;gBACN,OAAO,MAAM,WAAW;gBACxB,WAAW,IAAI;YACjB;YAEA,WAAW;QACb,SAAU;YACR,WAAW;YACX,SAAS,OAAO,EAAE;QACpB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,eAAe;QACf,2HAAA,CAAA,YAAS,CAAC,WAAW,CAAC;QAEtB,6CAA6C;QAC7C,MAAM,gBAAyB;YAC7B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS,CAAC,uBAAuB,EAAE,aAAa,UAAU,GAAG,EAAE,cAAc,WAAW;YACxF,MAAM;YACN,OAAO;YACP,WAAW,IAAI;QACjB;QAEA,WAAW;IACb;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ;YACZ,qBAAqB;YACrB,sBAAsB;YACtB,qBAAqB;QACvB;QACA,OAAO,KAAK,CAAC,MAAM;IACrB;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAS;YACb,qBAAqB;YACrB,sBAAsB;YACtB,qBAAqB;QACvB;QACA,OAAO,MAAM,CAAC,MAAM;IACtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAE,WAAU;;gDAA0B;gDACtB,aAAa,MAAM,WAAW;gDAAE;gDAAE,cAAc,MAAM,WAAW;;;;;;;;;;;;;;;;;;;sCAKtF,8OAAC,2IAAA,CAAA,gBAAa;4BACZ,aAAa,MAAM,WAAW;4BAC9B,eAAe;;;;;;;;;;;;;;;;;0BAMrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACnB,8OAAC,2IAAA,CAAA,gBAAa;gCAAkB,SAAS;+BAArB,QAAQ,EAAE;;;;;wBAG/B,MAAM,SAAS,kBAAI,8OAAC,6IAAA,CAAA,kBAAe;4BAAC,OAAO,MAAM,WAAW;;;;;;sCAE7D,8OAAC;4BAAI,KAAK;;;;;;;;;;;;;;;;;0BAKd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,KAAK;wCACL,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,YAAY;wCACZ,aAAY;wCACZ,WAAU;wCACV,UAAU,MAAM,SAAS;;;;;;;;;;;8CAI7B,8OAAC;oCACC,SAAS;oCACT,UAAU,CAAC,WAAW,IAAI,MAAM,MAAM,SAAS;oCAC/C,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8CACA,WAAW,IAAI,MAAM,CAAC,MAAM,SAAS,GACjC,8GACA;8CAGN,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIpB,8OAAC;4BAAI,WAAU;sCAA2C;;;;;;;;;;;;;;;;;;;;;;;AAOpE", "debugId": null}}]}