# 🚀 CryptoAgent AI - Sistema de Análise de Criptomoedas com IA

Um sistema avançado de análise de criptomoedas com **3 agentes de IA especializados** que fornecem análises técnicas, sinais de trading e consultoria de mercado em tempo real.

## ✨ Características Principais

### 🤖 3 Agentes Especializados

1. **👨‍💼 <PERSON><PERSON> <PERSON> - Analista Técnico**
   - Especialista em indicadores técnicos (RSI, MACD, SMA, EMA, Bollinger Bands)
   - Análise de padrões de candlestick e formações gráficas
   - Níveis de suporte e resistência
   - Análise de volume e momentum

2. **🐺 <PERSON> "The Wolf" Rodriguez - Trader Experiente**
   - 15 anos de experiência em mercados financeiros
   - Gestão de risco e money management
   - Psicologia do trading
   - Estratégias práticas de entrada e saída

3. **👩‍🏫 <PERSON><PERSON> <PERSON> - Consultora de Mercado**
   - Análise fundamentalista
   - Tendências macroeconômicas
   - Regulamentações e políticas
   - Adoção institucional e tecnologia blockchain

### 🔥 Funcionalidades

- **💬 Chat Inteligente**: Interface conversacional natural com contexto persistente
- **📊 Análise Técnica em Tempo Real**: Dados atualizados das principais APIs de criptomoedas
- **🎯 Sinais de Trading**: Recomendações de compra/venda com níveis de confiança
- **📈 Indicadores Técnicos**: RSI, MACD, SMA, EMA, Bollinger Bands, suporte/resistência
- **🌍 Dados Globais**: Informações de mercado, Fear & Greed Index, moedas em tendência
- **💾 Memória Persistente**: Histórico de conversas salvo localmente
- **📱 Interface Responsiva**: Design moderno e adaptável

## 🛠️ Tecnologias Utilizadas

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS, Lucide React Icons
- **APIs**: CoinGecko, Alternative.me (Fear & Greed Index)
- **Estado**: Context API + useReducer
- **Persistência**: localStorage
- **Análise Técnica**: Algoritmos próprios para cálculo de indicadores

## 🚀 Como Executar

### Pré-requisitos
- Node.js 18+
- npm, yarn ou pnpm

### Instalação

1. **Clone o repositório**
```bash
git clone <url-do-repositorio>
cd agente-crypto
```

2. **Instale as dependências**
```bash
npm install
# ou
yarn install
# ou
pnpm install
```

3. **Execute o servidor de desenvolvimento**
```bash
npm run dev
# ou
yarn dev
# ou
pnpm dev
```

4. **Abra no navegador**
```
http://localhost:3000
```

## 📖 Como Usar

### 1. **Iniciando uma Conversa**
- Digite o nome de uma criptomoeda (ex: "Bitcoin", "ETH", "Solana")
- Faça perguntas sobre mercado (ex: "Como está o mercado hoje?")
- Cumprimente os agentes (ex: "Oi", "Bom dia")

### 2. **Trocando de Agente**
- Use o seletor no canto superior direito
- Cada agente tem especialidades diferentes
- O contexto da conversa é mantido entre trocas

### 3. **Exemplos de Perguntas**
```
"Analise o Bitcoin"
"Qual o melhor momento para comprar ETH?"
"Como está o sentimento do mercado?"
"Explique o RSI do Solana"
"Devo vender meu Cardano?"
```

## 🏗️ Arquitetura do Sistema

### Estrutura de Pastas
```
src/
├── app/                    # App Router do Next.js
│   ├── api/               # API Routes
│   │   ├── chat/          # Endpoint do chat
│   │   ├── crypto/        # Dados de criptomoedas
│   │   ├── market/        # Dados de mercado
│   │   └── analysis/      # Análise técnica
│   ├── layout.tsx         # Layout principal
│   └── page.tsx           # Página inicial
├── components/            # Componentes React
│   └── chat/             # Componentes do chat
├── contexts/             # Context API
├── lib/                  # Bibliotecas e utilitários
│   ├── agents.ts         # Definição dos agentes
│   ├── ai-service.ts     # Serviço de IA
│   ├── crypto-api.ts     # API de criptomoedas
│   ├── technical-analysis.ts # Análise técnica
│   └── utils.ts          # Utilitários
└── types/                # Definições TypeScript
```

### APIs Disponíveis

#### 🔗 Endpoints

1. **GET /api/crypto/[coinId]**
   - Dados detalhados de uma criptomoeda
   - Exemplo: `/api/crypto/bitcoin`

2. **GET /api/market**
   - `?type=global` - Dados globais do mercado
   - `?type=popular&limit=10` - Top criptomoedas
   - `?type=multiple&coins=bitcoin,ethereum` - Múltiplas moedas

3. **GET /api/analysis/[coinId]**
   - Análise técnica completa
   - Indicadores e sinais de trading

4. **POST /api/chat**
   - Processamento de mensagens do chat
   - Body: `{ message: string, agent?: AgentType }`

## 🧠 Sistema de IA

### Estratégia de Confluência
O sistema utiliza uma **estratégia de confluência obrigatória** que combina 3 categorias de indicadores:

1. **Tendência**: SMA, EMA, Ichimoku, Parabolic SAR, ADX
2. **Momentum**: RSI, MACD, Estocástico, Awesome Oscillator
3. **Volume**: OBV, VWAP, MFI

### Geração de Sinais
- **BUY**: ≥60% dos indicadores bullish
- **SELL**: ≥60% dos indicadores bearish
- **HOLD**: Sinais mistos ou inconclusivos

### Níveis de Confiança
- **STRONG**: ≥80% de confluência
- **MODERATE**: 60-79% de confluência
- **WEAK**: <60% de confluência

## 🔧 Configuração Avançada

### Variáveis de Ambiente (Opcionais)
```env
# Não são necessárias para funcionamento básico
# APIs públicas são utilizadas por padrão
```

### Personalização de Agentes
Edite `src/lib/agents.ts` para:
- Modificar personalidades dos agentes
- Adicionar novas especialidades
- Customizar prompts do sistema

### Cache de Dados
- Cache automático de 1 minuto para APIs externas
- Reduz latência e evita rate limiting
- Configurável em `src/lib/crypto-api.ts`

## 🚀 Deploy

### Vercel (Recomendado)
```bash
npm run build
vercel --prod
```

### Docker
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 🆘 Suporte

- **Issues**: Reporte bugs ou solicite features
- **Discussões**: Tire dúvidas sobre uso
- **Wiki**: Documentação detalhada (em breve)

## 🔮 Roadmap

- [ ] Integração com mais exchanges
- [ ] Alertas por email/SMS
- [ ] Análise de sentimento de redes sociais
- [ ] Backtesting de estratégias
- [ ] API para desenvolvedores
- [ ] Aplicativo mobile

---

**⚠️ Aviso Legal**: Este sistema é apenas para fins educacionais e informativos. Não constitui aconselhamento financeiro. Sempre faça sua própria pesquisa antes de investir.
