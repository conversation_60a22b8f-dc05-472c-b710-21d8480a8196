// Tipos para o sistema de agentes de criptomoedas

export interface CryptoData {
  id: string;
  symbol: string;
  name: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  fully_diluted_valuation: number;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  market_cap_change_24h: number;
  market_cap_change_percentage_24h: number;
  circulating_supply: number;
  total_supply: number;
  max_supply: number;
  ath: number;
  ath_change_percentage: number;
  ath_date: string;
  atl: number;
  atl_change_percentage: number;
  atl_date: string;
  last_updated: string;
}

export interface TechnicalIndicators {
  rsi: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  sma: {
    sma20: number;
    sma50: number;
    sma200: number;
  };
  ema: {
    ema12: number;
    ema26: number;
  };
  bollinger: {
    upper: number;
    middle: number;
    lower: number;
  };
  volume_profile: number;
  support_resistance: {
    support: number[];
    resistance: number[];
  };
}

export interface TradingSignal {
  type: 'BUY' | 'SELL' | 'HOLD';
  strength: 'WEAK' | 'MODERATE' | 'STRONG';
  confidence: number; // 0-100
  price_target: number;
  stop_loss: number;
  timeframe: string;
  reasoning: string;
}

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  agent?: AgentType;
  timestamp: Date;
  cryptoData?: CryptoData;
  technicalAnalysis?: TechnicalIndicators;
  tradingSignal?: TradingSignal;
}

export type AgentType = 'technical_analyst' | 'experienced_trader' | 'market_consultant';

export interface Agent {
  id: AgentType;
  name: string;
  description: string;
  personality: string;
  specialties: string[];
  avatar: string;
  systemPrompt: string;
}

export interface ChatContext {
  messages: Message[];
  currentCrypto?: string;
  activeAgent: AgentType;
  marketSentiment: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  lastAnalysis?: Date;
}

export interface MarketData {
  global: {
    total_market_cap: number;
    total_volume: number;
    market_cap_percentage: Record<string, number>;
    market_cap_change_percentage_24h_usd: number;
  };
  fear_greed_index: number;
  trending_coins: string[];
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
}
