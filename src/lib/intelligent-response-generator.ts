import { AgentType, CryptoData } from '@/types';
import { IntentType, IntentClassification } from './intent-classifier';
import { ContextAnalysis } from './context-analyzer';

export interface ResponseTemplate {
  systemPrompt: string;
  userPrompt: string;
  includeMarketData: boolean;
  includeTechnicalAnalysis: boolean;
  responseStyle: 'conversational' | 'analytical' | 'educational' | 'supportive';
}

export class IntelligentResponseGenerator {
  
  /**
   * Gera um template de resposta baseado na intenção e contexto
   */
  static generateResponseTemplate(
    intent: IntentClassification,
    context: ContextAnalysis,
    agentType: AgentType,
    userMessage: string
  ): ResponseTemplate {
    
    const agentPersonality = this.getAgentPersonality(agentType);
    
    switch (intent.intent) {
      case 'AGENT_INFO':
        return this.generateAgentInfoResponse(agentPersonality, context, userMessage);
      
      case 'GREETING':
        return this.generateGreetingResponse(agentPersonality, context);
      
      case 'HELP':
        return this.generateHelpResponse(agentPersonality, context);
      
      case 'CRYPTO_ANALYSIS':
        return this.generateCryptoAnalysisResponse(agentPersonality, context, intent);
      
      case 'TRADING_ADVICE':
        return this.generateTradingAdviceResponse(agentPersonality, context, intent);
      
      case 'MARKET_GENERAL':
        return this.generateMarketGeneralResponse(agentPersonality, context);
      
      case 'COMPARISON':
        return this.generateComparisonResponse(agentPersonality, context, intent);
      
      case 'PRICE_PREDICTION':
        return this.generatePricePredictionResponse(agentPersonality, context, intent);
      
      case 'PERSONAL_CHAT':
        return this.generatePersonalChatResponse(agentPersonality, context);
      
      default:
        return this.generateDefaultResponse(agentPersonality, context);
    }
  }
  
  /**
   * Obtém a personalidade do agente
   */
  private static getAgentPersonality(agentType: AgentType) {
    const personalities = {
      ANALYST: {
        name: 'Sarah "The Wolf" Rodriguez',
        traits: 'analítica, precisa, focada em dados',
        expertise: 'análise técnica e fundamental',
        style: 'profissional e detalhada'
      },
      MENTOR: {
        name: 'Marcus "The Sage" Chen',
        traits: 'educativo, paciente, experiente',
        expertise: 'educação e estratégias de longo prazo',
        style: 'didático e encorajador'
      },
      STRATEGIST: {
        name: 'Alex "The Navigator" Kim',
        traits: 'estratégico, visionário, adaptável',
        expertise: 'estratégias de mercado e timing',
        style: 'estratégico e inspirador'
      }
    };
    
    return personalities[agentType];
  }
  
  /**
   * Gera resposta para perguntas sobre o agente
   */
  private static generateAgentInfoResponse(
    personality: any,
    context: ContextAnalysis,
    userMessage: string
  ): ResponseTemplate {
    
    const isAboutAvailability = /\b(trabalha|funciona|disponível|disponivel|24\s*horas?|horário|horario)\b/i.test(userMessage);
    const isAboutCapabilities = /\b(pode|consegue|sabe|especialidades|habilidades|capacidades)\b/i.test(userMessage);
    const isAboutIdentity = /\b(quem\s+)?(você|voce)\s+(é|eh|sou)\b/i.test(userMessage);

    const agentName = personality?.name || 'seu assistente de criptomoedas';
    const agentTraits = personality?.traits || 'profissional e prestativo';
    const agentExpertise = personality?.expertise || 'análise de criptomoedas';
    const agentStyle = personality?.style || 'profissional e detalhado';

    let systemPrompt = `Você é ${agentName}, um agente de IA especializado em criptomoedas.
Suas características: ${agentTraits}.
Sua expertise: ${agentExpertise}.
Seu estilo: ${agentStyle}.

IMPORTANTE: Responda especificamente sobre VOCÊ como agente, não sobre análise de criptomoedas.`;

    let userPrompt = '';

    if (isAboutAvailability) {
      return {
        systemPrompt: `Você é ${agentName}, um agente de IA especializado em criptomoedas.
O usuário está perguntando especificamente sobre sua disponibilidade/horário de funcionamento.
RESPONDA APENAS sobre disponibilidade - NÃO fale sobre análise de criptomoedas nesta resposta.`,
        userPrompt: `Responda de forma clara e direta que você está disponível 24 horas por dia, 7 dias por semana,
como um assistente de IA. Mantenha sua personalidade de ${agentName} mas foque apenas na disponibilidade.
Pergunta: "${userMessage}"`,
        includeMarketData: false,
        includeTechnicalAnalysis: false,
        responseStyle: 'conversational'
      };
    } else if (isAboutCapabilities) {
      userPrompt = `O usuário quer saber sobre suas capacidades e habilidades.
Explique o que você pode fazer como ${agentName}, suas especialidades em criptomoedas,
e como pode ajudar o usuário. Mantenha o foco em suas capacidades, não em análises específicas.

Pergunta do usuário: "${userMessage}"`;
    } else if (isAboutIdentity) {
      userPrompt = `O usuário quer saber quem você é.
Apresente-se como ${agentName}, explique sua personalidade (${agentTraits}),
sua área de expertise (${agentExpertise}) e como você pode ajudar.

Pergunta do usuário: "${userMessage}"`;
    } else {
      userPrompt = `O usuário está fazendo uma pergunta sobre você como agente.
Responda de forma apropriada mantendo sua personalidade de ${agentName}.
Foque na pergunta específica sobre você, não desvie para análise de mercado.

Pergunta do usuário: "${userMessage}"`;
    }
    
    return {
      systemPrompt,
      userPrompt,
      includeMarketData: false,
      includeTechnicalAnalysis: false,
      responseStyle: context.suggestedResponseTone === 'casual' ? 'conversational' : 'supportive'
    };
  }
  
  /**
   * Gera resposta para saudações
   */
  private static generateGreetingResponse(
    personality: any,
    context: ContextAnalysis
  ): ResponseTemplate {

    const agentName = personality?.name || 'seu assistente de criptomoedas';
    const agentTraits = personality?.traits || 'profissional e prestativo';

    const systemPrompt = `Você é ${agentName}, um agente de IA especializado em criptomoedas.
Responda à saudação de forma amigável e natural, apresentando-se brevemente e oferecendo ajuda.
Mantenha sua personalidade: ${agentTraits}.`;

    const userPrompt = `Responda à saudação do usuário de forma calorosa e profissional.
Apresente-se como ${agentName} e ofereça sua ajuda com análises de criptomoedas.
Seja ${context.userExpertiseLevel === 'beginner' ? 'mais didático' : 'mais direto'}.`;

    return {
      systemPrompt,
      userPrompt,
      includeMarketData: false,
      includeTechnicalAnalysis: false,
      responseStyle: 'conversational'
    };
  }
  
  /**
   * Gera resposta para pedidos de ajuda
   */
  private static generateHelpResponse(
    personality: any,
    context: ContextAnalysis
  ): ResponseTemplate {

    const agentName = personality?.name || 'seu assistente de criptomoedas';
    const agentStyle = personality?.style || 'profissional e prestativo';

    const systemPrompt = `Você é ${agentName}, especialista em criptomoedas.
O usuário precisa de ajuda. Seja ${agentStyle} e ofereça orientação clara.
Nível do usuário: ${context.userExpertiseLevel}.`;

    const userPrompt = `O usuário está pedindo ajuda. Explique como você pode ajudar,
quais tipos de análises você faz, e como o usuário pode interagir com você.
Adapte sua linguagem para um usuário ${context.userExpertiseLevel}.`;

    return {
      systemPrompt,
      userPrompt,
      includeMarketData: false,
      includeTechnicalAnalysis: false,
      responseStyle: 'educational'
    };
  }
  
  /**
   * Gera resposta para análise de criptomoedas
   */
  private static generateCryptoAnalysisResponse(
    personality: any,
    context: ContextAnalysis,
    intent: IntentClassification
  ): ResponseTemplate {

    const agentName = personality?.name || 'seu assistente de criptomoedas';
    const agentExpertise = personality?.expertise || 'análise de criptomoedas';
    const agentStyle = personality?.style || 'profissional e detalhado';

    const systemPrompt = `Você é ${agentName}, especialista em ${agentExpertise}.
Analise as criptomoedas mencionadas: ${intent.entities.join(', ')}.
Seja ${agentStyle} e adapte para usuário ${context.userExpertiseLevel}.
${context.topicContinuity ? 'Continue a análise anterior se relevante.' : ''}`;

    const userPrompt = `Analise as criptomoedas: ${intent.entities.join(', ')}.
Forneça insights ${agentExpertise.includes('técnica') ? 'técnicos' : 'estratégicos'}.
Considere o contexto: ${context.previousContext}.
Usuário é ${context.userExpertiseLevel}.`;

    return {
      systemPrompt,
      userPrompt,
      includeMarketData: true,
      includeTechnicalAnalysis: true,
      responseStyle: 'analytical'
    };
  }
  
  /**
   * Gera resposta para conselhos de trading
   */
  private static generateTradingAdviceResponse(
    personality: any,
    context: ContextAnalysis,
    intent: IntentClassification
  ): ResponseTemplate {

    const agentName = personality?.name || 'seu assistente de criptomoedas';
    const agentExpertise = personality?.expertise || 'trading e análise de mercado';
    const agentStyle = personality?.style || 'profissional e cauteloso';

    const systemPrompt = `Você é ${agentName}, especialista em ${agentExpertise}.
Forneça conselhos de trading responsáveis. SEMPRE inclua disclaimers sobre riscos.
Seja ${agentStyle} mas cauteloso. Usuário: ${context.userExpertiseLevel}.`;

    const userPrompt = `O usuário busca conselhos de trading sobre: ${intent.entities.join(', ')}.
Forneça orientações estratégicas, mas sempre com disclaimers de risco.
Adapte para usuário ${context.userExpertiseLevel}.
${context.userMood === 'urgent' ? 'Usuário parece ansioso - seja tranquilizador.' : ''}`;

    return {
      systemPrompt,
      userPrompt,
      includeMarketData: true,
      includeTechnicalAnalysis: true,
      responseStyle: context.userMood === 'urgent' ? 'supportive' : 'analytical'
    };
  }
  
  /**
   * Gera resposta para perguntas gerais sobre mercado
   */
  private static generateMarketGeneralResponse(
    personality: any,
    context: ContextAnalysis
  ): ResponseTemplate {

    const agentName = personality?.name || 'seu assistente de criptomoedas';
    const agentStyle = personality?.style || 'analítico e informativo';

    const systemPrompt = `Você é ${agentName}, especialista em mercados de criptomoedas.
Forneça uma visão geral do mercado atual. Seja ${agentStyle}.
Usuário: ${context.userExpertiseLevel}.`;

    const userPrompt = `Forneça uma análise geral do mercado de criptomoedas atual.
Inclua sentimento, tendências principais e fatores relevantes.
Adapte para usuário ${context.userExpertiseLevel}.`;

    return {
      systemPrompt,
      userPrompt,
      includeMarketData: true,
      includeTechnicalAnalysis: false,
      responseStyle: 'analytical'
    };
  }
  
  /**
   * Gera resposta para comparações
   */
  private static generateComparisonResponse(
    personality: any,
    context: ContextAnalysis,
    intent: IntentClassification
  ): ResponseTemplate {

    const agentName = personality?.name || 'seu assistente de criptomoedas';
    const agentStyle = personality?.style || 'objetivo e imparcial';

    const systemPrompt = `Você é ${agentName}. Compare as criptomoedas mencionadas: ${intent.entities.join(', ')}.
Seja ${agentStyle} e objetivo. Usuário: ${context.userExpertiseLevel}.`;

    const userPrompt = `Compare as criptomoedas: ${intent.entities.join(', ')}.
Analise prós, contras, casos de uso e potencial.
Seja imparcial e educativo para usuário ${context.userExpertiseLevel}.`;

    return {
      systemPrompt,
      userPrompt,
      includeMarketData: true,
      includeTechnicalAnalysis: true,
      responseStyle: 'educational'
    };
  }
  
  /**
   * Gera resposta para previsões de preço
   */
  private static generatePricePredictionResponse(
    personality: any,
    context: ContextAnalysis,
    intent: IntentClassification
  ): ResponseTemplate {

    const agentName = personality?.name || 'seu assistente de criptomoedas';
    const agentStyle = personality?.style || 'responsável e educativo';

    const systemPrompt = `Você é ${agentName}. CUIDADO: Nunca faça previsões definitivas de preço.
Discuta fatores que podem influenciar preços, mas sempre com disclaimers.
Seja ${agentStyle} mas responsável.`;

    const userPrompt = `O usuário pergunta sobre previsões de preço para: ${intent.entities.join(', ')}.
Discuta fatores técnicos e fundamentais, mas NÃO faça previsões específicas.
Eduque sobre a imprevisibilidade do mercado.`;

    return {
      systemPrompt,
      userPrompt,
      includeMarketData: true,
      includeTechnicalAnalysis: true,
      responseStyle: 'educational'
    };
  }
  
  /**
   * Gera resposta para conversa pessoal
   */
  private static generatePersonalChatResponse(
    personality: any,
    context: ContextAnalysis
  ): ResponseTemplate {

    const agentName = personality?.name || 'seu assistente de criptomoedas';

    const systemPrompt = `Você é ${agentName}. O usuário quer conversar de forma mais pessoal.
Seja amigável mas mantenha o foco profissional. Redirecione gentilmente para criptomoedas.`;

    const userPrompt = `Responda de forma amigável mas profissional.
Mantenha sua personalidade de ${agentName} e redirecione para como pode ajudar com criptomoedas.`;

    return {
      systemPrompt,
      userPrompt,
      includeMarketData: false,
      includeTechnicalAnalysis: false,
      responseStyle: 'conversational'
    };
  }

  /**
   * Gera resposta padrão para casos não identificados
   */
  private static generateDefaultResponse(
    personality: any,
    context: ContextAnalysis
  ): ResponseTemplate {

    const agentName = personality?.name || 'seu assistente de criptomoedas';

    const systemPrompt = `Você é ${agentName}. A intenção do usuário não foi clara.
Peça esclarecimentos de forma educada e ofereça opções de como pode ajudar.`;

    const userPrompt = `A pergunta não foi clara. Peça esclarecimentos educadamente e
liste as principais formas como você pode ajudar com criptomoedas.`;

    return {
      systemPrompt,
      userPrompt,
      includeMarketData: false,
      includeTechnicalAnalysis: false,
      responseStyle: 'supportive'
    };
  }
}
