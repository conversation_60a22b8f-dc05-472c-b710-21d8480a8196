import { AgentType, CryptoData } from '@/types';
import { IntentType, IntentClassification } from './intent-classifier';
import { ContextAnalysis } from './context-analyzer';

export interface ResponseTemplate {
  systemPrompt: string;
  userPrompt: string;
  includeMarketData: boolean;
  includeTechnicalAnalysis: boolean;
  responseStyle: 'conversational' | 'analytical' | 'educational' | 'supportive';
}

export class IntelligentResponseGenerator {
  
  /**
   * Gera um template de resposta baseado na intenção e contexto
   */
  static generateResponseTemplate(
    intent: IntentClassification,
    context: ContextAnalysis,
    agentType: AgentType,
    userMessage: string
  ): ResponseTemplate {
    
    const agentPersonality = this.getAgentPersonality(agentType);
    
    switch (intent.intent) {
      case 'AGENT_INFO':
        return this.generateAgentInfoResponse(agentPersonality, context, userMessage);
      
      case 'GREETING':
        return this.generateGreetingResponse(agentPersonality, context);
      
      case 'HELP':
        return this.generateHelpResponse(agentPersonality, context);
      
      case 'CRYPTO_ANALYSIS':
        return this.generateCryptoAnalysisResponse(agentPersonality, context, intent);
      
      case 'TRADING_ADVICE':
        return this.generateTradingAdviceResponse(agentPersonality, context, intent);
      
      case 'MARKET_GENERAL':
        return this.generateMarketGeneralResponse(agentPersonality, context);
      
      case 'COMPARISON':
        return this.generateComparisonResponse(agentPersonality, context, intent);
      
      case 'PRICE_PREDICTION':
        return this.generatePricePredictionResponse(agentPersonality, context, intent);
      
      case 'PERSONAL_CHAT':
        return this.generatePersonalChatResponse(agentPersonality, context);
      
      default:
        return this.generateDefaultResponse(agentPersonality, context);
    }
  }
  
  /**
   * Obtém a personalidade do agente
   */
  private static getAgentPersonality(agentType: AgentType) {
    const personalities = {
      ANALYST: {
        name: 'Sarah "The Wolf" Rodriguez',
        traits: 'analítica, precisa, focada em dados',
        expertise: 'análise técnica e fundamental',
        style: 'profissional e detalhada'
      },
      MENTOR: {
        name: 'Marcus "The Sage" Chen',
        traits: 'educativo, paciente, experiente',
        expertise: 'educação e estratégias de longo prazo',
        style: 'didático e encorajador'
      },
      STRATEGIST: {
        name: 'Alex "The Navigator" Kim',
        traits: 'estratégico, visionário, adaptável',
        expertise: 'estratégias de mercado e timing',
        style: 'estratégico e inspirador'
      }
    };
    
    return personalities[agentType];
  }
  
  /**
   * Gera resposta para perguntas sobre o agente
   */
  private static generateAgentInfoResponse(
    personality: any,
    context: ContextAnalysis,
    userMessage: string
  ): ResponseTemplate {
    
    const isAboutAvailability = /\b(trabalha|funciona|disponível|disponivel|24\s*horas?|horário|horario)\b/i.test(userMessage);
    const isAboutCapabilities = /\b(pode|consegue|sabe|especialidades|habilidades|capacidades)\b/i.test(userMessage);
    const isAboutIdentity = /\b(quem\s+)?(você|voce)\s+(é|eh|sou)\b/i.test(userMessage);
    
    let systemPrompt = `Você é ${personality.name}, um agente de IA especializado em criptomoedas. 
Suas características: ${personality.traits}.
Sua expertise: ${personality.expertise}.
Seu estilo: ${personality.style}.

IMPORTANTE: Responda especificamente sobre VOCÊ como agente, não sobre análise de criptomoedas.`;
    
    let userPrompt = '';
    
    if (isAboutAvailability) {
      userPrompt = `O usuário está perguntando sobre sua disponibilidade/horário de funcionamento. 
Responda de forma clara que você está disponível 24/7 como um assistente de IA, 
mantendo sua personalidade de ${personality.name}. 
Seja direto e não desvie para análise de criptomoedas.

Pergunta do usuário: "${userMessage}"`;
    } else if (isAboutCapabilities) {
      userPrompt = `O usuário quer saber sobre suas capacidades e habilidades.
Explique o que você pode fazer como ${personality.name}, suas especialidades em criptomoedas,
e como pode ajudar o usuário. Mantenha o foco em suas capacidades, não em análises específicas.

Pergunta do usuário: "${userMessage}"`;
    } else if (isAboutIdentity) {
      userPrompt = `O usuário quer saber quem você é.
Apresente-se como ${personality.name}, explique sua personalidade (${personality.traits}),
sua área de expertise (${personality.expertise}) e como você pode ajudar.

Pergunta do usuário: "${userMessage}"`;
    } else {
      userPrompt = `O usuário está fazendo uma pergunta sobre você como agente.
Responda de forma apropriada mantendo sua personalidade de ${personality.name}.
Foque na pergunta específica sobre você, não desvie para análise de mercado.

Pergunta do usuário: "${userMessage}"`;
    }
    
    return {
      systemPrompt,
      userPrompt,
      includeMarketData: false,
      includeTechnicalAnalysis: false,
      responseStyle: context.suggestedResponseTone === 'casual' ? 'conversational' : 'supportive'
    };
  }
  
  /**
   * Gera resposta para saudações
   */
  private static generateGreetingResponse(
    personality: any,
    context: ContextAnalysis
  ): ResponseTemplate {
    
    const systemPrompt = `Você é ${personality.name}, um agente de IA especializado em criptomoedas.
Responda à saudação de forma amigável e natural, apresentando-se brevemente e oferecendo ajuda.
Mantenha sua personalidade: ${personality.traits}.`;
    
    const userPrompt = `Responda à saudação do usuário de forma calorosa e profissional.
Apresente-se como ${personality.name} e ofereça sua ajuda com análises de criptomoedas.
Seja ${context.userExpertiseLevel === 'beginner' ? 'mais didático' : 'mais direto'}.`;
    
    return {
      systemPrompt,
      userPrompt,
      includeMarketData: false,
      includeTechnicalAnalysis: false,
      responseStyle: 'conversational'
    };
  }
  
  /**
   * Gera resposta para pedidos de ajuda
   */
  private static generateHelpResponse(
    personality: any,
    context: ContextAnalysis
  ): ResponseTemplate {
    
    const systemPrompt = `Você é ${personality.name}, especialista em criptomoedas.
O usuário precisa de ajuda. Seja ${personality.style} e ofereça orientação clara.
Nível do usuário: ${context.userExpertiseLevel}.`;
    
    const userPrompt = `O usuário está pedindo ajuda. Explique como você pode ajudar,
quais tipos de análises você faz, e como o usuário pode interagir com você.
Adapte sua linguagem para um usuário ${context.userExpertiseLevel}.`;
    
    return {
      systemPrompt,
      userPrompt,
      includeMarketData: false,
      includeTechnicalAnalysis: false,
      responseStyle: 'educational'
    };
  }
  
  /**
   * Gera resposta para análise de criptomoedas
   */
  private static generateCryptoAnalysisResponse(
    personality: any,
    context: ContextAnalysis,
    intent: IntentClassification
  ): ResponseTemplate {
    
    const systemPrompt = `Você é ${personality.name}, especialista em ${personality.expertise}.
Analise as criptomoedas mencionadas: ${intent.entities.join(', ')}.
Seja ${personality.style} e adapte para usuário ${context.userExpertiseLevel}.
${context.topicContinuity ? 'Continue a análise anterior se relevante.' : ''}`;
    
    const userPrompt = `Analise as criptomoedas: ${intent.entities.join(', ')}.
Forneça insights ${personality.expertise.includes('técnica') ? 'técnicos' : 'estratégicos'}.
Considere o contexto: ${context.previousContext}.
Usuário é ${context.userExpertiseLevel}.`;
    
    return {
      systemPrompt,
      userPrompt,
      includeMarketData: true,
      includeTechnicalAnalysis: true,
      responseStyle: 'analytical'
    };
  }
  
  /**
   * Gera resposta para conselhos de trading
   */
  private static generateTradingAdviceResponse(
    personality: any,
    context: ContextAnalysis,
    intent: IntentClassification
  ): ResponseTemplate {
    
    const systemPrompt = `Você é ${personality.name}, especialista em ${personality.expertise}.
Forneça conselhos de trading responsáveis. SEMPRE inclua disclaimers sobre riscos.
Seja ${personality.style} mas cauteloso. Usuário: ${context.userExpertiseLevel}.`;
    
    const userPrompt = `O usuário busca conselhos de trading sobre: ${intent.entities.join(', ')}.
Forneça orientações estratégicas, mas sempre com disclaimers de risco.
Adapte para usuário ${context.userExpertiseLevel}.
${context.userMood === 'urgent' ? 'Usuário parece ansioso - seja tranquilizador.' : ''}`;
    
    return {
      systemPrompt,
      userPrompt,
      includeMarketData: true,
      includeTechnicalAnalysis: true,
      responseStyle: context.userMood === 'urgent' ? 'supportive' : 'analytical'
    };
  }
  
  /**
   * Gera resposta para perguntas gerais sobre mercado
   */
  private static generateMarketGeneralResponse(
    personality: any,
    context: ContextAnalysis
  ): ResponseTemplate {
    
    const systemPrompt = `Você é ${personality.name}, especialista em mercados de criptomoedas.
Forneça uma visão geral do mercado atual. Seja ${personality.style}.
Usuário: ${context.userExpertiseLevel}.`;
    
    const userPrompt = `Forneça uma análise geral do mercado de criptomoedas atual.
Inclua sentimento, tendências principais e fatores relevantes.
Adapte para usuário ${context.userExpertiseLevel}.`;
    
    return {
      systemPrompt,
      userPrompt,
      includeMarketData: true,
      includeTechnicalAnalysis: false,
      responseStyle: 'analytical'
    };
  }
  
  /**
   * Gera resposta para comparações
   */
  private static generateComparisonResponse(
    personality: any,
    context: ContextAnalysis,
    intent: IntentClassification
  ): ResponseTemplate {
    
    const systemPrompt = `Você é ${personality.name}. Compare as criptomoedas mencionadas: ${intent.entities.join(', ')}.
Seja ${personality.style} e objetivo. Usuário: ${context.userExpertiseLevel}.`;
    
    const userPrompt = `Compare as criptomoedas: ${intent.entities.join(', ')}.
Analise prós, contras, casos de uso e potencial.
Seja imparcial e educativo para usuário ${context.userExpertiseLevel}.`;
    
    return {
      systemPrompt,
      userPrompt,
      includeMarketData: true,
      includeTechnicalAnalysis: true,
      responseStyle: 'educational'
    };
  }
  
  /**
   * Gera resposta para previsões de preço
   */
  private static generatePricePredictionResponse(
    personality: any,
    context: ContextAnalysis,
    intent: IntentClassification
  ): ResponseTemplate {
    
    const systemPrompt = `Você é ${personality.name}. CUIDADO: Nunca faça previsões definitivas de preço.
Discuta fatores que podem influenciar preços, mas sempre com disclaimers.
Seja ${personality.style} mas responsável.`;
    
    const userPrompt = `O usuário pergunta sobre previsões de preço para: ${intent.entities.join(', ')}.
Discuta fatores técnicos e fundamentais, mas NÃO faça previsões específicas.
Eduque sobre a imprevisibilidade do mercado.`;
    
    return {
      systemPrompt,
      userPrompt,
      includeMarketData: true,
      includeTechnicalAnalysis: true,
      responseStyle: 'educational'
    };
  }
  
  /**
   * Gera resposta para conversa pessoal
   */
  private static generatePersonalChatResponse(
    personality: any,
    context: ContextAnalysis
  ): ResponseTemplate {
    
    const systemPrompt = `Você é ${personality.name}. O usuário quer conversar de forma mais pessoal.
Seja amigável mas mantenha o foco profissional. Redirecione gentilmente para criptomoedas.`;
    
    const userPrompt = `Responda de forma amigável mas profissional.
Mantenha sua personalidade de ${personality.name} e redirecione para como pode ajudar com criptomoedas.`;
    
    return {
      systemPrompt,
      userPrompt,
      includeMarketData: false,
      includeTechnicalAnalysis: false,
      responseStyle: 'conversational'
    };
  }
  
  /**
   * Gera resposta padrão para casos não identificados
   */
  private static generateDefaultResponse(
    personality: any,
    context: ContextAnalysis
  ): ResponseTemplate {
    
    const systemPrompt = `Você é ${personality.name}. A intenção do usuário não foi clara.
Peça esclarecimentos de forma educada e ofereça opções de como pode ajudar.`;
    
    const userPrompt = `A pergunta não foi clara. Peça esclarecimentos educadamente e
liste as principais formas como você pode ajudar com criptomoedas.`;
    
    return {
      systemPrompt,
      userPrompt,
      includeMarketData: false,
      includeTechnicalAnalysis: false,
      responseStyle: 'supportive'
    };
  }
}
