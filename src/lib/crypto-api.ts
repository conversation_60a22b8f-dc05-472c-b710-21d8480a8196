import axios from 'axios';
import { CryptoData, MarketData, TechnicalIndicators, ApiResponse } from '@/types';

const COINGECKO_BASE_URL = 'https://api.coingecko.com/api/v3';
const FEAR_GREED_API = 'https://api.alternative.me/fng/';

// Cache simples para evitar muitas requisições
const cache = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 60000; // 1 minuto

function getCachedData<T>(key: string): T | null {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data as T;
  }
  return null;
}

function setCachedData(key: string, data: any): void {
  cache.set(key, { data, timestamp: Date.now() });
}

export class CryptoApiService {
  
  /**
   * Busca dados de uma criptomoeda específica
   */
  static async getCryptoData(coinId: string): Promise<ApiResponse<CryptoData>> {
    try {
      const cacheKey = `crypto-${coinId}`;
      const cached = getCachedData<CryptoData>(cacheKey);
      
      if (cached) {
        return {
          success: true,
          data: cached,
          timestamp: new Date()
        };
      }

      const response = await axios.get(
        `${COINGECKO_BASE_URL}/coins/${coinId}`,
        {
          params: {
            localization: false,
            tickers: false,
            market_data: true,
            community_data: false,
            developer_data: false,
            sparkline: false
          }
        }
      );

      const data: CryptoData = {
        id: response.data.id,
        symbol: response.data.symbol,
        name: response.data.name,
        current_price: response.data.market_data.current_price.usd,
        market_cap: response.data.market_data.market_cap.usd,
        market_cap_rank: response.data.market_cap_rank,
        fully_diluted_valuation: response.data.market_data.fully_diluted_valuation?.usd || 0,
        total_volume: response.data.market_data.total_volume.usd,
        high_24h: response.data.market_data.high_24h.usd,
        low_24h: response.data.market_data.low_24h.usd,
        price_change_24h: response.data.market_data.price_change_24h,
        price_change_percentage_24h: response.data.market_data.price_change_percentage_24h,
        market_cap_change_24h: response.data.market_data.market_cap_change_24h,
        market_cap_change_percentage_24h: response.data.market_data.market_cap_change_percentage_24h,
        circulating_supply: response.data.market_data.circulating_supply,
        total_supply: response.data.market_data.total_supply,
        max_supply: response.data.market_data.max_supply,
        ath: response.data.market_data.ath.usd,
        ath_change_percentage: response.data.market_data.ath_change_percentage.usd,
        ath_date: response.data.market_data.ath_date.usd,
        atl: response.data.market_data.atl.usd,
        atl_change_percentage: response.data.market_data.atl_change_percentage.usd,
        atl_date: response.data.market_data.atl_date.usd,
        last_updated: response.data.last_updated
      };

      setCachedData(cacheKey, data);

      return {
        success: true,
        data,
        timestamp: new Date()
      };

    } catch (error) {
      console.error('Erro ao buscar dados da crypto:', error);
      return {
        success: false,
        error: 'Erro ao buscar dados da criptomoeda',
        timestamp: new Date()
      };
    }
  }

  /**
   * Busca dados de múltiplas criptomoedas
   */
  static async getMultipleCryptoData(coinIds: string[]): Promise<ApiResponse<CryptoData[]>> {
    try {
      const cacheKey = `multiple-${coinIds.join(',')}`;
      const cached = getCachedData<CryptoData[]>(cacheKey);
      
      if (cached) {
        return {
          success: true,
          data: cached,
          timestamp: new Date()
        };
      }

      const response = await axios.get(
        `${COINGECKO_BASE_URL}/coins/markets`,
        {
          params: {
            vs_currency: 'usd',
            ids: coinIds.join(','),
            order: 'market_cap_desc',
            per_page: coinIds.length,
            page: 1,
            sparkline: false,
            price_change_percentage: '24h'
          }
        }
      );

      const data: CryptoData[] = response.data.map((coin: any) => ({
        id: coin.id,
        symbol: coin.symbol,
        name: coin.name,
        current_price: coin.current_price,
        market_cap: coin.market_cap,
        market_cap_rank: coin.market_cap_rank,
        fully_diluted_valuation: coin.fully_diluted_valuation || 0,
        total_volume: coin.total_volume,
        high_24h: coin.high_24h,
        low_24h: coin.low_24h,
        price_change_24h: coin.price_change_24h,
        price_change_percentage_24h: coin.price_change_percentage_24h,
        market_cap_change_24h: coin.market_cap_change_24h,
        market_cap_change_percentage_24h: coin.market_cap_change_percentage_24h,
        circulating_supply: coin.circulating_supply,
        total_supply: coin.total_supply,
        max_supply: coin.max_supply,
        ath: coin.ath,
        ath_change_percentage: coin.ath_change_percentage,
        ath_date: coin.ath_date,
        atl: coin.atl,
        atl_change_percentage: coin.atl_change_percentage,
        atl_date: coin.atl_date,
        last_updated: coin.last_updated
      }));

      setCachedData(cacheKey, data);

      return {
        success: true,
        data,
        timestamp: new Date()
      };

    } catch (error) {
      console.error('Erro ao buscar dados de múltiplas cryptos:', error);
      return {
        success: false,
        error: 'Erro ao buscar dados das criptomoedas',
        timestamp: new Date()
      };
    }
  }

  /**
   * Busca dados globais do mercado
   */
  static async getMarketData(): Promise<ApiResponse<MarketData>> {
    try {
      const cacheKey = 'market-data';
      const cached = getCachedData<MarketData>(cacheKey);
      
      if (cached) {
        return {
          success: true,
          data: cached,
          timestamp: new Date()
        };
      }

      const [globalResponse, fearGreedResponse, trendingResponse] = await Promise.all([
        axios.get(`${COINGECKO_BASE_URL}/global`),
        axios.get(FEAR_GREED_API),
        axios.get(`${COINGECKO_BASE_URL}/search/trending`)
      ]);

      const data: MarketData = {
        global: globalResponse.data.data,
        fear_greed_index: fearGreedResponse.data.data[0].value,
        trending_coins: trendingResponse.data.coins.map((coin: any) => coin.item.id)
      };

      setCachedData(cacheKey, data);

      return {
        success: true,
        data,
        timestamp: new Date()
      };

    } catch (error) {
      console.error('Erro ao buscar dados do mercado:', error);
      return {
        success: false,
        error: 'Erro ao buscar dados do mercado',
        timestamp: new Date()
      };
    }
  }

  /**
   * Busca lista de criptomoedas populares
   */
  static async getPopularCoins(limit: number = 10): Promise<ApiResponse<CryptoData[]>> {
    try {
      const response = await axios.get(
        `${COINGECKO_BASE_URL}/coins/markets`,
        {
          params: {
            vs_currency: 'usd',
            order: 'market_cap_desc',
            per_page: limit,
            page: 1,
            sparkline: false,
            price_change_percentage: '24h'
          }
        }
      );

      const data: CryptoData[] = response.data.map((coin: any) => ({
        id: coin.id,
        symbol: coin.symbol,
        name: coin.name,
        current_price: coin.current_price,
        market_cap: coin.market_cap,
        market_cap_rank: coin.market_cap_rank,
        fully_diluted_valuation: coin.fully_diluted_valuation || 0,
        total_volume: coin.total_volume,
        high_24h: coin.high_24h,
        low_24h: coin.low_24h,
        price_change_24h: coin.price_change_24h,
        price_change_percentage_24h: coin.price_change_percentage_24h,
        market_cap_change_24h: coin.market_cap_change_24h,
        market_cap_change_percentage_24h: coin.market_cap_change_percentage_24h,
        circulating_supply: coin.circulating_supply,
        total_supply: coin.total_supply,
        max_supply: coin.max_supply,
        ath: coin.ath,
        ath_change_percentage: coin.ath_change_percentage,
        ath_date: coin.ath_date,
        atl: coin.atl,
        atl_change_percentage: coin.atl_change_percentage,
        atl_date: coin.atl_date,
        last_updated: coin.last_updated
      }));

      return {
        success: true,
        data,
        timestamp: new Date()
      };

    } catch (error) {
      console.error('Erro ao buscar moedas populares:', error);
      return {
        success: false,
        error: 'Erro ao buscar moedas populares',
        timestamp: new Date()
      };
    }
  }
}
