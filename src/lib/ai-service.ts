import { Message, AgentType, ChatContext, CryptoData, TechnicalIndicators, TradingSignal } from '@/types';
import { getAgent } from './agents';
import { CryptoApiService } from './crypto-api';
import { TechnicalAnalysisService } from './technical-analysis';

export class AIService {
  private static context: ChatContext = {
    messages: [],
    activeAgent: 'technical_analyst',
    marketSentiment: 'NEUTRAL'
  };

  /**
   * Processa uma mensagem do usuário e retorna resposta do agente ativo
   */
  static async processMessage(
    userMessage: string,
    agentType?: AgentType
  ): Promise<Message> {
    // Atualiza agente ativo se especificado
    if (agentType) {
      this.context.activeAgent = agentType;
    }

    // Adiciona mensagem do usuário ao contexto
    const userMsg: Message = {
      id: this.generateId(),
      content: userMessage,
      role: 'user',
      timestamp: new Date()
    };
    
    this.context.messages.push(userMsg);

    // Detecta se o usuário está perguntando sobre uma criptomoeda específica
    const cryptoMention = this.detectCryptoMention(userMessage);
    let cryptoData: CryptoData | undefined;
    let technicalAnalysis: TechnicalIndicators | undefined;
    let tradingSignal: TradingSignal | undefined;

    if (cryptoMention) {
      this.context.currentCrypto = cryptoMention;
      
      // Busca dados reais da criptomoeda
      const cryptoResponse = await CryptoApiService.getCryptoData(cryptoMention);
      if (cryptoResponse.success && cryptoResponse.data) {
        cryptoData = cryptoResponse.data;
        
        // Gera análise técnica
        technicalAnalysis = TechnicalAnalysisService.generateTechnicalAnalysis(cryptoData);
        
        // Gera sinal de trading
        tradingSignal = TechnicalAnalysisService.generateTradingSignal(cryptoData, technicalAnalysis);
      }
    }

    // Gera resposta do agente
    const agentResponse = await this.generateAgentResponse(
      userMessage,
      this.context.activeAgent,
      cryptoData,
      technicalAnalysis,
      tradingSignal
    );

    // Cria mensagem de resposta
    const assistantMsg: Message = {
      id: this.generateId(),
      content: agentResponse,
      role: 'assistant',
      agent: this.context.activeAgent,
      timestamp: new Date(),
      cryptoData,
      technicalAnalysis,
      tradingSignal
    };

    this.context.messages.push(assistantMsg);
    this.context.lastAnalysis = new Date();

    return assistantMsg;
  }

  /**
   * Gera resposta do agente baseada no contexto
   */
  private static async generateAgentResponse(
    userMessage: string,
    agentType: AgentType,
    cryptoData?: CryptoData,
    technicalAnalysis?: TechnicalIndicators,
    tradingSignal?: TradingSignal
  ): Promise<string> {
    const agent = getAgent(agentType);
    const isGreeting = this.isGreeting(userMessage);
    const isGeneralQuestion = this.isGeneralMarketQuestion(userMessage);

    // Se é uma saudação
    if (isGreeting) {
      return this.generateGreetingResponse(agent.name, agent.personality);
    }

    // Se tem dados de crypto específica
    if (cryptoData && technicalAnalysis && tradingSignal) {
      return this.generateCryptoAnalysisResponse(
        agentType,
        cryptoData,
        technicalAnalysis,
        tradingSignal,
        userMessage
      );
    }

    // Se é pergunta geral sobre mercado
    if (isGeneralQuestion) {
      return this.generateGeneralMarketResponse(agentType, userMessage);
    }

    // Resposta contextual baseada no agente
    return this.generateContextualResponse(agentType, userMessage);
  }

  /**
   * Detecta menção a criptomoedas na mensagem
   */
  private static detectCryptoMention(message: string): string | null {
    const cryptoPatterns = [
      // Nomes completos
      /bitcoin/i, /ethereum/i, /cardano/i, /solana/i, /polkadot/i,
      /chainlink/i, /litecoin/i, /dogecoin/i, /shiba/i, /avalanche/i,
      
      // Símbolos
      /\bbtc\b/i, /\beth\b/i, /\bada\b/i, /\bsol\b/i, /\bdot\b/i,
      /\blink\b/i, /\bltc\b/i, /\bdoge\b/i, /\bshib\b/i, /\bavax\b/i,
      /\bmatic\b/i, /\bbnb\b/i, /\bxrp\b/i, /\batom\b/i, /\balgo\b/i
    ];

    const cryptoMap: Record<string, string> = {
      'bitcoin': 'bitcoin',
      'btc': 'bitcoin',
      'ethereum': 'ethereum',
      'eth': 'ethereum',
      'cardano': 'cardano',
      'ada': 'cardano',
      'solana': 'solana',
      'sol': 'solana',
      'polkadot': 'polkadot',
      'dot': 'polkadot',
      'chainlink': 'chainlink',
      'link': 'chainlink',
      'litecoin': 'litecoin',
      'ltc': 'litecoin',
      'dogecoin': 'dogecoin',
      'doge': 'dogecoin',
      'shiba': 'shiba-inu',
      'shib': 'shiba-inu',
      'avalanche': 'avalanche-2',
      'avax': 'avalanche-2',
      'matic': 'matic-network',
      'polygon': 'matic-network',
      'bnb': 'binancecoin',
      'binance': 'binancecoin',
      'xrp': 'ripple',
      'ripple': 'ripple',
      'atom': 'cosmos',
      'cosmos': 'cosmos',
      'algo': 'algorand',
      'algorand': 'algorand'
    };

    for (const pattern of cryptoPatterns) {
      const match = message.match(pattern);
      if (match) {
        const found = match[0].toLowerCase();
        return cryptoMap[found] || found;
      }
    }

    return null;
  }

  /**
   * Verifica se é uma saudação
   */
  private static isGreeting(message: string): boolean {
    const greetingPatterns = [
      /^(oi|olá|ola|hey|hi|hello|bom dia|boa tarde|boa noite)/i,
      /^(e aí|eai|como vai|tudo bem|beleza)/i
    ];
    
    return greetingPatterns.some(pattern => pattern.test(message.trim()));
  }

  /**
   * Verifica se é pergunta geral sobre mercado
   */
  private static isGeneralMarketQuestion(message: string): boolean {
    const marketPatterns = [
      /mercado/i, /cripto/i, /crypto/i, /trading/i, /investir/i,
      /comprar/i, /vender/i, /análise/i, /tendência/i, /previsão/i
    ];
    
    return marketPatterns.some(pattern => pattern.test(message));
  }

  /**
   * Gera resposta de saudação
   */
  private static generateGreetingResponse(agentName: string, personality: string): string {
    const greetings = {
      'Dr. Marcus Chen': [
        "Olá! Sou o Dr. Marcus Chen 👨‍💼. Como especialista em análise técnica, estou aqui para ajudá-lo com análises precisas do mercado cripto. Sobre qual criptomoeda gostaria de conversar?",
        "Bom dia! Dr. Marcus Chen aqui 📊. Pronto para mergulhar nos dados e indicadores técnicos? Qual análise posso fazer para você hoje?"
      ],
      'Sarah "The Wolf" Rodriguez': [
        "E aí, parceiro! 🐺 Sarah Rodriguez na área. 15 anos batalhando nos mercados e pronta para compartilhar a real sobre trading. Em que posso ajudar?",
        "Opa! The Wolf aqui 💎. Mercado tá pegando fogo hoje, né? Conta aí, qual crypto tá na sua mira?"
      ],
      'Prof. Elena Nakamura': [
        "Olá! Sou a Prof. Elena Nakamura 👩‍🏫. Vamos explorar juntos o fascinante mundo das criptomoedas? Posso ajudá-lo com análises fundamentais e contexto de mercado.",
        "Bom dia! Prof. Elena aqui 📚. Sempre é um prazer discutir as tendências e o futuro do mercado cripto. O que gostaria de aprender hoje?"
      ]
    };

    const agentGreetings = greetings[agentName as keyof typeof greetings] || [
      "Olá! Como posso ajudá-lo com análises de criptomoedas hoje?"
    ];

    return agentGreetings[Math.floor(Math.random() * agentGreetings.length)];
  }

  /**
   * Gera resposta com análise de criptomoeda
   */
  private static generateCryptoAnalysisResponse(
    agentType: AgentType,
    cryptoData: CryptoData,
    technicalAnalysis: TechnicalIndicators,
    tradingSignal: TradingSignal,
    userMessage: string
  ): string {
    const agent = getAgent(agentType);
    const price = cryptoData.current_price.toLocaleString('pt-BR', { 
      style: 'currency', 
      currency: 'USD' 
    });
    const change24h = cryptoData.price_change_percentage_24h.toFixed(2);
    const changeEmoji = cryptoData.price_change_percentage_24h >= 0 ? '📈' : '📉';

    switch (agentType) {
      case 'technical_analyst':
        return `📊 **Análise Técnica - ${cryptoData.name} (${cryptoData.symbol.toUpperCase()})**

**Preço Atual:** ${price} ${changeEmoji} ${change24h}%

**Indicadores Técnicos:**
• **RSI (14):** ${technicalAnalysis.rsi.toFixed(1)} ${technicalAnalysis.rsi < 30 ? '(Sobrevenda 🟢)' : technicalAnalysis.rsi > 70 ? '(Sobrecompra 🔴)' : '(Neutro 🟡)'}
• **MACD:** ${technicalAnalysis.macd.macd > technicalAnalysis.macd.signal ? 'Bullish 📈' : 'Bearish 📉'}
• **SMA20:** ${technicalAnalysis.sma.sma20.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}
• **Bollinger:** Preço ${cryptoData.current_price < technicalAnalysis.bollinger.lower ? 'abaixo da banda inferior 🟢' : cryptoData.current_price > technicalAnalysis.bollinger.upper ? 'acima da banda superior 🔴' : 'dentro das bandas 🟡'}

**Sinal de Trading:** ${tradingSignal.type} ${tradingSignal.strength} ⚡
**Confiança:** ${tradingSignal.confidence}%
**Target:** ${tradingSignal.price_target.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}
**Stop-Loss:** ${tradingSignal.stop_loss.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}

**Confluência de Sinais:** ${tradingSignal.reasoning}

*Lembre-se: Esta análise é baseada em dados atuais e deve ser combinada com sua própria pesquisa.* 🎯`;

      case 'experienced_trader':
        return `🐺 **The Wolf's Take - ${cryptoData.name}**

Opa, ${price} ${changeEmoji} ${change24h}% nas últimas 24h!

**Minha leitura do mercado:**
${tradingSignal.type === 'BUY' ? '🚀 Tô vendo oportunidade de compra aqui!' : tradingSignal.type === 'SELL' ? '⚠️ Hora de tomar lucro ou sair fora!' : '🤔 Melhor ficar de fora por enquanto.'}

**Setup que eu faria:**
• **Entrada:** ${tradingSignal.type === 'BUY' ? 'Compra' : tradingSignal.type === 'SELL' ? 'Venda' : 'Aguardar'} com ${tradingSignal.confidence}% de confiança
• **Target:** ${tradingSignal.price_target.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}
• **Stop:** ${tradingSignal.stop_loss.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })} (SEMPRE use stop-loss! 💎)

**Volume:** ${(cryptoData.total_volume / 1000000).toFixed(0)}M - ${cryptoData.total_volume > cryptoData.market_cap * 0.1 ? 'Volume forte 💪' : 'Volume fraco 😴'}

**Dica da Wolf:** ${technicalAnalysis.rsi < 30 ? 'RSI em oversold, pode ser uma boa entrada se você tem estômago!' : technicalAnalysis.rsi > 70 ? 'Cuidado com o RSI em overbought, pode corrigir!' : 'RSI neutro, aguarde confirmação!'}

*Risk management é tudo, parceiro! Nunca aposte mais do que pode perder.* 🔥`;

      case 'market_consultant':
        return `👩‍🏫 **Análise Fundamentalista - ${cryptoData.name}**

**Contexto de Mercado:**
${cryptoData.name} está cotado a ${price}, com variação de ${change24h}% em 24h.

**Posição no Mercado:**
• **Ranking:** #${cryptoData.market_cap_rank} por market cap
• **Market Cap:** $${(cryptoData.market_cap / 1000000000).toFixed(2)}B
• **Volume 24h:** $${(cryptoData.total_volume / 1000000).toFixed(0)}M
• **Supply:** ${(cryptoData.circulating_supply / 1000000).toFixed(0)}M ${cryptoData.symbol.toUpperCase()}

**Análise Fundamentalista:**
${this.getFundamentalAnalysis(cryptoData)}

**Perspectiva de Longo Prazo:**
${tradingSignal.type === 'BUY' ? '🌱 Os fundamentos sugerem potencial de crescimento' : tradingSignal.type === 'SELL' ? '⚠️ Alguns indicadores fundamentais preocupam' : '📊 Fundamentos mistos, aguardar mais clareza'}

**Fatores a Observar:**
• Adoção institucional e parcerias
• Desenvolvimentos tecnológicos
• Regulamentações governamentais
• Sentimento geral do mercado cripto

*A análise fundamentalista complementa a técnica para decisões mais informadas.* 💡`;

      default:
        return "Análise não disponível para este agente.";
    }
  }

  /**
   * Gera análise fundamentalista básica
   */
  private static getFundamentalAnalysis(cryptoData: CryptoData): string {
    const analyses = [];
    
    if (cryptoData.market_cap_rank <= 10) {
      analyses.push("Projeto consolidado entre os top 10");
    }
    
    if (cryptoData.price_change_percentage_24h > 10) {
      analyses.push("Alta volatilidade recente pode indicar interesse especulativo");
    }
    
    if (cryptoData.total_volume > cryptoData.market_cap * 0.1) {
      analyses.push("Alto volume de negociação indica liquidez saudável");
    }
    
    return analyses.join('. ') || "Projeto em desenvolvimento com métricas estáveis";
  }

  /**
   * Gera resposta para perguntas gerais sobre mercado
   */
  private static generateGeneralMarketResponse(agentType: AgentType, userMessage: string): string {
    // Implementação simplificada - em produção, integraria com API de IA
    const responses = {
      'technical_analyst': "Como analista técnico, recomendo sempre basear decisões em dados concretos. Sobre qual criptomoeda específica gostaria de uma análise detalhada? 📊",
      'experienced_trader': "No trading, experiência vale ouro! Qual crypto tá na sua mira? Posso dar umas dicas baseadas no que já vi por aí 🐺",
      'market_consultant': "O mercado cripto está em constante evolução. Para uma análise mais precisa, me diga sobre qual projeto específico gostaria de conversar 👩‍🏫"
    };
    
    return responses[agentType];
  }

  /**
   * Gera resposta contextual
   */
  private static generateContextualResponse(agentType: AgentType, userMessage: string): string {
    const agent = getAgent(agentType);
    return `Como ${agent.name}, posso ajudá-lo com ${agent.specialties.join(', ')}. Sobre qual criptomoeda específica gostaria de conversar? ${agent.avatar}`;
  }

  /**
   * Gera ID único
   */
  private static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * Obtém contexto atual
   */
  static getContext(): ChatContext {
    return { ...this.context };
  }

  /**
   * Limpa contexto
   */
  static clearContext(): void {
    this.context = {
      messages: [],
      activeAgent: 'technical_analyst',
      marketSentiment: 'NEUTRAL'
    };
  }

  /**
   * Troca agente ativo
   */
  static switchAgent(agentType: AgentType): void {
    this.context.activeAgent = agentType;
  }
}
