import { Message, AgentType, ChatContext, CryptoData, TechnicalIndicators, TradingSignal } from '@/types';
import { getAgent } from './agents';
import { CryptoApiService } from './crypto-api';
import { TechnicalAnalysisService } from './technical-analysis';
import { IntentClassifier, IntentClassification } from './intent-classifier';
import { ContextAnalyzer, ContextAnalysis } from './context-analyzer';
import { IntelligentResponseGenerator, ResponseTemplate } from './intelligent-response-generator';

export class AIService {
  private static context: ChatContext = {
    messages: [],
    activeAgent: 'ANALYST',
    marketSentiment: 'NEUTRAL'
  };

  /**
   * Processa uma mensagem do usuário e retorna resposta do agente ativo
   */
  static async processMessage(
    userMessage: string,
    agentType?: AgentType
  ): Promise<Message> {
    // Atualiza agente ativo se especificado
    if (agentType) {
      this.context.activeAgent = agentType;
    }

    // Adiciona mensagem do usuário ao contexto
    const userMsg: Message = {
      id: this.generateId(),
      content: userMessage,
      role: 'user',
      timestamp: new Date()
    };

    this.context.messages.push(userMsg);

    // NOVA INTELIGÊNCIA: Classifica a intenção da mensagem
    const intentClassification = IntentClassifier.classifyIntent(
      userMessage,
      this.context.messages.slice(0, -1) // Histórico sem a mensagem atual
    );

    // NOVA INTELIGÊNCIA: Analisa o contexto da conversa
    const contextAnalysis = ContextAnalyzer.analyzeContext(
      userMessage,
      this.context.messages.slice(0, -1), // Histórico sem a mensagem atual
      intentClassification,
      this.context.activeAgent
    );

    // Busca dados de crypto se necessário
    let cryptoData: CryptoData | undefined;
    let technicalAnalysis: TechnicalIndicators | undefined;
    let tradingSignal: TradingSignal | undefined;

    // Só busca dados de crypto se a intenção requer
    if (this.shouldFetchCryptoData(intentClassification)) {
      const cryptoEntities = intentClassification.entities;

      if (cryptoEntities.length > 0) {
        this.context.currentCrypto = cryptoEntities[0]; // Usa a primeira crypto mencionada

        // Busca dados reais da criptomoeda
        const cryptoResponse = await CryptoApiService.getCryptoData(cryptoEntities[0]);
        if (cryptoResponse.success && cryptoResponse.data) {
          cryptoData = cryptoResponse.data;

          // Gera análise técnica
          technicalAnalysis = TechnicalAnalysisService.generateTechnicalAnalysis(cryptoData);

          // Gera sinal de trading
          tradingSignal = TechnicalAnalysisService.generateTradingSignal(cryptoData, technicalAnalysis);
        }
      }
    }

    // NOVA INTELIGÊNCIA: Gera resposta inteligente baseada na intenção e contexto
    const agentResponse = await this.generateIntelligentResponse(
      userMessage,
      intentClassification,
      contextAnalysis,
      this.context.activeAgent,
      cryptoData,
      technicalAnalysis,
      tradingSignal
    );

    // Cria mensagem de resposta
    const assistantMsg: Message = {
      id: this.generateId(),
      content: agentResponse,
      role: 'assistant',
      agentType: this.context.activeAgent,
      timestamp: new Date(),
      cryptoData,
      technicalAnalysis,
      tradingSignal
    };

    this.context.messages.push(assistantMsg);
    this.context.lastAnalysis = new Date();

    return assistantMsg;
  }

  /**
   * Determina se deve buscar dados de criptomoeda baseado na intenção
   */
  private static shouldFetchCryptoData(intent: IntentClassification): boolean {
    const cryptoIntents = [
      'CRYPTO_ANALYSIS',
      'TRADING_ADVICE',
      'COMPARISON',
      'PRICE_PREDICTION'
    ];

    return cryptoIntents.includes(intent.intent) && intent.entities.length > 0;
  }

  /**
   * NOVA INTELIGÊNCIA: Gera resposta inteligente baseada na intenção e contexto
   */
  private static async generateIntelligentResponse(
    userMessage: string,
    intent: IntentClassification,
    context: ContextAnalysis,
    agentType: AgentType,
    cryptoData?: CryptoData,
    technicalAnalysis?: TechnicalIndicators,
    tradingSignal?: TradingSignal
  ): Promise<string> {

    // Gera template de resposta baseado na intenção e contexto
    const responseTemplate = IntelligentResponseGenerator.generateResponseTemplate(
      intent,
      context,
      agentType,
      userMessage
    );

    // Para respostas que precisam de dados de mercado
    if (responseTemplate.includeMarketData && cryptoData && technicalAnalysis && tradingSignal) {
      return this.generateEnhancedCryptoResponse(
        responseTemplate,
        cryptoData,
        technicalAnalysis,
        tradingSignal,
        context
      );
    }

    // Para respostas simples (saudações, info do agente, etc.)
    if (intent.intent === 'AGENT_INFO') {
      return this.generateAgentInfoResponse(userMessage, agentType, context);
    }

    return this.generateSimpleResponse(responseTemplate, context, agentType);
  }

  /**
   * Gera resposta específica para perguntas sobre o agente
   */
  private static generateAgentInfoResponse(
    userMessage: string,
    agentType: AgentType,
    context: ContextAnalysis
  ): string {
    const agentNames = {
      'ANALYST': 'Sarah "The Wolf" Rodriguez',
      'MENTOR': 'Marcus "The Sage" Chen',
      'STRATEGIST': 'Alex "The Navigator" Kim'
    };

    const agentName = agentNames[agentType] || 'seu assistente de criptomoedas';

    // Detecta tipo específico de pergunta sobre o agente
    const isAboutAvailability = /\b(trabalha|funciona|disponível|disponivel|24\s*horas?|horário|horario)\b/i.test(userMessage);
    const isAboutCapabilities = /\b(pode|consegue|sabe|especialidades|habilidades|capacidades)\b/i.test(userMessage);
    const isAboutIdentity = /\b(quem\s+)?(você|voce)\s+(é|eh|sou)\b/i.test(userMessage);

    if (isAboutAvailability) {
      return `Olá! Sou ${agentName} 🤖

Sim, estou disponível **24 horas por dia, 7 dias por semana**! Como um assistente de IA, não preciso dormir ou descansar.

Estou sempre aqui para ajudá-lo com:
• Análises de criptomoedas em tempo real
• Estratégias de trading e investimento
• Educação sobre blockchain e DeFi
• Acompanhamento de mercado

Sobre qual criptomoeda gostaria de conversar agora? 🚀`;
    }

    if (isAboutCapabilities) {
      const capabilities = {
        'ANALYST': `Como Sarah "The Wolf" Rodriguez, minhas especialidades incluem:

🔍 **Análise Técnica Avançada**
• RSI, MACD, Bollinger Bands, Fibonacci
• Identificação de padrões de candlestick
• Análise de volume e momentum

⚡ **Gestão de Risco**
• Cálculo de stop-loss e take-profit
• Estratégias de money management
• Análise de confluências

📊 **Sinais de Trading**
• Identificação de pontos de entrada/saída
• Avaliação de força de tendências
• Timing de mercado`,

        'MENTOR': `Como Marcus "The Sage" Chen, posso educá-lo sobre:

📚 **Fundamentos Blockchain**
• Como funcionam diferentes protocolos
• Casos de uso e utilidade real
• Análise fundamentalista de projetos

🌱 **Estratégias de Investimento**
• Construção de portfólio diversificado
• DCA e estratégias de longo prazo
• Avaliação de risco/retorno

🧠 **Educação Financeira**
• Psicologia do investidor
• Planejamento financeiro
• Gestão emocional no trading`,

        'STRATEGIST': `Como Alex "The Navigator" Kim, ofereço:

🧭 **Visão Estratégica**
• Análise de tendências macro
• Identificação de narrativas emergentes
• Timing de ciclos de mercado

⚡ **Adaptação Tática**
• Rebalanceamento de portfólio
• Aproveitamento de volatilidade
• Estratégias multi-timeframe

🎯 **Planejamento**
• Cenários de mercado
• Correlações entre ativos
• Oportunidades de arbitragem`
      };

      return capabilities[agentType] || `Como ${agentName}, posso ajudá-lo com análises completas de criptomoedas!`;
    }

    if (isAboutIdentity) {
      const identities = {
        'ANALYST': `Sou Sarah "The Wolf" Rodriguez 🐺

**Minha História:**
15 anos navegando pelos mercados financeiros, especializada em análise técnica e gestão de risco. Ganhei o apelido "The Wolf" pela minha capacidade de farejar oportunidades no mercado cripto.

**Minha Filosofia:**
"Dados não mentem, mas o mercado pode surpreender." Sempre uso análise baseada em evidências, mas nunca esqueço que o mercado é imprevisível.

**Como Posso Ajudar:**
Transformo dados complexos em insights acionáveis para suas decisões de trading.`,

        'MENTOR': `Sou Marcus "The Sage" Chen 👨‍💼

**Minha Missão:**
Democratizar o conhecimento sobre criptomoedas e blockchain. Acredito que educação é o melhor investimento.

**Minha Abordagem:**
Ensino de forma didática e paciente, adaptando a linguagem ao nível de cada pessoa. Do iniciante ao avançado.

**Minha Paixão:**
Ver pessoas tomarem decisões financeiras mais inteligentes através do conhecimento sólido.`,

        'STRATEGIST': `Sou Alex "The Navigator" Kim 🧭

**Minha Expertise:**
Navegação estratégica em mercados voláteis. Especialista em identificar oportunidades onde outros veem apenas caos.

**Minha Metodologia:**
Combino análise macro com táticas adaptáveis. O mercado muda, nossa estratégia também deve mudar.

**Meu Diferencial:**
Visão de longo prazo com execução de curto prazo. Estratégia é sobre timing e adaptação.`
      };

      return identities[agentType] || `Sou ${agentName}, seu assistente especializado em criptomoedas!`;
    }

    // Resposta geral sobre o agente
    return `Sou ${agentName}, seu assistente de IA especializado em criptomoedas!

Estou aqui 24/7 para ajudá-lo com análises, estratégias e educação sobre o mercado cripto.

O que gostaria de saber? 🚀`;
  }

  /**
   * Gera resposta simples baseada no template
   */
  private static generateSimpleResponse(
    template: ResponseTemplate,
    context: ContextAnalysis,
    agentType: AgentType
  ): string {
    const agentInfo = { type: agentType };

    // Respostas específicas baseadas no template
    switch (template.responseStyle) {
      case 'conversational':
        return this.generateConversationalResponse(agentInfo, template, context);
      case 'educational':
        return this.generateEducationalResponse(agentInfo, template, context);
      case 'supportive':
        return this.generateSupportiveResponse(agentInfo, template, context);
      default:
        return this.generateDefaultAgentResponse(agentInfo, template, context);
    }
  }

  /**
   * Gera resposta conversacional
   */
  private static generateConversationalResponse(
    agent: any,
    template: ResponseTemplate,
    context: ContextAnalysis
  ): string {
    const greetings = {
      'ANALYST': [
        `Olá! Sou Sarah "The Wolf" Rodriguez 🐺. Como posso ajudá-lo com análises de criptomoedas hoje?`,
        `E aí! Sarah aqui, pronta para mergulhar nos dados do mercado cripto. Em que posso ajudar?`
      ],
      'MENTOR': [
        `Olá! Sou Marcus "The Sage" Chen 👨‍💼. Vamos explorar o mundo das criptomoedas juntos?`,
        `Bom dia! Prof. Marcus aqui, pronto para compartilhar conhecimento sobre o mercado cripto.`
      ],
      'STRATEGIST': [
        `Opa! Alex "The Navigator" Kim aqui 🧭. Pronto para navegar pelas oportunidades do mercado?`,
        `Olá! Alex aqui, vamos traçar estratégias inteligentes para o mercado cripto?`
      ]
    };

    const agentGreetings = greetings[agent.type] || greetings['ANALYST'];

    return agentGreetings[Math.floor(Math.random() * agentGreetings.length)];
  }

  /**
   * Gera resposta educacional
   */
  private static generateEducationalResponse(
    agent: any,
    template: ResponseTemplate,
    context: ContextAnalysis
  ): string {
    const helpResponses = {
      'ANALYST': `Como Sarah "The Wolf" Rodriguez, posso ajudá-lo com:

📊 **Análises Técnicas Detalhadas**
• Indicadores como RSI, MACD, Bollinger Bands
• Identificação de padrões de preço
• Sinais de entrada e saída

🎯 **Estratégias de Trading**
• Gestão de risco e money management
• Definição de stop-loss e take-profit
• Análise de confluências

💡 **Dicas Práticas**
• Como interpretar gráficos
• Timing de mercado
• Psicologia do trading

Sobre qual criptomoeda gostaria de uma análise?`,

      'MENTOR': `Como Marcus "The Sage" Chen, estou aqui para educá-lo sobre:

📚 **Fundamentos das Criptomoedas**
• Como funcionam as diferentes blockchains
• Casos de uso e utilidade dos tokens
• Análise fundamentalista

🌱 **Estratégias de Longo Prazo**
• Investimento vs Trading
• Diversificação de portfólio
• DCA (Dollar Cost Averaging)

🧠 **Educação Financeira**
• Gestão de risco
• Planejamento de investimentos
• Psicologia do investidor

Qual tópico gostaria de explorar primeiro?`,

      'STRATEGIST': `Como Alex "The Navigator" Kim, posso orientá-lo em:

🧭 **Estratégias de Mercado**
• Análise de tendências macro
• Timing de ciclos de mercado
• Oportunidades emergentes

⚡ **Adaptação Rápida**
• Como reagir a mudanças do mercado
• Rebalanceamento de portfólio
• Aproveitamento de volatilidade

🎯 **Visão Estratégica**
• Identificação de narrativas
• Análise de correlações
• Planejamento de cenários

Que estratégia podemos desenvolver juntos?`
    };

    return helpResponses[agent.type] || helpResponses['ANALYST'];
  }

  /**
   * Gera resposta de suporte
   */
  private static generateSupportiveResponse(
    agent: any,
    template: ResponseTemplate,
    context: ContextAnalysis
  ): string {
    const agentNames = {
      'ANALYST': 'Sarah "The Wolf" Rodriguez',
      'MENTOR': 'Marcus "The Sage" Chen',
      'STRATEGIST': 'Alex "The Navigator" Kim'
    };

    const agentName = agentNames[agent.type] || agent.name || 'seu assistente de criptomoedas';

    if (context.userMood === 'negative' || context.userMood === 'urgent') {
      return `Entendo sua preocupação. Como ${agentName}, estou aqui para ajudá-lo de forma clara e objetiva.

${context.userMood === 'urgent' ? '⚡ Vamos direto ao ponto:' : '🤝 Vamos resolver isso juntos:'}

Sobre qual criptomoeda específica você gostaria de uma análise? Ou prefere uma visão geral do mercado atual?

*Lembre-se: No mercado cripto, paciência e análise são fundamentais.* 💎`;
    }

    return `Como ${agentName}, estou aqui para ajudá-lo da melhor forma possível.

Posso fornecer análises detalhadas, explicar conceitos ou ajudar com estratégias. O que seria mais útil para você agora?`;
  }

  /**
   * Gera resposta padrão do agente
   */
  private static generateDefaultAgentResponse(
    agent: any,
    template: ResponseTemplate,
    context: ContextAnalysis
  ): string {
    const agentNames = {
      'ANALYST': 'Sarah "The Wolf" Rodriguez',
      'MENTOR': 'Marcus "The Sage" Chen',
      'STRATEGIST': 'Alex "The Navigator" Kim'
    };

    const agentName = agentNames[agent.type] || 'seu assistente de criptomoedas';

    return `Como ${agentName}, especialista em análise de criptomoedas, estou aqui para ajudá-lo.

Sobre qual criptomoeda específica gostaria de conversar?`;
  }

  /**
   * Gera resposta aprimorada com dados de crypto
   */
  private static generateEnhancedCryptoResponse(
    template: ResponseTemplate,
    cryptoData: CryptoData,
    technicalAnalysis: TechnicalIndicators,
    tradingSignal: TradingSignal,
    context: ContextAnalysis
  ): string {
    // Usa a resposta existente como base, mas pode ser melhorada
    return this.generateCryptoAnalysisResponse(
      context.suggestedResponseTone === 'professional' ? 'ANALYST' :
      context.suggestedResponseTone === 'educational' ? 'MENTOR' : 'STRATEGIST',
      cryptoData,
      technicalAnalysis,
      tradingSignal,
      template.userPrompt
    );
  }

  /**
   * Detecta menção a criptomoedas na mensagem
   */
  private static detectCryptoMention(message: string): string | null {
    const cryptoPatterns = [
      // Nomes completos
      /bitcoin/i, /ethereum/i, /cardano/i, /solana/i, /polkadot/i,
      /chainlink/i, /litecoin/i, /dogecoin/i, /shiba/i, /avalanche/i,
      
      // Símbolos
      /\bbtc\b/i, /\beth\b/i, /\bada\b/i, /\bsol\b/i, /\bdot\b/i,
      /\blink\b/i, /\bltc\b/i, /\bdoge\b/i, /\bshib\b/i, /\bavax\b/i,
      /\bmatic\b/i, /\bbnb\b/i, /\bxrp\b/i, /\batom\b/i, /\balgo\b/i
    ];

    const cryptoMap: Record<string, string> = {
      'bitcoin': 'bitcoin',
      'btc': 'bitcoin',
      'ethereum': 'ethereum',
      'eth': 'ethereum',
      'cardano': 'cardano',
      'ada': 'cardano',
      'solana': 'solana',
      'sol': 'solana',
      'polkadot': 'polkadot',
      'dot': 'polkadot',
      'chainlink': 'chainlink',
      'link': 'chainlink',
      'litecoin': 'litecoin',
      'ltc': 'litecoin',
      'dogecoin': 'dogecoin',
      'doge': 'dogecoin',
      'shiba': 'shiba-inu',
      'shib': 'shiba-inu',
      'avalanche': 'avalanche-2',
      'avax': 'avalanche-2',
      'matic': 'matic-network',
      'polygon': 'matic-network',
      'bnb': 'binancecoin',
      'binance': 'binancecoin',
      'xrp': 'ripple',
      'ripple': 'ripple',
      'atom': 'cosmos',
      'cosmos': 'cosmos',
      'algo': 'algorand',
      'algorand': 'algorand'
    };

    for (const pattern of cryptoPatterns) {
      const match = message.match(pattern);
      if (match) {
        const found = match[0].toLowerCase();
        return cryptoMap[found] || found;
      }
    }

    return null;
  }

  /**
   * Verifica se é uma saudação
   */
  private static isGreeting(message: string): boolean {
    const greetingPatterns = [
      /^(oi|olá|ola|hey|hi|hello|bom dia|boa tarde|boa noite)/i,
      /^(e aí|eai|como vai|tudo bem|beleza)/i
    ];
    
    return greetingPatterns.some(pattern => pattern.test(message.trim()));
  }

  /**
   * Verifica se é pergunta geral sobre mercado
   */
  private static isGeneralMarketQuestion(message: string): boolean {
    const marketPatterns = [
      /mercado/i, /cripto/i, /crypto/i, /trading/i, /investir/i,
      /comprar/i, /vender/i, /análise/i, /tendência/i, /previsão/i
    ];
    
    return marketPatterns.some(pattern => pattern.test(message));
  }

  /**
   * Gera resposta de saudação
   */
  private static generateGreetingResponse(agentName: string, personality: string): string {
    const greetings = {
      'Dr. Marcus Chen': [
        "Olá! Sou o Dr. Marcus Chen 👨‍💼. Como especialista em análise técnica, estou aqui para ajudá-lo com análises precisas do mercado cripto. Sobre qual criptomoeda gostaria de conversar?",
        "Bom dia! Dr. Marcus Chen aqui 📊. Pronto para mergulhar nos dados e indicadores técnicos? Qual análise posso fazer para você hoje?"
      ],
      'Sarah "The Wolf" Rodriguez': [
        "E aí, parceiro! 🐺 Sarah Rodriguez na área. 15 anos batalhando nos mercados e pronta para compartilhar a real sobre trading. Em que posso ajudar?",
        "Opa! The Wolf aqui 💎. Mercado tá pegando fogo hoje, né? Conta aí, qual crypto tá na sua mira?"
      ],
      'Prof. Elena Nakamura': [
        "Olá! Sou a Prof. Elena Nakamura 👩‍🏫. Vamos explorar juntos o fascinante mundo das criptomoedas? Posso ajudá-lo com análises fundamentais e contexto de mercado.",
        "Bom dia! Prof. Elena aqui 📚. Sempre é um prazer discutir as tendências e o futuro do mercado cripto. O que gostaria de aprender hoje?"
      ]
    };

    const agentGreetings = greetings[agentName as keyof typeof greetings] || [
      "Olá! Como posso ajudá-lo com análises de criptomoedas hoje?"
    ];

    return agentGreetings[Math.floor(Math.random() * agentGreetings.length)];
  }

  /**
   * Gera resposta com análise de criptomoeda
   */
  private static generateCryptoAnalysisResponse(
    agentType: AgentType,
    cryptoData: CryptoData,
    technicalAnalysis: TechnicalIndicators,
    tradingSignal: TradingSignal,
    userMessage: string
  ): string {
    const agent = getAgent(agentType);
    const price = cryptoData.current_price.toLocaleString('pt-BR', { 
      style: 'currency', 
      currency: 'USD' 
    });
    const change24h = cryptoData.price_change_percentage_24h.toFixed(2);
    const changeEmoji = cryptoData.price_change_percentage_24h >= 0 ? '📈' : '📉';

    switch (agentType) {
      case 'ANALYST':
      case 'technical_analyst':
        return `📊 **Sarah "The Wolf" Rodriguez - Análise Técnica**
**${cryptoData.name} (${cryptoData.symbol.toUpperCase()})**

**Preço Atual:** ${price} ${changeEmoji} ${change24h}%

**Indicadores Técnicos:**
• **RSI (14):** ${technicalAnalysis.rsi.toFixed(1)} ${technicalAnalysis.rsi < 30 ? '(Sobrevenda 🟢)' : technicalAnalysis.rsi > 70 ? '(Sobrecompra 🔴)' : '(Neutro 🟡)'}
• **MACD:** ${technicalAnalysis.macd.macd > technicalAnalysis.macd.signal ? 'Bullish 📈' : 'Bearish 📉'}
• **SMA20:** ${technicalAnalysis.sma.sma20.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}
• **Bollinger:** Preço ${cryptoData.current_price < technicalAnalysis.bollinger.lower ? 'abaixo da banda inferior 🟢' : cryptoData.current_price > technicalAnalysis.bollinger.upper ? 'acima da banda superior 🔴' : 'dentro das bandas 🟡'}

**Sinal de Trading:** ${tradingSignal.type} ${tradingSignal.strength} ⚡
**Confiança:** ${tradingSignal.confidence}%
**Target:** ${tradingSignal.price_target.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}
**Stop-Loss:** ${tradingSignal.stop_loss.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}

**Confluência de Sinais:** ${tradingSignal.reasoning}

*Como sempre digo: dados não mentem, mas o mercado pode surpreender. Use stop-loss!* 🐺`;

      case 'MENTOR':
      case 'market_consultant':
        return `👨‍💼 **Marcus "The Sage" Chen - Análise Educativa**
**${cryptoData.name} (${cryptoData.symbol.toUpperCase()})**

**Contexto Educativo:**
${cryptoData.name} está cotado a ${price}, com variação de ${change24h}% em 24h.

**Posição no Mercado:**
• **Ranking:** #${cryptoData.market_cap_rank} por market cap
• **Market Cap:** $${(cryptoData.market_cap / 1000000000).toFixed(2)}B
• **Volume 24h:** $${(cryptoData.total_volume / 1000000).toFixed(0)}M
• **Supply:** ${(cryptoData.circulating_supply / 1000000).toFixed(0)}M ${cryptoData.symbol.toUpperCase()}

**Análise Fundamentalista:**
${this.getFundamentalAnalysis(cryptoData)}

**Lição de Hoje:**
${tradingSignal.type === 'BUY' ? '🌱 Os fundamentos sugerem potencial, mas lembre-se: investimento é maratona, não sprint' : tradingSignal.type === 'SELL' ? '⚠️ Alguns indicadores preocupam - hora de revisar sua tese de investimento' : '📊 Fundamentos mistos - paciência é virtude no mercado cripto'}

**Fatores Educativos a Observar:**
• Utilidade real do projeto
• Equipe e parcerias
• Roadmap e desenvolvimento
• Adoção e comunidade

*Lembre-se: conhecimento é o melhor investimento. Estude antes de investir.* 📚`;

      case 'STRATEGIST':
      case 'experienced_trader':
        return `🧭 **Alex "The Navigator" Kim - Visão Estratégica**
**${cryptoData.name} (${cryptoData.symbol.toUpperCase()})**

**Situação Atual:** ${price} ${changeEmoji} ${change24h}%

**Leitura Estratégica:**
${tradingSignal.type === 'BUY' ? '🚀 Vejo oportunidade tática aqui - momento pode ser favorável' : tradingSignal.type === 'SELL' ? '⚠️ Sinais sugerem cautela - hora de reavaliar posição' : '🤔 Cenário misto - aguardar melhor definição'}

**Setup Estratégico:**
• **Ação:** ${tradingSignal.type === 'BUY' ? 'Acumulação gradual' : tradingSignal.type === 'SELL' ? 'Redução de exposição' : 'Observação ativa'}
• **Confiança:** ${tradingSignal.confidence}%
• **Target:** ${tradingSignal.price_target.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}
• **Proteção:** ${tradingSignal.stop_loss.toLocaleString('pt-BR', { style: 'currency', currency: 'USD' })}

**Volume:** ${(cryptoData.total_volume / 1000000).toFixed(0)}M - ${cryptoData.total_volume > cryptoData.market_cap * 0.1 ? 'Liquidez forte 💪' : 'Liquidez limitada 😴'}

**Navegação Tática:** ${technicalAnalysis.rsi < 30 ? 'RSI oversold - oportunidade para posicionamento' : technicalAnalysis.rsi > 70 ? 'RSI overbought - cautela recomendada' : 'RSI neutro - aguardar confirmação direcional'}

*Estratégia é adaptação constante. Mantenha-se flexível e disciplinado.* ⚡`;

      default:
        return "Análise não disponível para este agente.";
    }
  }

  /**
   * Gera análise fundamentalista básica
   */
  private static getFundamentalAnalysis(cryptoData: CryptoData): string {
    const analyses = [];
    
    if (cryptoData.market_cap_rank <= 10) {
      analyses.push("Projeto consolidado entre os top 10");
    }
    
    if (cryptoData.price_change_percentage_24h > 10) {
      analyses.push("Alta volatilidade recente pode indicar interesse especulativo");
    }
    
    if (cryptoData.total_volume > cryptoData.market_cap * 0.1) {
      analyses.push("Alto volume de negociação indica liquidez saudável");
    }
    
    return analyses.join('. ') || "Projeto em desenvolvimento com métricas estáveis";
  }

  /**
   * Gera resposta para perguntas gerais sobre mercado
   */
  private static generateGeneralMarketResponse(agentType: AgentType, userMessage: string): string {
    const responses = {
      'ANALYST': "Como Sarah 'The Wolf' Rodriguez, sempre baseio minhas análises em dados concretos. Sobre qual criptomoeda específica gostaria de uma análise técnica detalhada? 📊🐺",
      'technical_analyst': "Como Sarah 'The Wolf' Rodriguez, sempre baseio minhas análises em dados concretos. Sobre qual criptomoeda específica gostaria de uma análise técnica detalhada? 📊🐺",
      'MENTOR': "Como Marcus 'The Sage' Chen, acredito que educação é fundamental. Sobre qual projeto cripto gostaria de aprender mais? Posso explicar os fundamentos! 👨‍💼📚",
      'market_consultant': "Como Marcus 'The Sage' Chen, acredito que educação é fundamental. Sobre qual projeto cripto gostaria de aprender mais? Posso explicar os fundamentos! 👨‍💼📚",
      'STRATEGIST': "Como Alex 'The Navigator' Kim, vejo o mercado cripto em constante evolução. Qual oportunidade estratégica podemos explorar juntos? 🧭⚡",
      'experienced_trader': "Como Alex 'The Navigator' Kim, vejo o mercado cripto em constante evolução. Qual oportunidade estratégica podemos explorar juntos? 🧭⚡"
    };

    return responses[agentType] || "Como posso ajudá-lo com análises de criptomoedas hoje?";
  }

  /**
   * Gera resposta contextual
   */
  private static generateContextualResponse(agentType: AgentType, userMessage: string): string {
    const responses = {
      'ANALYST': `Como Sarah "The Wolf" Rodriguez 🐺, especialista em análise técnica e gestão de risco, posso ajudá-lo com análises precisas baseadas em dados. Sobre qual criptomoeda específica gostaria de conversar?`,
      'technical_analyst': `Como Sarah "The Wolf" Rodriguez 🐺, especialista em análise técnica e gestão de risco, posso ajudá-lo com análises precisas baseadas em dados. Sobre qual criptomoeda específica gostaria de conversar?`,
      'MENTOR': `Como Marcus "The Sage" Chen 👨‍💼, especialista em educação e estratégias de longo prazo, posso ajudá-lo a entender melhor o mercado cripto. Qual tópico gostaria de explorar?`,
      'market_consultant': `Como Marcus "The Sage" Chen 👨‍💼, especialista em educação e estratégias de longo prazo, posso ajudá-lo a entender melhor o mercado cripto. Qual tópico gostaria de explorar?`,
      'STRATEGIST': `Como Alex "The Navigator" Kim 🧭, especialista em estratégias de mercado e timing, posso ajudá-lo a navegar pelas oportunidades do mercado cripto. Que estratégia podemos desenvolver?`,
      'experienced_trader': `Como Alex "The Navigator" Kim 🧭, especialista em estratégias de mercado e timing, posso ajudá-lo a navegar pelas oportunidades do mercado cripto. Que estratégia podemos desenvolver?`
    };

    return responses[agentType] || "Como posso ajudá-lo com análises de criptomoedas hoje?";
  }

  /**
   * Gera ID único
   */
  private static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * Obtém contexto atual
   */
  static getContext(): ChatContext {
    return { ...this.context };
  }

  /**
   * Limpa contexto
   */
  static clearContext(): void {
    this.context = {
      messages: [],
      activeAgent: 'ANALYST',
      marketSentiment: 'NEUTRAL'
    };
  }

  /**
   * Troca agente ativo
   */
  static switchAgent(agentType: AgentType): void {
    this.context.activeAgent = agentType;
  }
}
