import { CryptoData, TechnicalIndicators, TradingSignal } from '@/types';

export class TechnicalAnalysisService {
  
  /**
   * Calcula RSI (Relative Strength Index)
   */
  static calculateRSI(prices: number[], period: number = 14): number {
    if (prices.length < period + 1) return 50; // Valor neutro se não há dados suficientes
    
    let gains = 0;
    let losses = 0;
    
    // Calcula ganhos e perdas iniciais
    for (let i = 1; i <= period; i++) {
      const change = prices[i] - prices[i - 1];
      if (change > 0) {
        gains += change;
      } else {
        losses += Math.abs(change);
      }
    }
    
    let avgGain = gains / period;
    let avgLoss = losses / period;
    
    // Calcula RSI para o resto dos dados
    for (let i = period + 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1];
      const gain = change > 0 ? change : 0;
      const loss = change < 0 ? Math.abs(change) : 0;
      
      avgGain = (avgGain * (period - 1) + gain) / period;
      avgLoss = (avgLoss * (period - 1) + loss) / period;
    }
    
    if (avgLoss === 0) return 100;
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
  }

  /**
   * Calcula SMA (Simple Moving Average)
   */
  static calculateSMA(prices: number[], period: number): number {
    if (prices.length < period) return prices[prices.length - 1] || 0;
    
    const slice = prices.slice(-period);
    return slice.reduce((sum, price) => sum + price, 0) / period;
  }

  /**
   * Calcula EMA (Exponential Moving Average)
   */
  static calculateEMA(prices: number[], period: number): number {
    if (prices.length === 0) return 0;
    if (prices.length === 1) return prices[0];
    
    const multiplier = 2 / (period + 1);
    let ema = prices[0];
    
    for (let i = 1; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
    }
    
    return ema;
  }

  /**
   * Calcula Bollinger Bands
   */
  static calculateBollingerBands(prices: number[], period: number = 20, stdDev: number = 2) {
    const sma = this.calculateSMA(prices, period);
    
    if (prices.length < period) {
      return {
        upper: sma * 1.02,
        middle: sma,
        lower: sma * 0.98
      };
    }
    
    const slice = prices.slice(-period);
    const variance = slice.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period;
    const standardDeviation = Math.sqrt(variance);
    
    return {
      upper: sma + (standardDeviation * stdDev),
      middle: sma,
      lower: sma - (standardDeviation * stdDev)
    };
  }

  /**
   * Calcula MACD
   */
  static calculateMACD(prices: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {
    const emaFast = this.calculateEMA(prices, fastPeriod);
    const emaSlow = this.calculateEMA(prices, slowPeriod);
    const macdLine = emaFast - emaSlow;
    
    // Para simplificar, usamos uma aproximação do sinal
    const signalLine = macdLine * 0.9; // Aproximação
    const histogram = macdLine - signalLine;
    
    return {
      macd: macdLine,
      signal: signalLine,
      histogram: histogram
    };
  }

  /**
   * Identifica níveis de suporte e resistência
   */
  static findSupportResistance(prices: number[], currentPrice: number) {
    const sortedPrices = [...prices].sort((a, b) => a - b);
    const priceRanges = [];
    
    // Agrupa preços em ranges
    for (let i = 0; i < sortedPrices.length; i += Math.floor(sortedPrices.length / 10)) {
      const range = sortedPrices.slice(i, i + Math.floor(sortedPrices.length / 10));
      if (range.length > 0) {
        priceRanges.push(range.reduce((sum, p) => sum + p, 0) / range.length);
      }
    }
    
    const support = priceRanges.filter(price => price < currentPrice).slice(-3);
    const resistance = priceRanges.filter(price => price > currentPrice).slice(0, 3);
    
    return {
      support: support.length > 0 ? support : [currentPrice * 0.95],
      resistance: resistance.length > 0 ? resistance : [currentPrice * 1.05]
    };
  }

  /**
   * Gera análise técnica completa
   */
  static generateTechnicalAnalysis(cryptoData: CryptoData, historicalPrices?: number[]): TechnicalIndicators {
    // Se não temos dados históricos, simulamos baseado nos dados atuais
    const prices = historicalPrices || this.generateMockPrices(cryptoData);
    
    const rsi = this.calculateRSI(prices);
    const macd = this.calculateMACD(prices);
    const sma = {
      sma20: this.calculateSMA(prices, 20),
      sma50: this.calculateSMA(prices, 50),
      sma200: this.calculateSMA(prices, 200)
    };
    const ema = {
      ema12: this.calculateEMA(prices, 12),
      ema26: this.calculateEMA(prices, 26)
    };
    const bollinger = this.calculateBollingerBands(prices);
    const supportResistance = this.findSupportResistance(prices, cryptoData.current_price);
    
    return {
      rsi,
      macd,
      sma,
      ema,
      bollinger,
      volume_profile: cryptoData.total_volume / cryptoData.market_cap,
      support_resistance: supportResistance
    };
  }

  /**
   * Gera sinal de trading baseado na análise técnica
   */
  static generateTradingSignal(cryptoData: CryptoData, technicalIndicators: TechnicalIndicators): TradingSignal {
    let bullishSignals = 0;
    let bearishSignals = 0;
    let totalSignals = 0;
    
    const currentPrice = cryptoData.current_price;
    const { rsi, macd, sma, ema, bollinger } = technicalIndicators;
    
    // Análise RSI
    totalSignals++;
    if (rsi < 30) bullishSignals++; // Oversold
    else if (rsi > 70) bearishSignals++; // Overbought
    
    // Análise MACD
    totalSignals++;
    if (macd.macd > macd.signal && macd.histogram > 0) bullishSignals++;
    else if (macd.macd < macd.signal && macd.histogram < 0) bearishSignals++;
    
    // Análise SMA
    totalSignals++;
    if (currentPrice > sma.sma20 && sma.sma20 > sma.sma50) bullishSignals++;
    else if (currentPrice < sma.sma20 && sma.sma20 < sma.sma50) bearishSignals++;
    
    // Análise EMA
    totalSignals++;
    if (ema.ema12 > ema.ema26) bullishSignals++;
    else bearishSignals++;
    
    // Análise Bollinger
    totalSignals++;
    if (currentPrice < bollinger.lower) bullishSignals++; // Oversold
    else if (currentPrice > bollinger.upper) bearishSignals++; // Overbought
    
    // Análise de tendência de preço
    totalSignals++;
    if (cryptoData.price_change_percentage_24h > 5) bullishSignals++;
    else if (cryptoData.price_change_percentage_24h < -5) bearishSignals++;
    
    const bullishPercentage = (bullishSignals / totalSignals) * 100;
    const bearishPercentage = (bearishSignals / totalSignals) * 100;
    
    let type: 'BUY' | 'SELL' | 'HOLD';
    let strength: 'WEAK' | 'MODERATE' | 'STRONG';
    let confidence: number;
    
    if (bullishPercentage >= 60) {
      type = 'BUY';
      strength = bullishPercentage >= 80 ? 'STRONG' : 'MODERATE';
      confidence = bullishPercentage;
    } else if (bearishPercentage >= 60) {
      type = 'SELL';
      strength = bearishPercentage >= 80 ? 'STRONG' : 'MODERATE';
      confidence = bearishPercentage;
    } else {
      type = 'HOLD';
      strength = 'WEAK';
      confidence = Math.max(bullishPercentage, bearishPercentage);
    }
    
    // Calcula targets
    const volatility = Math.abs(cryptoData.price_change_percentage_24h) / 100;
    const priceTarget = type === 'BUY' 
      ? currentPrice * (1 + volatility * 2)
      : currentPrice * (1 - volatility * 2);
    
    const stopLoss = type === 'BUY'
      ? currentPrice * 0.95
      : currentPrice * 1.05;
    
    return {
      type,
      strength,
      confidence: Math.round(confidence),
      price_target: Math.round(priceTarget * 100) / 100,
      stop_loss: Math.round(stopLoss * 100) / 100,
      timeframe: '24h',
      reasoning: this.generateReasoning(type, strength, technicalIndicators, cryptoData)
    };
  }

  /**
   * Gera explicação do sinal
   */
  private static generateReasoning(
    type: 'BUY' | 'SELL' | 'HOLD',
    strength: 'WEAK' | 'MODERATE' | 'STRONG',
    indicators: TechnicalIndicators,
    cryptoData: CryptoData
  ): string {
    const reasons = [];
    
    if (indicators.rsi < 30) reasons.push('RSI indica sobrevenda');
    if (indicators.rsi > 70) reasons.push('RSI indica sobrecompra');
    if (indicators.macd.macd > indicators.macd.signal) reasons.push('MACD bullish');
    if (indicators.macd.macd < indicators.macd.signal) reasons.push('MACD bearish');
    if (cryptoData.current_price > indicators.sma.sma20) reasons.push('Preço acima da SMA20');
    if (cryptoData.current_price < indicators.sma.sma20) reasons.push('Preço abaixo da SMA20');
    
    return reasons.join(', ') || 'Análise baseada em múltiplos indicadores';
  }

  /**
   * Gera preços históricos simulados baseados nos dados atuais
   */
  private static generateMockPrices(cryptoData: CryptoData): number[] {
    const prices = [];
    const currentPrice = cryptoData.current_price;
    const volatility = Math.abs(cryptoData.price_change_percentage_24h) / 100;
    
    // Gera 200 pontos de preço simulados
    for (let i = 0; i < 200; i++) {
      const randomChange = (Math.random() - 0.5) * volatility * 2;
      const price = currentPrice * (1 + randomChange);
      prices.push(Math.max(price, currentPrice * 0.5)); // Evita preços muito baixos
    }
    
    // Adiciona tendência baseada na mudança de 24h
    const trend = cryptoData.price_change_percentage_24h / 100;
    for (let i = 0; i < prices.length; i++) {
      const trendFactor = (i / prices.length) * trend;
      prices[i] *= (1 + trendFactor);
    }
    
    return prices;
  }
}
