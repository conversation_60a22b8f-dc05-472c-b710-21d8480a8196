import { Message, AgentType } from '@/types';
import { IntentType, IntentClassification } from './intent-classifier';

export interface ContextAnalysis {
  conversationFlow: string;
  userMood: 'positive' | 'negative' | 'neutral' | 'curious' | 'urgent';
  topicContinuity: boolean;
  previousContext: string;
  suggestedResponseTone: 'professional' | 'casual' | 'helpful' | 'educational' | 'reassuring';
  keyTopics: string[];
  userExpertiseLevel: 'beginner' | 'intermediate' | 'advanced';
}

export class ContextAnalyzer {
  
  /**
   * Analisa o contexto completo da conversa
   */
  static analyzeContext(
    currentMessage: string,
    history: Message[],
    intent: IntentClassification,
    agentType: AgentType
  ): ContextAnalysis {
    
    const userMood = this.detectUserMood(currentMessage, history);
    const topicContinuity = this.checkTopicContinuity(currentMessage, history);
    const conversationFlow = this.analyzeConversationFlow(history);
    const previousContext = this.extractPreviousContext(history);
    const suggestedResponseTone = this.suggestResponseTone(intent, userMood, agentType);
    const keyTopics = this.extractKeyTopics(currentMessage, history);
    const userExpertiseLevel = this.assessUserExpertise(history);
    
    return {
      conversationFlow,
      userMood,
      topicContinuity,
      previousContext,
      suggestedResponseTone,
      keyTopics,
      userExpertiseLevel
    };
  }
  
  /**
   * Detecta o humor/tom do usuário
   */
  private static detectUserMood(message: string, history: Message[]): ContextAnalysis['userMood'] {
    const normalizedMessage = message.toLowerCase();
    
    // Padrões de humor
    const moodPatterns = {
      positive: [
        /\b(obrigado|obrigada|valeu|legal|ótimo|otimo|perfeito|excelente|show|top|massa|bacana)\b/i,
        /\b(gostei|adorei|amei|curtir|maneiro)\b/i,
        /[!]{2,}/, // Múltiplas exclamações
        /😊|😄|😃|👍|🚀|💪|🔥/
      ],
      negative: [
        /\b(não|nao)\s+(entendi|gostei|concordo|funciona|deu\s+certo)\b/i,
        /\b(ruim|péssimo|pessimo|horrível|horrivel|droga|merda)\b/i,
        /\b(frustrado|irritado|chateado|decepcionado)\b/i,
        /😠|😡|😤|👎|💩/
      ],
      curious: [
        /\b(como|por\s+que|porque|quando|onde|qual|quais)\b/i,
        /\?{2,}/, // Múltiplas interrogações
        /\b(interessante|curioso|quero\s+saber|me\s+explica)\b/i,
        /🤔|❓|❔/
      ],
      urgent: [
        /\b(urgente|rápido|rapido|agora|já|ja|preciso\s+agora)\b/i,
        /\b(help|socorro|ajuda\s+urgente)\b/i,
        /[!]{3,}/, // Muitas exclamações
        /🆘|⚠️|🚨/
      ]
    };
    
    // Conta matches para cada humor
    const scores = {
      positive: 0,
      negative: 0,
      curious: 0,
      urgent: 0
    };
    
    for (const [mood, patterns] of Object.entries(moodPatterns)) {
      for (const pattern of patterns) {
        if (pattern.test(normalizedMessage)) {
          scores[mood as keyof typeof scores]++;
        }
      }
    }
    
    // Considera histórico recente
    const recentMessages = history.slice(-2);
    for (const msg of recentMessages) {
      if (msg.role === 'user') {
        for (const [mood, patterns] of Object.entries(moodPatterns)) {
          for (const pattern of patterns) {
            if (pattern.test(msg.content.toLowerCase())) {
              scores[mood as keyof typeof scores] += 0.5;
            }
          }
        }
      }
    }
    
    // Retorna o humor com maior score
    const maxScore = Math.max(...Object.values(scores));
    if (maxScore === 0) return 'neutral';
    
    const detectedMood = Object.entries(scores).find(([_, score]) => score === maxScore)?.[0];
    return detectedMood as ContextAnalysis['userMood'] || 'neutral';
  }
  
  /**
   * Verifica se há continuidade no tópico da conversa
   */
  private static checkTopicContinuity(message: string, history: Message[]): boolean {
    if (history.length === 0) return false;
    
    const lastMessages = history.slice(-3);
    const currentTopics = this.extractTopicsFromMessage(message);
    
    for (const msg of lastMessages) {
      const msgTopics = this.extractTopicsFromMessage(msg.content);
      const commonTopics = currentTopics.filter(topic => msgTopics.includes(topic));
      if (commonTopics.length > 0) return true;
    }
    
    return false;
  }
  
  /**
   * Extrai tópicos de uma mensagem
   */
  private static extractTopicsFromMessage(message: string): string[] {
    const topics = [];
    const normalizedMessage = message.toLowerCase();
    
    // Tópicos de crypto
    const cryptoTopics = ['bitcoin', 'ethereum', 'trading', 'mercado', 'preço', 'análise', 'investimento'];
    for (const topic of cryptoTopics) {
      if (normalizedMessage.includes(topic)) {
        topics.push(topic);
      }
    }
    
    return topics;
  }
  
  /**
   * Analisa o fluxo da conversa
   */
  private static analyzeConversationFlow(history: Message[]): string {
    if (history.length === 0) return 'início da conversa';
    if (history.length === 1) return 'segunda interação';
    
    const recentMessages = history.slice(-5);
    const userMessages = recentMessages.filter(msg => msg.role === 'user');
    
    if (userMessages.length >= 3) {
      return 'conversa estabelecida';
    } else if (userMessages.length === 2) {
      return 'construindo rapport';
    } else {
      return 'início da interação';
    }
  }
  
  /**
   * Extrai contexto das mensagens anteriores
   */
  private static extractPreviousContext(history: Message[]): string {
    if (history.length === 0) return '';
    
    const contexts = [];
    const lastAssistantMessage = history.slice().reverse().find(msg => msg.role === 'assistant');
    
    if (lastAssistantMessage) {
      if (lastAssistantMessage.cryptoData) {
        contexts.push(`Última análise: ${lastAssistantMessage.cryptoData.name}`);
      }
      
      if (lastAssistantMessage.agentType) {
        contexts.push(`Último agente: ${lastAssistantMessage.agentType}`);
      }
    }
    
    return contexts.join(' | ');
  }
  
  /**
   * Sugere o tom de resposta baseado no contexto
   */
  private static suggestResponseTone(
    intent: IntentClassification,
    userMood: ContextAnalysis['userMood'],
    agentType: AgentType
  ): ContextAnalysis['suggestedResponseTone'] {
    
    // Baseado na intenção
    if (intent.intent === 'HELP' || intent.intent === 'AGENT_INFO') {
      return 'helpful';
    }
    
    if (intent.intent === 'CRYPTO_ANALYSIS' || intent.intent === 'TRADING_ADVICE') {
      return 'professional';
    }
    
    if (intent.intent === 'GREETING' || intent.intent === 'PERSONAL_CHAT') {
      return 'casual';
    }
    
    // Baseado no humor do usuário
    if (userMood === 'negative' || userMood === 'urgent') {
      return 'reassuring';
    }
    
    if (userMood === 'curious') {
      return 'educational';
    }
    
    // Baseado no tipo de agente
    if (agentType === 'ANALYST') {
      return 'professional';
    } else if (agentType === 'MENTOR') {
      return 'educational';
    } else {
      return 'helpful';
    }
  }
  
  /**
   * Extrai tópicos-chave da conversa
   */
  private static extractKeyTopics(message: string, history: Message[]): string[] {
    const topics = new Set<string>();
    
    // Tópicos da mensagem atual
    const currentTopics = this.extractTopicsFromMessage(message);
    currentTopics.forEach(topic => topics.add(topic));
    
    // Tópicos do histórico recente
    const recentMessages = history.slice(-3);
    for (const msg of recentMessages) {
      const msgTopics = this.extractTopicsFromMessage(msg.content);
      msgTopics.forEach(topic => topics.add(topic));
    }
    
    return Array.from(topics);
  }
  
  /**
   * Avalia o nível de expertise do usuário
   */
  private static assessUserExpertise(history: Message[]): ContextAnalysis['userExpertiseLevel'] {
    const userMessages = history.filter(msg => msg.role === 'user');
    
    let technicalTerms = 0;
    let basicQuestions = 0;
    
    const technicalPatterns = [
      /\b(rsi|macd|bollinger|fibonacci|support|resistance|volume|candlestick)\b/i,
      /\b(scalping|swing\s+trading|day\s+trading|hodl|dca)\b/i,
      /\b(market\s+cap|liquidity|volatility|correlation)\b/i
    ];
    
    const basicPatterns = [
      /\b(o\s+que\s+é|como\s+funciona|não\s+entendo|nao\s+entendo)\b/i,
      /\b(iniciante|começando|comecando|primeiro|primeira\s+vez)\b/i,
      /\b(explique|me\s+ensina|como\s+faço|como\s+faco)\b/i
    ];
    
    for (const msg of userMessages) {
      const content = msg.content.toLowerCase();
      
      for (const pattern of technicalPatterns) {
        if (pattern.test(content)) technicalTerms++;
      }
      
      for (const pattern of basicPatterns) {
        if (pattern.test(content)) basicQuestions++;
      }
    }
    
    if (technicalTerms >= 2) return 'advanced';
    if (technicalTerms >= 1 && basicQuestions === 0) return 'intermediate';
    return 'beginner';
  }
}
