import { IntentClassifier } from './intent-classifier';
import { ContextAnalyzer } from './context-analyzer';
import { IntelligentResponseGenerator } from './intelligent-response-generator';
import { Message, AgentType } from '@/types';

/**
 * Testa a nova inteligência dos agentes
 */
export class IntelligentAgentTester {
  
  /**
   * Testa diferentes cenários de perguntas
   */
  static testScenarios() {
    console.log('🧠 TESTANDO NOVA INTELIGÊNCIA DOS AGENTES\n');
    
    // Cenário 1: Pergunta sobre disponibilidade do agente (problema original)
    this.testScenario(
      "você trabalha 24 horas?",
      [],
      'ANALYST',
      "❌ PROBLEMA ORIGINAL: Agente respondia sobre análise de crypto"
    );
    
    // Cenário 2: Pergunta sobre capacidades do agente
    this.testScenario(
      "o que você pode fazer?",
      [],
      'MENTOR',
      "✅ DEVE: Explicar capacidades do agente"
    );
    
    // Cenário 3: <PERSON>udação
    this.testScenario(
      "oi, tudo bem?",
      [],
      'STRATEGIST',
      "✅ DEVE: Responder saudação e se apresentar"
    );
    
    // Cenário 4: Análise de crypto específica
    this.testScenario(
      "como está o bitcoin hoje?",
      [],
      'ANALYST',
      "✅ DEVE: Analisar Bitcoin com dados técnicos"
    );
    
    // Cenário 5: Pergunta com contexto de conversa anterior
    const previousMessages: Message[] = [
      {
        id: '1',
        content: 'como está o ethereum?',
        role: 'user',
        timestamp: new Date()
      },
      {
        id: '2',
        content: 'Ethereum está em $2,500...',
        role: 'assistant',
        agentType: 'ANALYST',
        timestamp: new Date(),
        cryptoData: {
          id: 'ethereum',
          name: 'Ethereum',
          symbol: 'eth',
          current_price: 2500,
          price_change_percentage_24h: 5.2,
          market_cap: 300000000000,
          market_cap_rank: 2,
          total_volume: 15000000000,
          circulating_supply: 120000000,
          max_supply: null,
          image: ''
        }
      }
    ];
    
    this.testScenario(
      "e o bitcoin?",
      previousMessages,
      'ANALYST',
      "✅ DEVE: Entender contexto e analisar Bitcoin"
    );
    
    // Cenário 6: Pergunta urgente
    this.testScenario(
      "URGENTE! Devo vender meu bitcoin agora???",
      [],
      'STRATEGIST',
      "✅ DEVE: Responder com tom tranquilizador e orientação"
    );
    
    // Cenário 7: Pergunta de iniciante
    this.testScenario(
      "não entendo nada de crypto, me explica o que é bitcoin?",
      [],
      'MENTOR',
      "✅ DEVE: Resposta educativa para iniciante"
    );
  }
  
  /**
   * Testa um cenário específico
   */
  private static testScenario(
    userMessage: string,
    conversationHistory: Message[],
    agentType: AgentType,
    expectedBehavior: string
  ) {
    console.log(`📝 CENÁRIO: "${userMessage}"`);
    console.log(`🎯 ${expectedBehavior}`);
    console.log(`🤖 Agente: ${agentType}\n`);
    
    // 1. Classifica intenção
    const intent = IntentClassifier.classifyIntent(userMessage, conversationHistory);
    console.log(`🧠 INTENÇÃO DETECTADA:`);
    console.log(`   Tipo: ${intent.intent}`);
    console.log(`   Confiança: ${(intent.confidence * 100).toFixed(1)}%`);
    console.log(`   Entidades: ${intent.entities.join(', ') || 'Nenhuma'}`);
    console.log(`   Contexto: ${intent.context || 'Nenhum'}\n`);
    
    // 2. Analisa contexto
    const context = ContextAnalyzer.analyzeContext(
      userMessage,
      conversationHistory,
      intent,
      agentType
    );
    console.log(`🔍 ANÁLISE DE CONTEXTO:`);
    console.log(`   Humor do usuário: ${context.userMood}`);
    console.log(`   Continuidade do tópico: ${context.topicContinuity ? 'Sim' : 'Não'}`);
    console.log(`   Fluxo da conversa: ${context.conversationFlow}`);
    console.log(`   Tom sugerido: ${context.suggestedResponseTone}`);
    console.log(`   Nível de expertise: ${context.userExpertiseLevel}`);
    console.log(`   Tópicos-chave: ${context.keyTopics.join(', ') || 'Nenhum'}\n`);
    
    // 3. Gera template de resposta
    const responseTemplate = IntelligentResponseGenerator.generateResponseTemplate(
      intent,
      context,
      agentType,
      userMessage
    );
    console.log(`📋 TEMPLATE DE RESPOSTA:`);
    console.log(`   Estilo: ${responseTemplate.responseStyle}`);
    console.log(`   Incluir dados de mercado: ${responseTemplate.includeMarketData ? 'Sim' : 'Não'}`);
    console.log(`   Incluir análise técnica: ${responseTemplate.includeTechnicalAnalysis ? 'Sim' : 'Não'}\n`);
    
    // 4. Mostra prompt do sistema (resumido)
    console.log(`🎭 PROMPT DO SISTEMA (resumido):`);
    console.log(`   ${responseTemplate.systemPrompt.substring(0, 100)}...\n`);
    
    console.log(`✅ RESULTADO: ${this.evaluateResult(intent, context, responseTemplate, expectedBehavior)}\n`);
    console.log('─'.repeat(80) + '\n');
  }
  
  /**
   * Avalia se o resultado está correto
   */
  private static evaluateResult(
    intent: any,
    context: any,
    template: any,
    expectedBehavior: string
  ): string {
    // Lógica simples de avaliação baseada na intenção detectada
    if (expectedBehavior.includes('disponibilidade') && intent.intent === 'AGENT_INFO') {
      return '✅ CORRETO - Detectou pergunta sobre o agente';
    }
    
    if (expectedBehavior.includes('capacidades') && intent.intent === 'AGENT_INFO') {
      return '✅ CORRETO - Detectou pergunta sobre capacidades';
    }
    
    if (expectedBehavior.includes('saudação') && intent.intent === 'GREETING') {
      return '✅ CORRETO - Detectou saudação';
    }
    
    if (expectedBehavior.includes('Bitcoin') && intent.intent === 'CRYPTO_ANALYSIS') {
      return '✅ CORRETO - Detectou análise de crypto';
    }
    
    if (expectedBehavior.includes('contexto') && context.topicContinuity) {
      return '✅ CORRETO - Detectou continuidade de contexto';
    }
    
    if (expectedBehavior.includes('urgente') && context.userMood === 'urgent') {
      return '✅ CORRETO - Detectou urgência';
    }
    
    if (expectedBehavior.includes('iniciante') && context.userExpertiseLevel === 'beginner') {
      return '✅ CORRETO - Detectou usuário iniciante';
    }
    
    return '⚠️ PARCIAL - Resultado pode ser melhorado';
  }
}

// Para testar, descomente a linha abaixo:
// IntelligentAgentTester.testScenarios();
