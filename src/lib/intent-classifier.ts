import { Message, AgentType } from '@/types';

export type IntentType = 
  | 'AGENT_INFO'           // Perguntas sobre o agente (horário, funcionamento, etc.)
  | 'CRYPTO_ANALYSIS'      // Análise de criptomoedas específicas
  | 'MARKET_GENERAL'       // Perguntas gerais sobre mercado
  | 'GREETING'             // Saudações
  | 'PERSONAL_CHAT'        // Conversa pessoal/casual
  | 'HELP'                 // Pedidos de ajuda
  | 'TRADING_ADVICE'       // Conselhos de trading
  | 'COMPARISON'           // Comparação entre cryptos
  | 'PRICE_PREDICTION'     // Previsões de preço
  | 'UNKNOWN';             // Não identificado

export interface IntentClassification {
  intent: IntentType;
  confidence: number;
  entities: string[];
  context: string;
}

export class IntentClassifier {
  
  /**
   * Classifica a intenção de uma mensagem do usuário
   */
  static classifyIntent(message: string, conversationHistory: Message[] = []): IntentClassification {
    const normalizedMessage = message.toLowerCase().trim();
    
    // Padrões para cada tipo de intenção
    const patterns = {
      AGENT_INFO: [
        /\b(você|voce)\s+(trabalha|funciona|está|esta|fica)\s+(24\s*horas?|sempre|disponível|disponivel|online)\b/i,
        /\b(horário|horario|funcionamento|disponibilidade)\b/i,
        /\b(você|voce)\s+(é|eh|sou|está|esta)\s+(um\s+)?(bot|robô|robo|ia|inteligência|inteligencia)\b/i,
        /\b(quem\s+)?(você|voce)\s+(é|eh|sou)\b/i,
        /\b(como\s+)?(você|voce)\s+(funciona|trabalha)\b/i,
        /\b(você|voce)\s+(pode|consegue|sabe)\b/i,
        /\b(suas\s+)?(especialidades|habilidades|capacidades)\b/i,
        /\b(sobre\s+)?(você|voce|agente|assistente)\b/i
      ],
      
      GREETING: [
        /^(oi|olá|ola|hey|hi|hello|bom\s+dia|boa\s+tarde|boa\s+noite|e\s+aí|eai|como\s+vai|tudo\s+bem|beleza)\b/i,
        /^(tchau|bye|até\s+logo|falou|obrigado|obrigada|valeu)\b/i
      ],
      
      HELP: [
        /\b(ajuda|help|socorro|como\s+usar|como\s+funciona|tutorial|guia)\b/i,
        /\b(não\s+entendi|nao\s+entendi|explique|como\s+faço|como\s+faco)\b/i,
        /\b(comandos|opções|opcoes|funcionalidades)\b/i
      ],
      
      CRYPTO_ANALYSIS: [
        /\b(bitcoin|btc|ethereum|eth|cardano|ada|solana|sol|polkadot|dot|chainlink|link|litecoin|ltc|dogecoin|doge|shiba|shib|avalanche|avax|matic|polygon|bnb|binance|xrp|ripple|atom|cosmos|algo|algorand)\b/i,
        /\b(analise|análise|analyze)\s+(o|a|do|da)\s+\w+/i,
        /\b(como\s+está|como\s+esta)\s+(o|a)\s+\w+/i,
        /\b(preço|preco|cotação|cotacao|valor)\s+(do|da|de)\b/i
      ],
      
      TRADING_ADVICE: [
        /\b(devo\s+)?(comprar|vender|investir|apostar)\b/i,
        /\b(é\s+hora|eh\s+hora|momento|timing)\s+(de|para)\s+(comprar|vender)\b/i,
        /\b(estratégia|estrategia|tática|tatica|plano)\b/i,
        /\b(stop\s*loss|take\s*profit|entrada|saída|saida)\b/i,
        /\b(gestão\s+de\s+risco|gestao\s+de\s+risco|money\s+management)\b/i,
        /\b(portfolio|carteira|diversificar)\b/i
      ],
      
      MARKET_GENERAL: [
        /\b(mercado|market)\s+(cripto|crypto|criptomoedas|cryptocurrency)\b/i,
        /\b(como\s+está|como\s+esta)\s+(o\s+)?mercado\b/i,
        /\b(tendência|tendencia|trend|sentimento)\s+(do\s+)?mercado\b/i,
        /\b(bull\s+market|bear\s+market|alta|baixa|correção|correcao)\b/i,
        /\b(fear\s+and\s+greed|medo\s+e\s+ganância|medo\s+e\s+ganancia)\b/i
      ],
      
      COMPARISON: [
        /\b(melhor|pior)\s+(que|do\s+que)\b/i,
        /\b(comparar|comparação|comparacao|versus|vs|x)\b/i,
        /\b(diferença|diferenca|entre)\b/i,
        /\b(qual\s+)?(é\s+melhor|eh\s+melhor|escolher)\b/i
      ],
      
      PRICE_PREDICTION: [
        /\b(previsão|previsao|prediction|vai\s+subir|vai\s+descer)\b/i,
        /\b(futuro|amanhã|amanha|próxima|proxima|semana|mês|mes|ano)\b/i,
        /\b(target|meta|objetivo|chegará|chegara)\b/i,
        /\b(quando|até\s+quando|ate\s+quando)\b/i
      ],
      
      PERSONAL_CHAT: [
        /\b(como\s+você\s+está|como\s+voce\s+esta|tudo\s+bem\s+com\s+você|tudo\s+bem\s+com\s+voce)\b/i,
        /\b(gosta|prefere|acha|pensa|opinião|opiniao)\b/i,
        /\b(experiência|experiencia|história|historia|vida)\b/i,
        /\b(família|familia|amigos|hobby|tempo\s+livre)\b/i
      ]
    };
    
    // Detecta entidades (nomes de criptomoedas)
    const cryptoEntities = this.extractCryptoEntities(normalizedMessage);
    
    // Calcula scores para cada intenção
    const scores: Record<IntentType, number> = {
      AGENT_INFO: 0,
      CRYPTO_ANALYSIS: 0,
      MARKET_GENERAL: 0,
      GREETING: 0,
      PERSONAL_CHAT: 0,
      HELP: 0,
      TRADING_ADVICE: 0,
      COMPARISON: 0,
      PRICE_PREDICTION: 0,
      UNKNOWN: 0
    };
    
    // Calcula scores baseado nos padrões
    for (const [intent, patternList] of Object.entries(patterns)) {
      for (const pattern of patternList) {
        if (pattern.test(normalizedMessage)) {
          scores[intent as IntentType] += 1;
        }
      }
    }
    
    // Boost para crypto analysis se entidades de crypto foram detectadas
    if (cryptoEntities.length > 0) {
      scores.CRYPTO_ANALYSIS += cryptoEntities.length * 2;
    }
    
    // Considera contexto da conversa
    const contextBoost = this.analyzeConversationContext(conversationHistory);
    for (const [intent, boost] of Object.entries(contextBoost)) {
      scores[intent as IntentType] += boost;
    }
    
    // Encontra a intenção com maior score
    const maxScore = Math.max(...Object.values(scores));
    const bestIntent = Object.entries(scores).find(([_, score]) => score === maxScore)?.[0] as IntentType;
    
    // Se nenhum padrão foi encontrado, classifica como UNKNOWN
    const finalIntent = maxScore > 0 ? bestIntent : 'UNKNOWN';
    const confidence = maxScore > 0 ? Math.min(maxScore / 3, 1) : 0;
    
    return {
      intent: finalIntent,
      confidence,
      entities: cryptoEntities,
      context: this.generateContextSummary(normalizedMessage, conversationHistory)
    };
  }
  
  /**
   * Extrai entidades de criptomoedas da mensagem
   */
  private static extractCryptoEntities(message: string): string[] {
    const cryptoPatterns = [
      { pattern: /\bbitcoin\b/i, entity: 'bitcoin' },
      { pattern: /\bbtc\b/i, entity: 'bitcoin' },
      { pattern: /\bethereum\b/i, entity: 'ethereum' },
      { pattern: /\beth\b/i, entity: 'ethereum' },
      { pattern: /\bcardano\b/i, entity: 'cardano' },
      { pattern: /\bada\b/i, entity: 'cardano' },
      { pattern: /\bsolana\b/i, entity: 'solana' },
      { pattern: /\bsol\b/i, entity: 'solana' },
      { pattern: /\bpolkadot\b/i, entity: 'polkadot' },
      { pattern: /\bdot\b/i, entity: 'polkadot' },
      { pattern: /\bchainlink\b/i, entity: 'chainlink' },
      { pattern: /\blink\b/i, entity: 'chainlink' },
      { pattern: /\blitecoin\b/i, entity: 'litecoin' },
      { pattern: /\bltc\b/i, entity: 'litecoin' },
      { pattern: /\bdogecoin\b/i, entity: 'dogecoin' },
      { pattern: /\bdoge\b/i, entity: 'dogecoin' },
      { pattern: /\bshiba\b/i, entity: 'shiba-inu' },
      { pattern: /\bshib\b/i, entity: 'shiba-inu' },
      { pattern: /\bavalanche\b/i, entity: 'avalanche-2' },
      { pattern: /\bavax\b/i, entity: 'avalanche-2' },
      { pattern: /\bmatic\b/i, entity: 'matic-network' },
      { pattern: /\bpolygon\b/i, entity: 'matic-network' },
      { pattern: /\bbnb\b/i, entity: 'binancecoin' },
      { pattern: /\bbinance\b/i, entity: 'binancecoin' },
      { pattern: /\bxrp\b/i, entity: 'ripple' },
      { pattern: /\bripple\b/i, entity: 'ripple' },
      { pattern: /\batom\b/i, entity: 'cosmos' },
      { pattern: /\bcosmos\b/i, entity: 'cosmos' },
      { pattern: /\balgo\b/i, entity: 'algorand' },
      { pattern: /\balgorand\b/i, entity: 'algorand' }
    ];
    
    const entities: string[] = [];
    for (const { pattern, entity } of cryptoPatterns) {
      if (pattern.test(message)) {
        if (!entities.includes(entity)) {
          entities.push(entity);
        }
      }
    }
    
    return entities;
  }
  
  /**
   * Analisa o contexto da conversa para dar boost a certas intenções
   */
  private static analyzeConversationContext(history: Message[]): Record<string, number> {
    const boost: Record<string, number> = {};
    
    if (history.length === 0) return boost;
    
    const recentMessages = history.slice(-3); // Últimas 3 mensagens
    
    for (const msg of recentMessages) {
      if (msg.role === 'assistant' && msg.cryptoData) {
        boost.CRYPTO_ANALYSIS = (boost.CRYPTO_ANALYSIS || 0) + 0.5;
      }
      
      if (msg.content.toLowerCase().includes('trading') || msg.content.toLowerCase().includes('comprar')) {
        boost.TRADING_ADVICE = (boost.TRADING_ADVICE || 0) + 0.3;
      }
      
      if (msg.content.toLowerCase().includes('mercado')) {
        boost.MARKET_GENERAL = (boost.MARKET_GENERAL || 0) + 0.3;
      }
    }
    
    return boost;
  }
  
  /**
   * Gera um resumo do contexto para ajudar na resposta
   */
  private static generateContextSummary(message: string, history: Message[]): string {
    const contexts = [];
    
    if (history.length > 0) {
      const lastMessage = history[history.length - 1];
      if (lastMessage.role === 'assistant' && lastMessage.cryptoData) {
        contexts.push(`Última análise: ${lastMessage.cryptoData.name}`);
      }
    }
    
    const cryptoMentioned = this.extractCryptoEntities(message);
    if (cryptoMentioned.length > 0) {
      contexts.push(`Cryptos mencionadas: ${cryptoMentioned.join(', ')}`);
    }
    
    return contexts.join(' | ');
  }
}
