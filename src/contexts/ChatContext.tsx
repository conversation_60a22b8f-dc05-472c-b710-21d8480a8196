'use client';

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { Message, AgentType, ChatContext as ChatContextType } from '@/types';

interface ChatState {
  messages: Message[];
  activeAgent: AgentType;
  currentCrypto?: string;
  isLoading: boolean;
  lastAnalysis?: Date;
}

type ChatAction =
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'SET_MESSAGES'; payload: Message[] }
  | { type: 'SET_ACTIVE_AGENT'; payload: AgentType }
  | { type: 'SET_CURRENT_CRYPTO'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_LAST_ANALYSIS'; payload: Date }
  | { type: 'CLEAR_CONTEXT' }
  | { type: 'LOAD_FROM_STORAGE'; payload: ChatState };

const initialState: ChatState = {
  messages: [],
  activeAgent: 'technical_analyst',
  isLoading: false,
};

function chatReducer(state: ChatState, action: ChatAction): ChatState {
  switch (action.type) {
    case 'ADD_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload],
      };

    case 'SET_MESSAGES':
      return {
        ...state,
        messages: action.payload,
      };

    case 'SET_ACTIVE_AGENT':
      return {
        ...state,
        activeAgent: action.payload,
      };

    case 'SET_CURRENT_CRYPTO':
      return {
        ...state,
        currentCrypto: action.payload,
      };

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };

    case 'SET_LAST_ANALYSIS':
      return {
        ...state,
        lastAnalysis: action.payload,
      };

    case 'CLEAR_CONTEXT':
      return {
        ...initialState,
        messages: [],
      };

    case 'LOAD_FROM_STORAGE':
      return {
        ...state,
        ...action.payload,
      };

    default:
      return state;
  }
}

interface ChatContextValue {
  state: ChatState;
  addMessage: (message: Message) => void;
  setMessages: (messages: Message[]) => void;
  setActiveAgent: (agent: AgentType) => void;
  setCurrentCrypto: (crypto: string) => void;
  setLoading: (loading: boolean) => void;
  setLastAnalysis: (date: Date) => void;
  clearContext: () => void;
  saveToStorage: () => void;
  loadFromStorage: () => void;
}

const ChatContext = createContext<ChatContextValue | undefined>(undefined);

export function ChatProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(chatReducer, initialState);

  // Salva no localStorage sempre que o estado muda
  useEffect(() => {
    if (typeof window !== 'undefined' && state.messages.length > 0) {
      const dataToSave = {
        messages: state.messages,
        activeAgent: state.activeAgent,
        currentCrypto: state.currentCrypto,
        lastAnalysis: state.lastAnalysis,
      };
      localStorage.setItem('cryptoagent-chat', JSON.stringify(dataToSave));
    }
  }, [state]);

  // Carrega do localStorage na inicialização
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('cryptoagent-chat');
      if (saved) {
        try {
          const parsedData = JSON.parse(saved);
          // Converte strings de data de volta para objetos Date
          const messages = parsedData.messages?.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })) || [];
          
          dispatch({
            type: 'LOAD_FROM_STORAGE',
            payload: {
              ...parsedData,
              messages,
              lastAnalysis: parsedData.lastAnalysis ? new Date(parsedData.lastAnalysis) : undefined,
              isLoading: false,
            },
          });
        } catch (error) {
          console.error('Erro ao carregar dados do localStorage:', error);
        }
      }
    }
  }, []);

  const addMessage = (message: Message) => {
    dispatch({ type: 'ADD_MESSAGE', payload: message });
  };

  const setMessages = (messages: Message[]) => {
    dispatch({ type: 'SET_MESSAGES', payload: messages });
  };

  const setActiveAgent = (agent: AgentType) => {
    dispatch({ type: 'SET_ACTIVE_AGENT', payload: agent });
  };

  const setCurrentCrypto = (crypto: string) => {
    dispatch({ type: 'SET_CURRENT_CRYPTO', payload: crypto });
  };

  const setLoading = (loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  };

  const setLastAnalysis = (date: Date) => {
    dispatch({ type: 'SET_LAST_ANALYSIS', payload: date });
  };

  const clearContext = () => {
    dispatch({ type: 'CLEAR_CONTEXT' });
    if (typeof window !== 'undefined') {
      localStorage.removeItem('cryptoagent-chat');
    }
  };

  const saveToStorage = () => {
    if (typeof window !== 'undefined') {
      const dataToSave = {
        messages: state.messages,
        activeAgent: state.activeAgent,
        currentCrypto: state.currentCrypto,
        lastAnalysis: state.lastAnalysis,
      };
      localStorage.setItem('cryptoagent-chat', JSON.stringify(dataToSave));
    }
  };

  const loadFromStorage = () => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('cryptoagent-chat');
      if (saved) {
        try {
          const parsedData = JSON.parse(saved);
          const messages = parsedData.messages?.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })) || [];
          
          dispatch({
            type: 'LOAD_FROM_STORAGE',
            payload: {
              ...parsedData,
              messages,
              lastAnalysis: parsedData.lastAnalysis ? new Date(parsedData.lastAnalysis) : undefined,
              isLoading: false,
            },
          });
        } catch (error) {
          console.error('Erro ao carregar dados do localStorage:', error);
        }
      }
    }
  };

  const value: ChatContextValue = {
    state,
    addMessage,
    setMessages,
    setActiveAgent,
    setCurrentCrypto,
    setLoading,
    setLastAnalysis,
    clearContext,
    saveToStorage,
    loadFromStorage,
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
}

export function useChatContext() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChatContext deve ser usado dentro de um ChatProvider');
  }
  return context;
}
