import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { ChatProvider } from "@/contexts/ChatContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "CryptoAgent AI - Assistente Inteligente para Criptomoedas",
  description: "Chat com IA especializada em análise técnica, trading e consultoria de mercado de criptomoedas. 3 agentes especializados para ajudá-lo com Bitcoin, Ethereum e outras cryptos.",
  keywords: "criptomoedas, bitcoin, ethereum, análise técnica, trading, IA, assistente, crypto",
  authors: [{ name: "CryptoAgent AI" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ChatProvider>
          {children}
        </ChatProvider>
      </body>
    </html>
  );
}
