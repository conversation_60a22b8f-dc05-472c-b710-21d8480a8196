import { NextRequest, NextResponse } from 'next/server';
import { AIService } from '@/lib/ai-service';
import { AgentType } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { message, agent } = body;

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Mensagem é obrigatória' },
        { status: 400 }
      );
    }

    // Valida o agente se fornecido
    const validAgents: AgentType[] = ['technical_analyst', 'experienced_trader', 'market_consultant'];
    const selectedAgent = agent && validAgents.includes(agent) ? agent : undefined;

    // Processa a mensagem
    const response = await AIService.processMessage(message.trim(), selectedAgent);

    return NextResponse.json({
      success: true,
      data: response
    });

  } catch (error) {
    console.error('Erro na API de chat:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro interno do servidor' 
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Retorna o contexto atual do chat
    const context = AIService.getContext();
    
    return NextResponse.json({
      success: true,
      data: context
    });

  } catch (error) {
    console.error('Erro ao buscar contexto:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro interno do servidor' 
      },
      { status: 500 }
    );
  }
}

export async function DELETE() {
  try {
    // Limpa o contexto do chat
    AIService.clearContext();
    
    return NextResponse.json({
      success: true,
      message: 'Contexto limpo com sucesso'
    });

  } catch (error) {
    console.error('Erro ao limpar contexto:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro interno do servidor' 
      },
      { status: 500 }
    );
  }
}
