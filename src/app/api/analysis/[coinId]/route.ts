import { NextRequest, NextResponse } from 'next/server';
import { CryptoApiService } from '@/lib/crypto-api';
import { TechnicalAnalysisService } from '@/lib/technical-analysis';

export async function GET(
  request: NextRequest,
  { params }: { params: { coinId: string } }
) {
  try {
    const { coinId } = params;

    if (!coinId) {
      return NextResponse.json(
        { error: 'ID da criptomoeda é obrigatório' },
        { status: 400 }
      );
    }

    // Busca dados da criptomoeda
    const cryptoResponse = await CryptoApiService.getCryptoData(coinId);

    if (!cryptoResponse.success || !cryptoResponse.data) {
      return NextResponse.json(
        { error: cryptoResponse.error || 'Erro ao buscar dados da criptomoeda' },
        { status: 500 }
      );
    }

    const cryptoData = cryptoResponse.data;

    // Gera análise técnica
    const technicalAnalysis = TechnicalAnalysisService.generateTechnicalAnalysis(cryptoData);

    // Gera sinal de trading
    const tradingSignal = TechnicalAnalysisService.generateTradingSignal(cryptoData, technicalAnalysis);

    return NextResponse.json({
      success: true,
      data: {
        cryptoData,
        technicalAnalysis,
        tradingSignal,
        timestamp: new Date()
      }
    });

  } catch (error) {
    console.error('Erro na API de análise:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro interno do servidor' 
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { coinId: string } }
) {
  try {
    const { coinId } = params;
    const body = await request.json();
    const { historicalPrices } = body;

    if (!coinId) {
      return NextResponse.json(
        { error: 'ID da criptomoeda é obrigatório' },
        { status: 400 }
      );
    }

    // Busca dados da criptomoeda
    const cryptoResponse = await CryptoApiService.getCryptoData(coinId);

    if (!cryptoResponse.success || !cryptoResponse.data) {
      return NextResponse.json(
        { error: cryptoResponse.error || 'Erro ao buscar dados da criptomoeda' },
        { status: 500 }
      );
    }

    const cryptoData = cryptoResponse.data;

    // Gera análise técnica com preços históricos fornecidos
    const technicalAnalysis = TechnicalAnalysisService.generateTechnicalAnalysis(
      cryptoData, 
      historicalPrices
    );

    // Gera sinal de trading
    const tradingSignal = TechnicalAnalysisService.generateTradingSignal(cryptoData, technicalAnalysis);

    return NextResponse.json({
      success: true,
      data: {
        cryptoData,
        technicalAnalysis,
        tradingSignal,
        timestamp: new Date()
      }
    });

  } catch (error) {
    console.error('Erro na API de análise:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro interno do servidor' 
      },
      { status: 500 }
    );
  }
}
