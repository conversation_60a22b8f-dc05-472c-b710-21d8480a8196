import { NextRequest, NextResponse } from 'next/server';
import { CryptoApiService } from '@/lib/crypto-api';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'global';
    const limit = parseInt(searchParams.get('limit') || '10');

    switch (type) {
      case 'global':
        // Dados globais do mercado
        const globalResponse = await CryptoApiService.getMarketData();
        
        if (!globalResponse.success) {
          return NextResponse.json(
            { error: globalResponse.error || 'Erro ao buscar dados globais' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          data: globalResponse.data,
          timestamp: globalResponse.timestamp
        });

      case 'popular':
        // Criptomoedas populares
        const popularResponse = await CryptoApiService.getPopularCoins(limit);
        
        if (!popularResponse.success) {
          return NextResponse.json(
            { error: popularResponse.error || 'Erro ao buscar moedas populares' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          data: popularResponse.data,
          timestamp: popularResponse.timestamp
        });

      case 'multiple':
        // Múltiplas criptomoedas
        const coins = searchParams.get('coins')?.split(',') || [];
        
        if (coins.length === 0) {
          return NextResponse.json(
            { error: 'Lista de moedas é obrigatória para tipo "multiple"' },
            { status: 400 }
          );
        }

        const multipleResponse = await CryptoApiService.getMultipleCryptoData(coins);
        
        if (!multipleResponse.success) {
          return NextResponse.json(
            { error: multipleResponse.error || 'Erro ao buscar múltiplas moedas' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          data: multipleResponse.data,
          timestamp: multipleResponse.timestamp
        });

      default:
        return NextResponse.json(
          { error: 'Tipo de consulta inválido. Use: global, popular, ou multiple' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Erro na API de mercado:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro interno do servidor' 
      },
      { status: 500 }
    );
  }
}
