import { NextRequest, NextResponse } from 'next/server';
import { CryptoApiService } from '@/lib/crypto-api';

export async function GET(
  request: NextRequest,
  { params }: { params: { coinId: string } }
) {
  try {
    const { coinId } = params;

    if (!coinId) {
      return NextResponse.json(
        { error: 'ID da criptomoeda é obrigatório' },
        { status: 400 }
      );
    }

    // Busca dados da criptomoeda
    const response = await CryptoApiService.getCryptoData(coinId);

    if (!response.success) {
      return NextResponse.json(
        { error: response.error || 'Erro ao buscar dados da criptomoeda' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: response.data,
      timestamp: response.timestamp
    });

  } catch (error) {
    console.error('Erro na API de crypto:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro interno do servidor' 
      },
      { status: 500 }
    );
  }
}
