'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, TrendingUp, BarChart3, GraduationCap } from 'lucide-react';
import { Message, AgentType } from '@/types';
import { AIService } from '@/lib/ai-service';
import { cn } from '@/lib/utils';
import { useChatContext } from '@/contexts/ChatContext';
import { AgentSelector } from './AgentSelector';
import { MessageBubble } from './MessageBubble';
import { TypingIndicator } from './TypingIndicator';

export function ChatInterface() {
  const {
    state,
    addMessage,
    setActiveAgent,
    setLoading,
    setCurrentCrypto,
    setLastAnalysis
  } = useChatContext();

  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [state.messages]);

  useEffect(() => {
    // Mensagem de boas-vindas apenas se não há mensagens
    if (state.messages.length === 0) {
      const welcomeMessage: Message = {
        id: 'welcome',
        content: `Olá! 👋 Bem-vindo ao **CryptoAgent AI**!

Sou seu assistente especializado em criptomoedas com 3 agentes especializados:

🔹 **Dr. Marcus Chen** 👨‍💼 - Analista Técnico
🔹 **Sarah "The Wolf" Rodriguez** 🐺 - Trader Experiente
🔹 **Prof. Elena Nakamura** 👩‍🏫 - Consultora de Mercado

Posso ajudá-lo com:
• Análises técnicas em tempo real
• Sinais de compra/venda
• Contexto de mercado e tendências
• Estratégias de trading

**Para começar, digite o nome de uma criptomoeda** (ex: Bitcoin, ETH, Solana) ou faça uma pergunta sobre o mercado! 🚀`,
        role: 'assistant',
        agent: state.activeAgent,
        timestamp: new Date()
      };

      addMessage(welcomeMessage);
    }
  }, [state.messages.length, state.activeAgent, addMessage]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || state.isLoading) return;

    const userMessage = inputValue.trim();
    setInputValue('');
    setLoading(true);

    try {
      // Adiciona mensagem do usuário
      const userMsg: Message = {
        id: Date.now().toString(),
        content: userMessage,
        role: 'user',
        timestamp: new Date()
      };

      addMessage(userMsg);

      // Processa mensagem com IA
      const response = await AIService.processMessage(userMessage, state.activeAgent);

      addMessage(response);

      // Atualiza contexto se necessário
      if (response.cryptoData) {
        setCurrentCrypto(response.cryptoData.id);
      }
      if (response.technicalAnalysis) {
        setLastAnalysis(new Date());
      }
    } catch (error) {
      console.error('Erro ao processar mensagem:', error);

      const errorMsg: Message = {
        id: Date.now().toString(),
        content: 'Desculpe, ocorreu um erro ao processar sua mensagem. Tente novamente.',
        role: 'assistant',
        agent: state.activeAgent,
        timestamp: new Date()
      };

      addMessage(errorMsg);
    } finally {
      setLoading(false);
      inputRef.current?.focus();
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleAgentChange = (newAgent: AgentType) => {
    setActiveAgent(newAgent);
    AIService.switchAgent(newAgent);

    // Adiciona mensagem informando sobre a troca
    const switchMessage: Message = {
      id: Date.now().toString(),
      content: `Agente alterado para **${getAgentName(newAgent)}** ${getAgentEmoji(newAgent)}`,
      role: 'assistant',
      agent: newAgent,
      timestamp: new Date()
    };

    addMessage(switchMessage);
  };

  const getAgentName = (agent: AgentType): string => {
    const names = {
      'technical_analyst': 'Dr. Marcus Chen',
      'experienced_trader': 'Sarah "The Wolf" Rodriguez',
      'market_consultant': 'Prof. Elena Nakamura'
    };
    return names[agent];
  };

  const getAgentEmoji = (agent: AgentType): string => {
    const emojis = {
      'technical_analyst': '👨‍💼',
      'experienced_trader': '🐺',
      'market_consultant': '👩‍🏫'
    };
    return emojis[agent];
  };

  return (
    <div className="flex flex-col h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <div className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">CryptoAgent AI</h1>
              <p className="text-sm text-purple-300">
                Agente ativo: {getAgentName(state.activeAgent)} {getAgentEmoji(state.activeAgent)}
              </p>
            </div>
          </div>
          
          <AgentSelector
            activeAgent={state.activeAgent}
            onAgentChange={handleAgentChange}
          />
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-4xl mx-auto space-y-4">
          {state.messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))}

          {state.isLoading && <TypingIndicator agent={state.activeAgent} />}
          
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input */}
      <div className="bg-black/20 backdrop-blur-sm border-t border-purple-500/20 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center space-x-3">
            <div className="flex-1 relative">
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Digite o nome de uma criptomoeda ou faça uma pergunta..."
                className="w-full bg-white/10 border border-purple-500/30 rounded-lg px-4 py-3 text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                disabled={state.isLoading}
              />
            </div>
            
            <button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              className={cn(
                "p-3 rounded-lg transition-all duration-200",
                inputValue.trim() && !isLoading
                  ? "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white shadow-lg"
                  : "bg-gray-600 text-gray-400 cursor-not-allowed"
              )}
            >
              <Send className="w-5 h-5" />
            </button>
          </div>
          
          <div className="mt-2 text-xs text-purple-300 text-center">
            Pressione Enter para enviar • Shift+Enter para nova linha
          </div>
        </div>
      </div>
    </div>
  );
}
