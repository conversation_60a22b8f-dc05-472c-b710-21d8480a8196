'use client';

import React from 'react';
import { TrendingUp, TrendingDown, Activity, Target, Shield, Clock } from 'lucide-react';
import { CryptoData, TechnicalIndicators, TradingSignal } from '@/types';
import { cn, formatCurrency, formatPercentage, formatNumber } from '@/lib/utils';

interface CryptoDataCardProps {
  cryptoData: CryptoData;
  technicalAnalysis: TechnicalIndicators;
  tradingSignal: TradingSignal;
}

export function CryptoDataCard({ 
  cryptoData, 
  technicalAnalysis, 
  tradingSignal 
}: CryptoDataCardProps) {
  const isPositive = cryptoData.price_change_percentage_24h >= 0;
  
  const getSignalColor = (signal: TradingSignal) => {
    switch (signal.type) {
      case 'BUY':
        return 'from-green-500 to-emerald-500';
      case 'SELL':
        return 'from-red-500 to-rose-500';
      default:
        return 'from-yellow-500 to-orange-500';
    }
  };

  const getSignalIcon = (signal: TradingSignal) => {
    switch (signal.type) {
      case 'BUY':
        return TrendingUp;
      case 'SELL':
        return TrendingDown;
      default:
        return Activity;
    }
  };

  const getStrengthColor = (strength: string) => {
    switch (strength) {
      case 'STRONG':
        return 'text-green-400';
      case 'MODERATE':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  const SignalIcon = getSignalIcon(tradingSignal);

  return (
    <div className="bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-lg p-4 space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
            <span className="text-xs font-bold text-white">
              {cryptoData.symbol.toUpperCase()}
            </span>
          </div>
          <div>
            <h3 className="font-semibold text-white">{cryptoData.name}</h3>
            <p className="text-xs text-gray-400">#{cryptoData.market_cap_rank}</p>
          </div>
        </div>
        
        <div className="text-right">
          <div className="text-lg font-bold text-white">
            {formatCurrency(cryptoData.current_price)}
          </div>
          <div className={cn(
            "text-sm font-medium flex items-center",
            isPositive ? "text-green-400" : "text-red-400"
          )}>
            {isPositive ? <TrendingUp className="w-3 h-3 mr-1" /> : <TrendingDown className="w-3 h-3 mr-1" />}
            {formatPercentage(cryptoData.price_change_percentage_24h)}
          </div>
        </div>
      </div>

      {/* Market Data */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-xs text-gray-400">Market Cap</span>
            <span className="text-xs text-white">{formatNumber(cryptoData.market_cap)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-xs text-gray-400">Volume 24h</span>
            <span className="text-xs text-white">{formatNumber(cryptoData.total_volume)}</span>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-xs text-gray-400">High 24h</span>
            <span className="text-xs text-white">{formatCurrency(cryptoData.high_24h)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-xs text-gray-400">Low 24h</span>
            <span className="text-xs text-white">{formatCurrency(cryptoData.low_24h)}</span>
          </div>
        </div>
      </div>

      {/* Technical Indicators */}
      <div className="border-t border-slate-700/50 pt-3">
        <h4 className="text-sm font-medium text-white mb-2">Indicadores Técnicos</h4>
        <div className="grid grid-cols-3 gap-3">
          <div className="text-center">
            <div className="text-xs text-gray-400">RSI</div>
            <div className={cn(
              "text-sm font-medium",
              technicalAnalysis.rsi < 30 ? "text-green-400" :
              technicalAnalysis.rsi > 70 ? "text-red-400" : "text-yellow-400"
            )}>
              {technicalAnalysis.rsi.toFixed(1)}
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-xs text-gray-400">MACD</div>
            <div className={cn(
              "text-sm font-medium",
              technicalAnalysis.macd.macd > technicalAnalysis.macd.signal ? "text-green-400" : "text-red-400"
            )}>
              {technicalAnalysis.macd.macd > technicalAnalysis.macd.signal ? "Bullish" : "Bearish"}
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-xs text-gray-400">SMA20</div>
            <div className={cn(
              "text-sm font-medium",
              cryptoData.current_price > technicalAnalysis.sma.sma20 ? "text-green-400" : "text-red-400"
            )}>
              {formatCurrency(technicalAnalysis.sma.sma20)}
            </div>
          </div>
        </div>
      </div>

      {/* Trading Signal */}
      <div className="border-t border-slate-700/50 pt-3">
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-sm font-medium text-white">Sinal de Trading</h4>
          <div className="flex items-center space-x-1">
            <Clock className="w-3 h-3 text-gray-400" />
            <span className="text-xs text-gray-400">{tradingSignal.timeframe}</span>
          </div>
        </div>
        
        <div className={cn(
          "bg-gradient-to-r rounded-lg p-3",
          getSignalColor(tradingSignal)
        )}>
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <SignalIcon className="w-5 h-5 text-white" />
              <span className="font-bold text-white">{tradingSignal.type}</span>
              <span className={cn("text-sm", getStrengthColor(tradingSignal.strength))}>
                {tradingSignal.strength}
              </span>
            </div>
            <div className="text-white font-bold">
              {tradingSignal.confidence}%
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-3 text-white">
            <div className="flex items-center space-x-1">
              <Target className="w-3 h-3" />
              <span className="text-xs">Target:</span>
              <span className="text-xs font-medium">
                {formatCurrency(tradingSignal.price_target)}
              </span>
            </div>
            
            <div className="flex items-center space-x-1">
              <Shield className="w-3 h-3" />
              <span className="text-xs">Stop:</span>
              <span className="text-xs font-medium">
                {formatCurrency(tradingSignal.stop_loss)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Support & Resistance */}
      <div className="border-t border-slate-700/50 pt-3">
        <h4 className="text-sm font-medium text-white mb-2">Suporte & Resistência</h4>
        <div className="grid grid-cols-2 gap-3">
          <div>
            <div className="text-xs text-gray-400 mb-1">Suporte</div>
            <div className="space-y-1">
              {technicalAnalysis.support_resistance.support.slice(0, 2).map((level, index) => (
                <div key={index} className="text-xs text-green-400">
                  {formatCurrency(level)}
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <div className="text-xs text-gray-400 mb-1">Resistência</div>
            <div className="space-y-1">
              {technicalAnalysis.support_resistance.resistance.slice(0, 2).map((level, index) => (
                <div key={index} className="text-xs text-red-400">
                  {formatCurrency(level)}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
