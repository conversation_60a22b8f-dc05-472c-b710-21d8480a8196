'use client';

import React, { useState } from 'react';
import { ChevronDown, BarChart3, TrendingUp, GraduationCap } from 'lucide-react';
import { AgentType } from '@/types';
import { cn } from '@/lib/utils';

interface AgentSelectorProps {
  activeAgent: AgentType;
  onAgentChange: (agent: AgentType) => void;
}

export function AgentSelector({ activeAgent, onAgentChange }: AgentSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  const agents = [
    {
      id: 'technical_analyst' as AgentType,
      name: 'Dr. <PERSON>',
      title: 'Analista Técnico',
      description: 'Especialista em indicadores e análise técnica',
      icon: BarChart3,
      emoji: '👨‍💼',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'experienced_trader' as AgentType,
      name: '<PERSON> "The Wolf" Rodriguez',
      title: 'Trader Experiente',
      description: '15 anos de experiência em trading',
      icon: TrendingUp,
      emoji: '🐺',
      color: 'from-green-500 to-emerald-500'
    },
    {
      id: 'market_consultant' as AgentType,
      name: 'Prof. <PERSON>',
      title: 'Consultora de Mercado',
      description: '<PERSON>álise fundamentalista e tendências macro',
      icon: GraduationCap,
      emoji: '👩‍🏫',
      color: 'from-purple-500 to-pink-500'
    }
  ];

  const currentAgent = agents.find(agent => agent.id === activeAgent);

  const handleAgentSelect = (agentId: AgentType) => {
    onAgentChange(agentId);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      {/* Trigger Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-3 bg-white/10 hover:bg-white/20 border border-purple-500/30 rounded-lg px-4 py-2 transition-all duration-200"
      >
        <div className={cn(
          "w-8 h-8 rounded-full flex items-center justify-center bg-gradient-to-r",
          currentAgent?.color
        )}>
          {currentAgent && <currentAgent.icon className="w-4 h-4 text-white" />}
        </div>
        
        <div className="text-left">
          <div className="text-sm font-medium text-white">
            {currentAgent?.name}
          </div>
          <div className="text-xs text-purple-300">
            {currentAgent?.title}
          </div>
        </div>
        
        <ChevronDown className={cn(
          "w-4 h-4 text-purple-300 transition-transform duration-200",
          isOpen && "rotate-180"
        )} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Menu */}
          <div className="absolute right-0 top-full mt-2 w-80 bg-slate-800/95 backdrop-blur-sm border border-purple-500/30 rounded-lg shadow-xl z-20">
            <div className="p-2">
              {agents.map((agent) => (
                <button
                  key={agent.id}
                  onClick={() => handleAgentSelect(agent.id)}
                  className={cn(
                    "w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 text-left",
                    activeAgent === agent.id
                      ? "bg-purple-500/20 border border-purple-500/50"
                      : "hover:bg-white/10"
                  )}
                >
                  <div className={cn(
                    "w-10 h-10 rounded-full flex items-center justify-center bg-gradient-to-r flex-shrink-0",
                    agent.color
                  )}>
                    <agent.icon className="w-5 h-5 text-white" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-white truncate">
                        {agent.name}
                      </span>
                      <span className="text-lg">{agent.emoji}</span>
                    </div>
                    <div className="text-xs text-purple-300 mb-1">
                      {agent.title}
                    </div>
                    <div className="text-xs text-gray-400">
                      {agent.description}
                    </div>
                  </div>
                  
                  {activeAgent === agent.id && (
                    <div className="w-2 h-2 bg-purple-500 rounded-full flex-shrink-0" />
                  )}
                </button>
              ))}
            </div>
            
            <div className="border-t border-purple-500/20 p-3">
              <div className="text-xs text-gray-400 text-center">
                Cada agente tem especialidades únicas para melhor atendê-lo
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
