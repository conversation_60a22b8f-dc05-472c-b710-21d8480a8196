'use client';

import React from 'react';
import { BarChart3, TrendingUp, GraduationCap } from 'lucide-react';
import { AgentType } from '@/types';
import { cn } from '@/lib/utils';

interface TypingIndicatorProps {
  agent: AgentType;
}

export function TypingIndicator({ agent }: TypingIndicatorProps) {
  const getAgentIcon = (agentType: AgentType) => {
    const icons = {
      'technical_analyst': BarChart3,
      'experienced_trader': TrendingUp,
      'market_consultant': GraduationCap
    };
    return icons[agentType];
  };

  const getAgentColor = (agentType: AgentType) => {
    const colors = {
      'technical_analyst': 'from-blue-500 to-cyan-500',
      'experienced_trader': 'from-green-500 to-emerald-500',
      'market_consultant': 'from-purple-500 to-pink-500'
    };
    return colors[agentType];
  };

  const getAgentName = (agentType: AgentType) => {
    const names = {
      'technical_analyst': 'Dr. <PERSON>',
      'experienced_trader': '<PERSON> "The Wolf" <PERSON>',
      'market_consultant': 'Prof<PERSON> <PERSON>'
    };
    return names[agentType];
  };

  const getTypingMessage = (agentType: AgentType) => {
    const messages = {
      'technical_analyst': 'Analisando indicadores técnicos...',
      'experienced_trader': 'Checando o mercado...',
      'market_consultant': 'Consultando dados fundamentais...'
    };
    return messages[agentType];
  };

  const IconComponent = getAgentIcon(agent);

  return (
    <div className="flex items-start space-x-3">
      {/* Avatar */}
      <div className={cn(
        "w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 bg-gradient-to-r",
        getAgentColor(agent)
      )}>
        <IconComponent className="w-5 h-5 text-white" />
      </div>

      {/* Typing Content */}
      <div className="flex-1 max-w-3xl">
        {/* Header */}
        <div className="flex items-center space-x-2 mb-1">
          <span className="text-sm font-medium text-white">
            {getAgentName(agent)}
          </span>
          <span className="text-xs text-gray-400">
            agora
          </span>
        </div>

        {/* Typing Bubble */}
        <div className="bg-slate-800/80 border border-slate-700/50 rounded-lg px-4 py-3">
          <div className="flex items-center space-x-2">
            {/* Typing Animation */}
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
            </div>
            
            <span className="text-sm text-gray-300">
              {getTypingMessage(agent)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
