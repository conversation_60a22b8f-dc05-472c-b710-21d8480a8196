'use client';

import React from 'react';
import { User, BarChart3, TrendingUp, GraduationCap, Copy, ExternalLink } from 'lucide-react';
import { Message, AgentType } from '@/types';
import { cn, formatCurrency, formatPercentage, getTimeAgo } from '@/lib/utils';
import { CryptoDataCard } from './CryptoDataCard';

interface MessageBubbleProps {
  message: Message;
}

export function MessageBubble({ message }: MessageBubbleProps) {
  const isUser = message.role === 'user';
  
  const getAgentIcon = (agent?: AgentType) => {
    if (!agent) return User;
    
    const icons = {
      'technical_analyst': BarChart3,
      'experienced_trader': TrendingUp,
      'market_consultant': GraduationCap
    };
    
    return icons[agent] || User;
  };

  const getAgentColor = (agent?: AgentType) => {
    if (!agent) return 'from-gray-500 to-gray-600';
    
    const colors = {
      'technical_analyst': 'from-blue-500 to-cyan-500',
      'experienced_trader': 'from-green-500 to-emerald-500',
      'market_consultant': 'from-purple-500 to-pink-500'
    };
    
    return colors[agent] || 'from-gray-500 to-gray-600';
  };

  const getAgentName = (agent?: AgentType) => {
    if (!agent) return 'Usuário';
    
    const names = {
      'technical_analyst': 'Dr. Marcus Chen',
      'experienced_trader': 'Sarah "The Wolf" Rodriguez',
      'market_consultant': 'Prof. Elena Nakamura'
    };
    
    return names[agent] || 'Assistente';
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // Aqui você poderia adicionar um toast de sucesso
    } catch (err) {
      console.error('Erro ao copiar texto:', err);
    }
  };

  const formatMessageContent = (content: string) => {
    // Converte markdown básico para HTML
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code class="bg-black/20 px-1 py-0.5 rounded text-sm">$1</code>')
      .replace(/\n/g, '<br />');
  };

  const IconComponent = getAgentIcon(message.agent);

  return (
    <div className={cn(
      "flex items-start space-x-3",
      isUser ? "flex-row-reverse space-x-reverse" : "flex-row"
    )}>
      {/* Avatar */}
      <div className={cn(
        "w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 bg-gradient-to-r",
        isUser ? "from-slate-600 to-slate-700" : getAgentColor(message.agent)
      )}>
        <IconComponent className="w-5 h-5 text-white" />
      </div>

      {/* Message Content */}
      <div className={cn(
        "flex-1 max-w-3xl",
        isUser ? "flex flex-col items-end" : "flex flex-col items-start"
      )}>
        {/* Header */}
        <div className={cn(
          "flex items-center space-x-2 mb-1",
          isUser ? "flex-row-reverse space-x-reverse" : "flex-row"
        )}>
          <span className="text-sm font-medium text-white">
            {isUser ? 'Você' : getAgentName(message.agent)}
          </span>
          <span className="text-xs text-gray-400">
            {getTimeAgo(message.timestamp)}
          </span>
        </div>

        {/* Message Bubble */}
        <div className={cn(
          "rounded-lg px-4 py-3 max-w-full",
          isUser 
            ? "bg-gradient-to-r from-purple-600 to-pink-600 text-white" 
            : "bg-slate-800/80 text-gray-100 border border-slate-700/50"
        )}>
          <div 
            className="prose prose-sm max-w-none prose-invert"
            dangerouslySetInnerHTML={{ 
              __html: formatMessageContent(message.content) 
            }}
          />
          
          {/* Actions */}
          {!isUser && (
            <div className="flex items-center justify-end space-x-2 mt-3 pt-2 border-t border-slate-700/50">
              <button
                onClick={() => copyToClipboard(message.content)}
                className="p-1 text-gray-400 hover:text-white transition-colors duration-200"
                title="Copiar mensagem"
              >
                <Copy className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>

        {/* Crypto Data Card */}
        {message.cryptoData && message.technicalAnalysis && message.tradingSignal && (
          <div className="mt-3 w-full">
            <CryptoDataCard 
              cryptoData={message.cryptoData}
              technicalAnalysis={message.technicalAnalysis}
              tradingSignal={message.tradingSignal}
            />
          </div>
        )}
      </div>
    </div>
  );
}
